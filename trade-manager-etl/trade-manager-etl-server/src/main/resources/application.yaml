server:
  port: 20086
  servlet:
    context-path: /trade/etl

spring:
  application:
    name: trade-manager-etl-server
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://${DATABASE_HOST:*************}:${DATABASE_PORT:32372}/${DATABASE_DB:trade_sms}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PASSWORD:root}
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
      driver-class-name: com.mysql.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 30MB
  batch:
    job:
      enabled: false
    jdbc:
      table-prefix: etl_


logging:
  config: classpath:logback.xml

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/*.xml
  typeAliasesPackage: com.bobandata.cloud.**.dataobject
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
  #    sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
  #    meta-object-handler: com.bobandata.acquarium.core.server.handler.BaseEntityHandler
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

ribbon:
  ReadTimeout: 120000 # 请求处理的超时时间
