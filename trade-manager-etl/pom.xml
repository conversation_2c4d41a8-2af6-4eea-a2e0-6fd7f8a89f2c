<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bobandata.cloud</groupId>
        <artifactId>trade-manager-develop</artifactId>
        <version>${re.version}</version>
    </parent>

    <artifactId>trade-manager-etl</artifactId>
    <packaging>pom</packaging>
    <name>trade : manager :: etl</name>
    <modules>
        <module>trade-manager-etl-server</module>
    </modules>

</project>