stages:
  - image
  - deploy
  - push

variables:
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://*************:2375


Build Image:
  stage: image
  image: docker-register.bobandata.com/devops/auto-devops:latest
  tags:
    - "auto"
  only:
    - master
    - merge_requests
  script:
    - autodevops build
  when: on_success

Deploy To Kubernetes: # 部署测试环境
  stage: deploy
  image: docker-register.bobandata.com/devops/auto-devops:latest
  tags:
    - "auto"
  only:
    - master
    - merge_requests
  script:
    - autodevops deploy
  when: on_success

Push Helm Chart:
  stage: push
  image: docker-register.bobandata.com/devops/auto-devops:dev
  tags:
    - "auto"
  only:
    - master
    - merge_requests
  script:
    - autodevops -v debug push
  when: on_success