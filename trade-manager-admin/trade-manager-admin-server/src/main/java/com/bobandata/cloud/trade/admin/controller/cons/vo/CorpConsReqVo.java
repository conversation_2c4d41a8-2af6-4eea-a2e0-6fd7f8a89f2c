package com.bobandata.cloud.trade.admin.controller.cons.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18日 09:17
 * @description
 */
@Schema(description = "客户信息管理-客户管理 分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CorpConsReqVo extends PageParam {

    @Schema(description = "用户户号", example = "43012334")
    private String consNo;

    @Schema(description = "用户户名", example = "xxx公司")
    private String caption;

    @Schema(description = "用户地址信息", example = "xxx公司")
    private String addr;

    @Schema(description = "户名或户号")
    private String key;

    @Schema(description = "电压等级")
    private String level;

    @Schema(description = "所属地市")
    private String city;

    @Schema(description = "用电类别")
    private String elecTypeBigcls;
}
