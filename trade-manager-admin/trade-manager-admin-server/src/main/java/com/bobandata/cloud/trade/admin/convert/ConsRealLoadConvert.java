package com.bobandata.cloud.trade.admin.convert;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.admin.controller.curve.vo.ConsLoadRespVo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-21日 10:39
 * @description
 */
public class ConsRealLoadConvert {

    public static final ConsRealLoadConvert INSTANCE = new ConsRealLoadConvert();

    public void setRealLoadCount(ConsLoadRespVo loadRespVo, List<CorpConsRealLoadDo> corpConsRealLoadDos) {
        DoubleSummaryStatistics doubleStatistics = new DoubleSummaryStatistics();
        corpConsRealLoadDos.stream()
                           .flatMap(corpConsRealLoadDo -> corpConsRealLoadDo.checkDataCurve()
                                                                            .getDataCurve()
                                                                            .values()
                                                                            .stream())
                           .filter(Objects::nonNull)
                           .mapToDouble(BigDecimal::doubleValue)
                           .forEach(doubleStatistics);
        loadRespVo.setMaxLoad(new BigDecimal(String.valueOf(doubleStatistics.getMax())));
        loadRespVo.setMinLoad(new BigDecimal(String.valueOf(doubleStatistics.getMin())));
        loadRespVo.setMeanLoad(
                new BigDecimal(String.valueOf(doubleStatistics.getAverage())).setScale(3, RoundingMode.HALF_UP));
        List<KeyValueVo> keyValueVos = corpConsRealLoadDos.stream()
                                                          .map(realDo -> {
                                                              TwentyFourMapDataCurve dataCurve = realDo.checkDataCurve();
                                                              Date dataDate = realDo.getDataDate();
                                                              return dataCurve.convertKeyValue(dataDate);
                                                          }).collect(Collectors.toList());
        loadRespVo.setRealLoadCurves(keyValueVos);
    }
}
