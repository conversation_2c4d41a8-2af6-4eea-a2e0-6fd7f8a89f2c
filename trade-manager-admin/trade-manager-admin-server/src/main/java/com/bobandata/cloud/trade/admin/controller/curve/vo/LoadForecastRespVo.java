package com.bobandata.cloud.trade.admin.controller.curve.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-20日 13:57
 * @description
 */
@Schema(description = "客户信息管理-负荷预测及分析 负荷预测对比")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LoadForecastRespVo {

    @Schema(description = "预测准确率")
    private BigDecimal accuracy;

    @Schema(description = "实际负荷曲线")
    private KeyValueVo realCurve;

    @Schema(description = "预测负荷曲线")
    private KeyValueVo forecastCurve;
}
