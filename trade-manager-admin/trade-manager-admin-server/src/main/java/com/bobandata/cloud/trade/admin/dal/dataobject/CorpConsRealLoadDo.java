package com.bobandata.cloud.trade.admin.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve;
import com.bobandata.cloud.trade.curve.core.util.DataCurveUtil;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.Data;

/**
 * @TableName corp_cons_real_load
 */
@TableName(value = "corp_cons_real_load")
@Data
public class CorpConsRealLoadDo extends BaseDo {

    /**
     *
     */
    @TableField(value = "cons_id")
    private Long consId;

    /**
     *
     */
    @TableField(value = "data_date")
    private Date dataDate;

    /**
     *
     */
    @TableField(value = "T_PQ1")
    private BigDecimal tPq1;

    /**
     *
     */
    @TableField(value = "T_PQ2")
    private BigDecimal tPq2;

    /**
     *
     */
    @TableField(value = "T_PQ3")
    private BigDecimal tPq3;

    /**
     *
     */
    @TableField(value = "T_PQ4")
    private BigDecimal tPq4;

    /**
     *
     */
    @TableField(value = "T_PQ5")
    private BigDecimal tPq5;

    /**
     *
     */
    @TableField(value = "T_PQ6")
    private BigDecimal tPq6;

    /**
     *
     */
    @TableField(value = "T_PQ7")
    private BigDecimal tPq7;

    /**
     *
     */
    @TableField(value = "T_PQ8")
    private BigDecimal tPq8;

    /**
     *
     */
    @TableField(value = "T_PQ9")
    private BigDecimal tPq9;

    /**
     *
     */
    @TableField(value = "T_PQ10")
    private BigDecimal tPq10;

    /**
     *
     */
    @TableField(value = "T_PQ11")
    private BigDecimal tPq11;

    /**
     *
     */
    @TableField(value = "T_PQ12")
    private BigDecimal tPq12;

    /**
     *
     */
    @TableField(value = "T_PQ13")
    private BigDecimal tPq13;

    /**
     *
     */
    @TableField(value = "T_PQ14")
    private BigDecimal tPq14;

    /**
     *
     */
    @TableField(value = "T_PQ15")
    private BigDecimal tPq15;

    /**
     *
     */
    @TableField(value = "T_PQ16")
    private BigDecimal tPq16;

    /**
     *
     */
    @TableField(value = "T_PQ17")
    private BigDecimal tPq17;

    /**
     *
     */
    @TableField(value = "T_PQ18")
    private BigDecimal tPq18;

    /**
     *
     */
    @TableField(value = "T_PQ19")
    private BigDecimal tPq19;

    /**
     *
     */
    @TableField(value = "T_PQ20")
    private BigDecimal tPq20;

    /**
     *
     */
    @TableField(value = "T_PQ21")
    private BigDecimal tPq21;

    /**
     *
     */
    @TableField(value = "T_PQ22")
    private BigDecimal tPq22;

    /**
     *
     */
    @TableField(value = "T_PQ23")
    private BigDecimal tPq23;

    /**
     *
     */
    @TableField(value = "T_PQ24")
    private BigDecimal tPq24;

    public TwentyFourMapDataCurve checkDataCurve() {
        return DataCurveUtil.convertTwentyFour(this, "tPq");
    }
}