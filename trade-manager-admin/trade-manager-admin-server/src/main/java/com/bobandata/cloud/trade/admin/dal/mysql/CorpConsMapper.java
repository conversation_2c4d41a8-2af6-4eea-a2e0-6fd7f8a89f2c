package com.bobandata.cloud.trade.admin.dal.mysql;

import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons(售电公司: 用电客户档案信息)】的数据库操作Mapper
 * @createDate 2024-03-18 09:06:15
 * @Entity com.bobandata.cloud.trade.ml.term.domain.CorpCons
 */
public interface CorpConsMapper extends BaseCrudMapper<CorpConsDo> {

    @Select("select distinct trim(level) level from corp_cons where level!= '' and level is not null")
    List<Map> getLevel();

    @Select("select distinct trim(city) city from corp_cons where city!='' and city is not null")
    List<Map> getCity();

    @Select("select distinct trim(elec_type_bigcls) elecTypeBigcls from corp_cons where elec_type_bigcls !='' and elec_type_bigcls is not null")
    List<Map> getYd();
}




