package com.bobandata.cloud.trade.admin.convert;

import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsCreateReqVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsDetailRespVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsRespVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsUpdateReqVo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-19日 16:07
 * @description
 */
@Mapper
public interface CorpConsConvert {

    CorpConsConvert INSTANCE = Mappers.getMapper(CorpConsConvert.class);

    List<CorpConsRespVo> convertList(List<CorpConsDo> list);

    PagingResult<CorpConsRespVo> convertPage(PagingResult<CorpConsDo> page);

    CorpConsDetailRespVo convertDetail(CorpConsDo corpConsDo);

    CorpConsDo convert(CorpConsCreateReqVo reqVo);

    CorpConsDo convert(CorpConsUpdateReqVo reqVo);
}
