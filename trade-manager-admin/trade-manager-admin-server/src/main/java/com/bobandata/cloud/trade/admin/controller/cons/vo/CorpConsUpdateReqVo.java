package com.bobandata.cloud.trade.admin.controller.cons.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/5/27 16:32
 * @Classname CorpConsUpdateReqVo
 * @Description
 */
@Schema(description = "客户信息管理-客户管理修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CorpConsUpdateReqVo extends CorpConsBaseVo {

    @Schema(description = "客户序号", required = true, example = "1024")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 居间人
     */
    @Schema(description = "居间人")
    private String intermediaryName;


    /**
     * 居间方式
     */
    @Schema(description = "居间方式")
    private String inMethod;


    /**
     * 居间人电话
     */
    @Schema(description = "居间人电话")
    private String inPhone;

}