package com.bobandata.cloud.trade.admin.convert;

import com.bobandata.cloud.trade.admin.controller.curve.vo.ConsLoadRespVo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024-03-21日 10:57
 * @description
 */
public class ConsRealEnergyConvert {

    public static final ConsRealEnergyConvert INSTANCE = new ConsRealEnergyConvert();

    public void setRealEnergyCount(ConsLoadRespVo loadRespVo, List<CorpConsRealEnergyDo> energyDos) {
        BigDecimal avgHighestEnergy = this.getAvgEnergy(energyDos, CorpConsRealEnergyDo::getEnergy1);
        loadRespVo.setMeanHighestEnergy(avgHighestEnergy);
        BigDecimal avgPeakEnergy = this.getAvgEnergy(energyDos, CorpConsRealEnergyDo::getEnergy2);
        loadRespVo.setMeanPeakEnergy(avgPeakEnergy);
        BigDecimal avgFlatEnergy = this.getAvgEnergy(energyDos, CorpConsRealEnergyDo::getEnergy3);
        loadRespVo.setMeanFlatEnergy(avgFlatEnergy);
        BigDecimal avgValleyestEnergy = this.getAvgEnergy(energyDos, CorpConsRealEnergyDo::getEnergy4);
        loadRespVo.setMeanValleyEnergy(avgValleyestEnergy);
        BigDecimal avgTotalEnergy = this.getAvgEnergy(energyDos, CorpConsRealEnergyDo::getTotalBattery);
        loadRespVo.setMeanEnergy(avgTotalEnergy);
    }

    public BigDecimal getAvgEnergy(List<CorpConsRealEnergyDo> energyDos,
                                   Function<CorpConsRealEnergyDo, BigDecimal> getterFunction) {
        double asDouble = energyDos.stream()
                                   .map(getterFunction)
                                   .filter(Objects::nonNull)
                                   .mapToDouble(BigDecimal::doubleValue)
                                   .average()
                                   .getAsDouble();
        return new BigDecimal(String.valueOf(asDouble));
    }
}
