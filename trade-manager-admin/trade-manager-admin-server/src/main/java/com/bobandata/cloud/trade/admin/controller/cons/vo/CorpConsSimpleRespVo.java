package com.bobandata.cloud.trade.admin.controller.cons.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-03-19日 16:04
 * @description
 */
@Schema(description = "客户信息管理-客户管理 用户列表信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CorpConsSimpleRespVo {

    @Schema(description = "Id", example = "43012334")
    private Long id;

    /**
     * 用户户号
     */
    @Schema(description = "用户户号", example = "43012334")
    private String consNo;

    /**
     * 用户户名
     */
    @Schema(description = "用户户名")
    private String caption;

    /**
     * 电压等级
     */
    @Schema(description = "电压等级")
    private String voltCode;

    /**
     * 用电行业
     */
    @Schema(description = "用电行业")
    private String industTypeDsc;


    /**
     * 用电行业
     */
    @Schema(description = "用电类别")
    private String elecTypeBigcls;

    /**
     * 用电地址:用电客户的用电地址
     */
    @Schema(description = "用电地址")
    private String elecAddr;

    /**
     * 合同容量:合同约定的本用户的容量
     */
    @Schema(description = "合同容量")
    private BigDecimal contractCap;

    /**
     * 运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量
     */
    @Schema(description = "运行容量")
    private BigDecimal runCap;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactPhone;

    /**
     * 用户类别
     */
    @Schema(description = "用户类别")
    private String userType;

}
