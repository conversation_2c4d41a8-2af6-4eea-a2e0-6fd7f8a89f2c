package com.bobandata.cloud.trade.admin.service.curve;

import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealLoadMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_real_load】的数据库操作Service
 * @createDate 2024-03-20 15:27:52
 */
public interface CorpConsRealLoadService extends BaseService<CorpConsRealLoadMapper, CorpConsRealLoadDo> {

    List<CorpConsRealLoadDo> listCurves(Long consId, Date startDate, Date endDate);
}
