package com.bobandata.cloud.trade.admin.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @TableName corp_cons_real_energy
 */
@TableName(value = "corp_cons_real_energy")
@Data
public class CorpConsRealEnergyDo extends BaseDo {

    /**
     * 用户id
     */
    @TableField(value = "cons_id")
    private Long consId;

    /**
     * 用户号
     */
    @TableField(value = "cons_no")
    private String consNo;

    /**
     * 日期
     */
    @TableField(value = "data_date")
    private Date dataDate;

    /**
     * 尖峰
     */
    @TableField(value = "energy1")
    private BigDecimal energy1;

    /**
     * 高峰
     */
    @TableField(value = "energy2")
    private BigDecimal energy2;

    /**
     * 平段
     */
    @TableField(value = "energy3")
    private BigDecimal energy3;

    /**
     * 谷段
     */
    @TableField(value = "energy4")
    private BigDecimal energy4;

    /**
     * 总计
     */
    @TableField(value = "total_battery")
    private BigDecimal totalBattery;

}