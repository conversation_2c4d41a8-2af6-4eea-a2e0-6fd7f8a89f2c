package com.bobandata.cloud.trade.admin.controller;

import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.admin.controller.vo.HomePageVo;
import com.bobandata.cloud.trade.admin.convert.HomePageConvert;
import com.bobandata.cloud.trade.admin.service.curve.CorpConsRealEnergyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-03-22日 09:50
 * @description
 */
@Tag(name = "首页")
@Slf4j
@RequestMapping("/home")
@RestController
public class HomePageController {

    @Autowired
    private CorpConsRealEnergyService realEnergyService;

    @Operation(summary = "首页")
    @GetMapping("/get")
    public ServiceResult<HomePageVo> homePage() {
        List<KeyValue> realEnergySum = realEnergyService.getRealEnergySum();
        List<KeyValue<String, ?>> contractSum = realEnergyService.getContractSum();
        List<KeyValue> energyOrders = realEnergyService.listConsEnergyOrder();
        return ServiceResult.success(HomePageConvert.INSTANCE.convertVo(energyOrders, realEnergySum, contractSum));
    }
}
