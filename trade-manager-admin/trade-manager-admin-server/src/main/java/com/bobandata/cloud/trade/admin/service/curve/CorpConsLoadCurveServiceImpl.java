package com.bobandata.cloud.trade.admin.service.curve;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsLoadCurveDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsLoadCurveMapper;
import com.bobandata.cloud.trade.mvc.core.query.QueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import java.sql.Date;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_load_curve(用户相关负荷曲线定义)】的数据库操作Service实现
 * @createDate 2024-03-20 10:15:40
 */
@Service
public class CorpConsLoadCurveServiceImpl extends BaseServiceImpl<CorpConsLoadCurveMapper, CorpConsLoadCurveDo> implements CorpConsLoadCurveService {

    @Override
    public List<CorpConsLoadCurveDo> listLoadCurves(Long consId, Date startDate, Date endDate, final Integer... types) {
        QueryWrapper<CorpConsLoadCurveDo> wrapper = new QueryWrapperX<CorpConsLoadCurveDo>()
                .ge("start_date", startDate)
                .le("end_date", endDate)
                .in("data_type", types)
                .eq("cons_id", consId);
        return this.list(wrapper);
    }
}




