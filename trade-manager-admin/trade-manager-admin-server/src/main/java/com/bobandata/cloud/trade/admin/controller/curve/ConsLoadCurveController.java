package com.bobandata.cloud.trade.admin.controller.curve;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.admin.controller.curve.vo.ConsLoadReqVo;
import com.bobandata.cloud.trade.admin.controller.curve.vo.ConsLoadRespVo;
import com.bobandata.cloud.trade.admin.controller.curve.vo.LoadForecastReqVo;
import com.bobandata.cloud.trade.admin.controller.curve.vo.LoadForecastRespVo;
import com.bobandata.cloud.trade.admin.convert.ConsLoadCurveConvert;
import com.bobandata.cloud.trade.admin.convert.ConsRealEnergyConvert;
import com.bobandata.cloud.trade.admin.convert.ConsRealLoadConvert;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsLoadCurveDo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo;
import com.bobandata.cloud.trade.admin.service.CorpConsService;
import com.bobandata.cloud.trade.admin.service.curve.CorpConsLoadCurveService;
import com.bobandata.cloud.trade.admin.service.curve.CorpConsRealEnergyService;
import com.bobandata.cloud.trade.admin.service.curve.CorpConsRealLoadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.sql.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-03-20日 13:53
 * @description
 */
@Tag(name = "客户信息管理-负荷预测及分析")
@Slf4j
@RequestMapping("/curve")
@RestController
public class ConsLoadCurveController {

    @Autowired
    private CorpConsService corpConsService;

    @Autowired
    private CorpConsRealLoadService realLoadService;

    @Autowired
    private CorpConsRealEnergyService energyService;

    @Autowired
    private CorpConsLoadCurveService loadCurveService;

    @Operation(summary = "获取用户负荷数据")
    @GetMapping("/load")
    public ServiceResult<ConsLoadRespVo> getLoadSpecial(@Valid ConsLoadReqVo consLoadReqVo) {
        Long consId = consLoadReqVo.getConsId();
        Date startDate = consLoadReqVo.getStartDate();
        Date endDate = consLoadReqVo.getEndDate();
        CorpConsDo corpConsDo = corpConsService.getById(consLoadReqVo.getConsId());
        ConsLoadRespVo respVo = new ConsLoadRespVo().setConsNo(corpConsDo.getConsNo())
                                                    .setConsName(corpConsDo.getCaption());
        // 临时凑数
        respVo.setMaxLoadDate(startDate);
        respVo.setMinLoadDate(endDate);
        List<CorpConsRealLoadDo> corpConsRealLoadDos = realLoadService.listCurves(consId, startDate, endDate);
        ConsRealLoadConvert.INSTANCE.setRealLoadCount(respVo, corpConsRealLoadDos);
        List<CorpConsRealEnergyDo> corpConsRealEnergyDos = energyService.listRealEnergies(consId, startDate, endDate);
        ConsRealEnergyConvert.INSTANCE.setRealEnergyCount(respVo, corpConsRealEnergyDos);
        List<CorpConsLoadCurveDo> corpConsLoadCurveDos = loadCurveService.listLoadCurves(
                consId, startDate, endDate, 1, 2);
        ConsLoadCurveConvert.INSTANCE.setLoadCurves(respVo, corpConsLoadCurveDos);
        return ServiceResult.success(respVo);
    }

    @Operation(summary = "获取用户预测数据")
    @GetMapping("/forecast")
    public ServiceResult<LoadForecastRespVo> getLoadForecast(LoadForecastReqVo loadForecastReqVo) {
        Long consId = loadForecastReqVo.getConsId();
        Date forecastDate = loadForecastReqVo.getForecastDate();
        List<CorpConsLoadCurveDo> corpConsLoadCurveDos = loadCurveService.listLoadCurves(
                consId, forecastDate, forecastDate, 3);
        List<CorpConsRealLoadDo> corpConsRealLoadDos = realLoadService.listCurves(consId, forecastDate, forecastDate);
        LoadForecastRespVo respVo = ConsLoadCurveConvert.INSTANCE.convertForecastVo(
                corpConsLoadCurveDos, corpConsRealLoadDos);
        return ServiceResult.success(respVo);
    }
}
