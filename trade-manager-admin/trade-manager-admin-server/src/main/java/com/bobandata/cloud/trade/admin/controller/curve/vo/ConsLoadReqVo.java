package com.bobandata.cloud.trade.admin.controller.curve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.sql.Date;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-20日 13:53
 * @description
 */
@Schema(description = "客户信息管理-负荷预测及分析 负荷特性")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ConsLoadReqVo {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long consId;

    @Schema(description = "曲线开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    @Schema(description = "曲线结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endDate;
}
