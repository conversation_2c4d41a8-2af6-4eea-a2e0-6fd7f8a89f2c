package com.bobandata.cloud.trade.admin.service.curve;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealLoadMapper;
import com.bobandata.cloud.trade.mvc.core.query.QueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import java.sql.Date;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_real_load】的数据库操作Service实现
 * @createDate 2024-03-20 15:27:52
 */
@Service
public class CorpConsRealLoadServiceImpl extends BaseServiceImpl<CorpConsRealLoadMapper, CorpConsRealLoadDo> implements CorpConsRealLoadService {

    /**
     * 获取负荷相关统计信息
     *
     * @return
     */
    @Override
    public List<CorpConsRealLoadDo> listCurves(Long consId, Date startDate, Date endDate) {
        QueryWrapper<CorpConsRealLoadDo> wrapper = new QueryWrapperX<CorpConsRealLoadDo>()
                .between("data_date", startDate, endDate)
                .eq("cons_id", consId);
        return this.list(wrapper);
    }
}




