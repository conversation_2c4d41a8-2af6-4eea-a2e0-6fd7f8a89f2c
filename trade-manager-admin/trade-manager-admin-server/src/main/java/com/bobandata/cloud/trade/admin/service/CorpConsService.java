package com.bobandata.cloud.trade.admin.service;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsCreateReqVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsReqVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsUpdateReqVo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons(售电公司: 用电客户档案信息)】的数据库操作Service
 * @createDate 2024-03-18 09:06:15
 */
public interface CorpConsService extends BaseService<CorpConsMapper, CorpConsDo> {

    PagingResult<CorpConsDo> listByPage(CorpConsReqVo reqVo);

    ServiceResult<Boolean> createCorpCons(CorpConsCreateReqVo reqVo);

    void deleteCorpCons(Long id);

    void updateCorpCons(CorpConsUpdateReqVo reqVo);

    List<Map> getLevel();

    List<Map> getCity();

    List<Map> getYd();
}
