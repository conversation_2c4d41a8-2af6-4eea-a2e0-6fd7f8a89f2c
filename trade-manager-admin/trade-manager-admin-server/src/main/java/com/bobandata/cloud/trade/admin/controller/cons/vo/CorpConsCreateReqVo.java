package com.bobandata.cloud.trade.admin.controller.cons.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/5/27 10:50
 * @Classname CorpConsCreateReqVo
 * @Description
 */
@Schema(description = "客户信息管理-客户管理创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CorpConsCreateReqVo extends CorpConsBaseVo{

    /**
     * 居间人
     */
    @Schema(description = "居间人")
    private String intermediaryName;


    /**
     * 居间方式
     */
    @Schema(description = "居间方式")
    private String inMethod;


    /**
     * 居间人电话
     */
    @Schema(description = "居间人电话")
    private String inPhone;


}
