package com.bobandata.cloud.trade.admin.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve;
import com.bobandata.cloud.trade.curve.core.util.DataCurveUtil;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.Data;

/**
 * 用户相关负荷曲线定义
 *
 * @TableName corp_cons_load_curve
 */
@TableName(value = "corp_cons_load_curve")
@Data
public class CorpConsLoadCurveDo extends BaseDo {

    /**
     * 负荷曲线所属用户
     */
    @TableField(value = "cons_id")
    private Long consId;

    /**
     * 所属区域
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     * 负荷曲线所属年份
     */
    @TableField(value = "date_year")
    private Integer dateYear;

    /**
     * 负荷曲线所属月份
     */
    @TableField(value = "date_month")
    private Integer dateMonth;

    /**
     * 曲线数据类型
     */
    @TableField(value = "data_type")
    private Integer dataType;

    /**
     * 生效时间
     */
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 失效时间
     */
    @TableField(value = "end_date")
    private Date endDate;

    /**
     *
     */
    @TableField(value = "value1")
    private BigDecimal value1;

    /**
     *
     */
    @TableField(value = "value2")
    private BigDecimal value2;

    /**
     *
     */
    @TableField(value = "value3")
    private BigDecimal value3;

    /**
     *
     */
    @TableField(value = "value4")
    private BigDecimal value4;

    /**
     *
     */
    @TableField(value = "value5")
    private BigDecimal value5;

    /**
     *
     */
    @TableField(value = "value6")
    private BigDecimal value6;

    /**
     *
     */
    @TableField(value = "value7")
    private BigDecimal value7;

    /**
     *
     */
    @TableField(value = "value8")
    private BigDecimal value8;

    /**
     *
     */
    @TableField(value = "value9")
    private BigDecimal value9;

    /**
     *
     */
    @TableField(value = "value10")
    private BigDecimal value10;

    /**
     *
     */
    @TableField(value = "value11")
    private BigDecimal value11;

    /**
     *
     */
    @TableField(value = "value12")
    private BigDecimal value12;

    /**
     *
     */
    @TableField(value = "value13")
    private BigDecimal value13;

    /**
     *
     */
    @TableField(value = "value14")
    private BigDecimal value14;

    /**
     *
     */
    @TableField(value = "value15")
    private BigDecimal value15;

    /**
     *
     */
    @TableField(value = "value16")
    private BigDecimal value16;

    /**
     *
     */
    @TableField(value = "value17")
    private BigDecimal value17;

    /**
     *
     */
    @TableField(value = "value18")
    private BigDecimal value18;

    /**
     *
     */
    @TableField(value = "value19")
    private BigDecimal value19;

    /**
     *
     */
    @TableField(value = "value20")
    private BigDecimal value20;

    /**
     *
     */
    @TableField(value = "value21")
    private BigDecimal value21;

    /**
     *
     */
    @TableField(value = "value22")
    private BigDecimal value22;

    /**
     *
     */
    @TableField(value = "value23")
    private BigDecimal value23;

    /**
     *
     */
    @TableField(value = "value24")
    private BigDecimal value24;

    public TwentyFourMapDataCurve checkDataCurve() {
        return DataCurveUtil.convertTwentyFour(this, "value");
    }

}