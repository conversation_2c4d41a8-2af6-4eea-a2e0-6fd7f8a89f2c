package com.bobandata.cloud.trade.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsCreateReqVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsReqVo;
import com.bobandata.cloud.trade.admin.controller.cons.vo.CorpConsUpdateReqVo;
import com.bobandata.cloud.trade.admin.convert.CorpConsConvert;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsMapper;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.vo.Pagination;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.CORP_CONS_ADD_ERROR;
import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.CORP_CONS_NOT_EXISTS;
import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.CORP_CONS_VALUE_DUPLICATE;
import static com.bobandata.cloud.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons(售电公司: 用电客户档案信息)】的数据库操作Service实现
 * @createDate 2024-03-18 09:06:15
 */
@Slf4j
@Service
public class CorpConsServiceImpl extends BaseServiceImpl<CorpConsMapper, CorpConsDo> implements CorpConsService {


    @Autowired
    private CorpConsMapper corpConsMapper;

    @Override
    public PagingResult<CorpConsDo> listByPage(CorpConsReqVo reqVo) {

        LambdaQueryWrapperX<CorpConsDo> wrapperX = new LambdaQueryWrapperX<CorpConsDo>()
                .likeIfPresent(CorpConsDo::getConsNo, reqVo.getConsNo())
                .likeIfPresent(CorpConsDo::getCaption, reqVo.getCaption())
                .eqIfPresent(CorpConsDo::getLevel, reqVo.getLevel())
                .eqIfPresent(CorpConsDo::getCity, reqVo.getCity())
                .eqIfPresent(CorpConsDo::getElecTypeBigcls, reqVo.getElecTypeBigcls());
        if(StringUtils.isNotEmpty(reqVo.getKey())){
            wrapperX.and((wrapper) -> {
                wrapper.like(CorpConsDo::getConsNo, reqVo.getKey()).or().like(CorpConsDo::getCaption, reqVo.getKey());
            });
        }
        Pagination pagination = reqVo.toPagination();
        PagingResult<CorpConsDo> result = getBaseMapper().selectPage(pagination, wrapperX);
        for(CorpConsDo corpConsDo:result.getData()){
            if(StringUtils.isNotEmpty(corpConsDo.getIntermediaryName())){
                corpConsDo.setIntermediaryStatus(1);
            }else {
                corpConsDo.setIntermediaryStatus(0);
            }
        }
        return result;
    }

    @Override
    public ServiceResult<Boolean> createCorpCons(CorpConsCreateReqVo reqVo) {
        // 校验字典数据的值的唯一性
        validateCorpConsValueUnique(null, reqVo.getConsNo(), reqVo.getElementId());
        // 插入字典类型
        CorpConsDo corpConsDo = CorpConsConvert.INSTANCE.convert(reqVo);
        corpConsDo.setInPhone(reqVo.getIntermediaryName());
        corpConsDo.setInMethod(reqVo.getInMethod());
        corpConsDo.setInPhone(reqVo.getInPhone());
        int count = corpConsMapper.insert(corpConsDo);
        if (count > 0) {
            return ServiceResult.success(true);
        }
        return ServiceResult.error(CORP_CONS_ADD_ERROR);
    }

    @Override
    public void deleteCorpCons(Long id) {
        validateCorpConsExists(id);
        corpConsMapper.deleteById(id);
    }

    @Override
    public void updateCorpCons(CorpConsUpdateReqVo reqVo) {
        validateCorpConsExists(reqVo.getId());
        validateCorpConsValueUnique(reqVo.getId(), reqVo.getConsNo(), reqVo.getElementId());
        CorpConsDo consDo = CorpConsConvert.INSTANCE.convert(reqVo);
        consDo.setLastRefreshTime(Timestamp.valueOf(LocalDateTime.now()));
        consDo.setIntermediaryName(reqVo.getIntermediaryName());
        consDo.setInMethod(reqVo.getInMethod());
        consDo.setInPhone(reqVo.getInPhone());
        corpConsMapper.updateById(consDo);
    }

    @Override
    public List<Map> getLevel() {
        return corpConsMapper.getLevel();
    }

    @Override
    public List<Map> getCity() {
        return corpConsMapper.getCity();
    }

    @Override
    public List<Map> getYd() {
        return corpConsMapper.getYd();
    }

    private void validateCorpConsExists(Long id) {
        if (id == null) {
            return;
        }
        if (corpConsMapper.selectById(id) == null) {
            throw exception(CORP_CONS_NOT_EXISTS);
        }
    }

    public void validateCorpConsValueUnique(Long id, String consNo, String elementId) {
        CorpConsDo corpConsDo = corpConsMapper.selectOne(CorpConsDo::getConsNo, consNo, CorpConsDo::getElementId, elementId);
        if (corpConsDo == null) {
            return;
        }
        if (id == null) {
            throw exception(CORP_CONS_VALUE_DUPLICATE);
        }
        if (!corpConsDo.getId().equals(id)) {
            throw exception(CORP_CONS_VALUE_DUPLICATE);
        }
    }
}




