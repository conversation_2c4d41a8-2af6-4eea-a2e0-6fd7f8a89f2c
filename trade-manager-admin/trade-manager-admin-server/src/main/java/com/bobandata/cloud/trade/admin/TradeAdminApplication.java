package com.bobandata.cloud.trade.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @date 2024-03-14日 09:42
 * @description
 */
@MapperScan(basePackages = {
        "com.bobandata.cloud.trade.**.dal.mysql"
})
@SpringBootApplication(scanBasePackages = "com.bobandata")
public class TradeAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(TradeAdminApplication.class, args);
    }
}
