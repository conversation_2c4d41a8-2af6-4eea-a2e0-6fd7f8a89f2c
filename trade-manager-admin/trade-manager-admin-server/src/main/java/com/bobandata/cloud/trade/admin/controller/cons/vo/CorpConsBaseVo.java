package com.bobandata.cloud.trade.admin.controller.cons.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Date 2024/5/27 11:14
 * @Classname CorpConsBaseVo
 * @Description
 */
@Data
public class CorpConsBaseVo {

    @Schema(description = "用户户号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11111")
    @NotNull(message = "用户户号不能为空")
    @Size(max = 255, message = "用户户号长度不能超过 128 个字符")
    private String consNo;

    @Schema(description = "交易单元id", requiredMode = Schema.RequiredMode.REQUIRED, example = "f269616a2ab1")
    @NotNull(message = "交易单元id不能为空")
    @Size(max = 255, message = "交易单元id长度不能超过 256 个字符")
    private String elementId;

    /**
     * 旧的用户编号
     */
    @Schema(description = "旧的用户编号")
    private String oldAbbr;

    /**
     * 用户侧此域指向可能的代理售电公司
     */
    @Schema(description = "用户侧此域指向可能的代理售电公司")
    private String parentElementId;

    /**
     * 交易单元名称
     */
    @Schema(description = "交易单元名称")
    private String elementName;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String caption;

    /**
     * 所属区域
     */
    @Schema(description = "所属区域")
    private String orgNo;

    /**
     * 用户类型，现货还是非现货
     */
    @Schema(description = "用户类型，现货还是非现货")
    private String userType;

    /**
     * 电压等级
     */
    @Schema(description = "电压等级")
    private String level;

    /**
     * 用电类别 大工业用电 一般工商业用电 居民用电等
     */
    @Schema(description = "用电类别 大工业用电 一般工商业用电 居民用电等")
    private String elecTypeBigcls;

    /**
     * 用电行业
     */
    @Schema(description = "用电行业")
    private String industTypeDsc;


    /**
     * 产品类别
     */
    @Schema(description = "产品类别")
    private String productType;


    /**
     * 所属地市
     */
    @Schema(description = "所属地市")
    private String city;

    /**
     * 所属区县
     */
    @Schema(description = "所属区县")
    private String county;

    /**
     * 用电地址:用电客户的用电地址
     */
    @Schema(description = "用电地址:用电客户的用电地址")
    private String elecAddr;

    /**
     * 合同容量:合同约定的本用户的容量
     */
    @Schema(description = "合同约定的本用户的容量")
    private BigDecimal contractCap;

    /**
     * 运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量
     */
    @Schema(description = "运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量")
    private BigDecimal runCap;

    /**
     * 高耗能行业类别
     */
    @Schema(description = "高耗能行业类别")
    private String hecIndustryCode;

    /**
     * 负荷性质:负荷的重要程度分类一类，二类，三类
     */
    @Schema(description = "负荷性质:负荷的重要程度分类一类，二类，三类")
    private String lodeAttrCode;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String persion;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String phone;

    /**
     * 交易平台账号
     */
    @Schema(description = "交易平台账号")
    private String account;

    /**
     * 交易平台密码
     */
    @Schema(description = "交易平台密码")
    private String pwd;

    /**
     * 代理开始日期
     */
    @Schema(description = "代理开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp startTime;

    /**
     * 代理结束日期
     */
    @Schema(description = "代理结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp endTime;

    /**
     * 合同分成比例
     */
    @Schema(description = "合同分成比例")
    private String proportion;

    /**
     * 交易平台密码
     */
    @Schema(description = "代理服务费")
    private String serviceCharge;




}
