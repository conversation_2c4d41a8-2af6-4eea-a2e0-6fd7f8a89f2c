package com.bobandata.cloud.trade.admin.dal.mysql;

import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_real_energy】的数据库操作Mapper
 * @createDate 2024-03-20 15:27:52
 * @Entity com.bobandata.cloud.trade.ml.term.domain.CorpConsRealEnergy
 */
public interface CorpConsRealEnergyMapper extends BaseCrudMapper<CorpConsRealEnergyDo> {

    List<KeyValue<String, ?>> selectContractEnergy();
}




