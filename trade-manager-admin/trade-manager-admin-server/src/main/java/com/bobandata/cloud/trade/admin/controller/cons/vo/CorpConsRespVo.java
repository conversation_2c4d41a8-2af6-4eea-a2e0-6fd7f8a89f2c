package com.bobandata.cloud.trade.admin.controller.cons.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-03-19日 16:04
 * @description
 */
@Schema(description = "客户信息管理-客户管理 用户列表信息")
@Data
@EqualsAndHashCode(callSuper = true)
public class CorpConsRespVo extends CorpConsBaseVo {

    @Schema(description = "客户序号", required = true, example = "1024")
    private Long id;

    @Schema(description = "创建时间", required = true, example = "时间戳格式")
    private LocalDateTime createTime;

    @Schema(description = "居间人")
    private String intermediaryName;


    /**
     * 居间方式
     */
    @Schema(description = "居间方式")
    private String inMethod;


    /**
     * 居间人电话
     */
    @Schema(description = "居间人电话")
    private String inPhone;

    /**
     * 是否居间模式
     */
    @Schema(description = "是否居间模式")
    private Integer intermediaryStatus;

}
