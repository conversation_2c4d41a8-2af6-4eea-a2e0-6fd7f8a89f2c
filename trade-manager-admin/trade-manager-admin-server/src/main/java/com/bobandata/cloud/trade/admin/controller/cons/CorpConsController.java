package com.bobandata.cloud.trade.admin.controller.cons;


import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.admin.controller.cons.vo.*;
import com.bobandata.cloud.trade.admin.convert.CorpConsConvert;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsMapper;
import com.bobandata.cloud.trade.admin.service.CorpConsService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.util.JSON;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;
import java.util.Map;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

/**
 * <AUTHOR>
 * @date 2024-03-18日 09:10
 * @description
 */
@Tag(name = "客户信息管理-客户管理")
@Slf4j
@RequestMapping("/cons")
@RestController
public class CorpConsController extends BaseCrudRestController<CorpConsDo, CorpConsMapper> {

    @Autowired
    private CorpConsService corpConsService;

    @Operation(summary = "获得客户信息详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1")
    @GetMapping("/get")
    public ServiceResult<CorpConsDetailRespVo> getById(@RequestParam("id") Long id) {
        CorpConsDo corpConsDo = this.corpConsService.getById(id);
        CorpConsDetailRespVo corpConsDetailRespVo = CorpConsConvert.INSTANCE.convertDetail(corpConsDo);
        return ServiceResult.success(corpConsDetailRespVo);
    }

    @Operation(summary = "获得客户分页列表")
    @GetMapping("/list")
    @ResponseBody
    public PagingResult<CorpConsRespVo> listCorpCons(CorpConsReqVo corpConsReqVo) {
        PagingResult<CorpConsDo> corpConsDoPagingResult = corpConsService.listByPage(corpConsReqVo);
        return CorpConsConvert.INSTANCE.convertPage(corpConsDoPagingResult);
    }

    @PostMapping("create")
    @Operation(summary = "创建客户")
    public ServiceResult<Boolean> createCorpCons(@Valid @RequestBody CorpConsCreateReqVo reqVO) {
        return corpConsService.createCorpCons(reqVO);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除客户")
    public ServiceResult<Boolean> deleteCorpCons(@RequestBody String jsonString) {
        corpConsService.deleteCorpCons(Long.valueOf(JSON.parseObject(jsonString).get("id").toString()));
        return success(true);
    }

    @PostMapping("/update")
    @Operation(summary = "修改客户")
    public ServiceResult<Boolean> updateCorpCons(@Valid @RequestBody CorpConsUpdateReqVo reqVO) {
        corpConsService.updateCorpCons(reqVO);
        return success(true);
    }

    @GetMapping("/getLevel")
    @Operation(summary = "电压等级")
    public ServiceResult<List<Map>> getLevel() {
        List<Map> list = corpConsService.getLevel();
        return success(list);
    }

    @GetMapping("/getYd")
    @Operation(summary = "用电类别")
    public ServiceResult<List<Map>> getYd() {
        List<Map> list = corpConsService.getYd();
        return success(list);
    }

    @GetMapping("/getCity")
    @Operation(summary = "所属地市")
    public ServiceResult<List<Map>> getCity() {
        List<Map> list = corpConsService.getCity();
        return success(list);
    }

}
