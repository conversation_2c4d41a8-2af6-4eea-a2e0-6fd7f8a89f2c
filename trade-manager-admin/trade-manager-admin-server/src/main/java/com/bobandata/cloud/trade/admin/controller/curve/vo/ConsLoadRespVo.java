package com.bobandata.cloud.trade.admin.controller.curve.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-20日 13:53
 * @description
 */
@Schema(description = "客户信息管理-负荷预测及分析 负荷特性")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ConsLoadRespVo {

    @Schema(description = "户号")
    private String consNo;

    @Schema(description = "户名")
    private String consName;

    @Schema(description = "最大负荷")
    private BigDecimal maxLoad;

    @Schema(description = "最小负荷")
    private BigDecimal minLoad;

    @Schema(description = "最大平均负荷发生日")
    private Date maxLoadDate;

    @Schema(description = "最小平均负荷发生日")
    private Date minLoadDate;

    @Schema(description = "全平均负荷")
    private BigDecimal meanLoad;

    @Schema(description = "日均电量")
    private BigDecimal meanEnergy;

    @Schema(description = "日均尖峰电量")
    private BigDecimal meanHighestEnergy;

    @Schema(description = "日均高峰电量")
    private BigDecimal meanPeakEnergy;

    @Schema(description = "日均平段电量")
    private BigDecimal meanFlatEnergy;

    @Schema(description = "日均谷段电量")
    private BigDecimal meanValleyEnergy;

    @Schema(description = "实际负荷曲线")
    private List<KeyValueVo> realLoadCurves;

    @Schema(description = "典型特性曲线")
    private List<KeyValueVo> typicalLoadCurves;

    @Schema(description = "无特性曲线")
    private List<KeyValueVo> otherLoadCurves;
}
