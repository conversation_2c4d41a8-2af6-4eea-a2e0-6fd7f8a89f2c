package com.bobandata.cloud.trade.admin.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 售电公司: 用电客户档案信息
 *
 * @TableName corp_cons
 */
@TableName(value = "corp_cons")
@Data
public class CorpConsDo extends BaseDo {


    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     * 用户户号
     */
    @TableField(value = "cons_no")
    private String consNo;

    /**
     * 旧的用户编号
     */
    @TableField(value = "old_abbr")
    private String oldAbbr;

    /**
     * 交易单元id
     */
    @TableField(value = "element_id")
    private String elementId;

    /**
     * 用户侧此域指向可能的代理售电公司
     */
    @TableField(value = "parent_element_id")
    private String parentElementId;

    /**
     * 交易单元名称
     */
    @TableField(value = "element_name")
    private String elementName;
    /**
     * 用户户名
     */
    @TableField(value = "caption")
    private String caption;

    /**
     * 所属区域
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     * 用户类型，现货还是非现货
     */
    @TableField(value = "user_type")
    private String userType;

    /**
     * 电压等级
     */
    @TableField(value = "level")
    private String level;

    /**
     * 用电类别 大工业用电 一般工商业用电 居民用电等
     */
    @TableField(value = "elec_type_bigcls")
    private String elecTypeBigcls;

    /**
     * 用电行业
     */
    @TableField(value = "indust_type_dsc")
    private String industTypeDsc;


    /**
     * 产品类别
     */
    @TableField(value = "product_type")
    private String productType;


    /**
     * 所属地市
     */
    @TableField(value = "city")
    private String city;

    /**
     * 所属区县
     */
    @TableField(value = "county")
    private String county;

    /**
     * 用电地址:用电客户的用电地址
     */
    @TableField(value = "ELEC_ADDR")
    private String elecAddr;

    /**
     * 合同容量:合同约定的本用户的容量
     */
    @TableField(value = "CONTRACT_CAP")
    private BigDecimal contractCap;

    /**
     * 运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量
     */
    @TableField(value = "RUN_CAP")
    private BigDecimal runCap;

    /**
     * 高耗能行业类别
     */
    @TableField(value = "HEC_INDUSTRY_CODE")
    private String hecIndustryCode;

    /**
     * 负荷性质:负荷的重要程度分类一类，二类，三类
     */
    @TableField(value = "LODE_ATTR_CODE")
    private String lodeAttrCode;

    /**
     * 联系人
     */
    @TableField(value = "persion")
    private String persion;

    /**
     * 联系方式
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 交易平台账号
     */
    @TableField(value = "account")
    private String account;

    /**
     * 交易平台密码
     */
    @TableField(value = "pwd")
    private String pwd;

    /**
     * 代理开始日期
     */
    @TableField(value = "start_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTime;

    /**
     * 代理结束日期
     */
    @TableField(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTime;

    /**
     * 合同分成比例
     */
    @TableField(value = "proportion")
    private String proportion;

    /**
     * 居间人
     */
    @TableField(value = "intermediary")
    private String intermediaryName;


    /**
     * 居间方式
     */
    @TableField(value = "intermediary_type")
    private String inMethod;


    /**
     * 居间人电话
     */
    @TableField(value = "intermediary_phone")
    private String inPhone;

    /**
     * 代理服务费
     */
    @TableField(value = "service_charge")
    private String serviceCharge;

    /**
     * 是否居间模式
     */
    @TableField(exist = false)
    private Integer intermediaryStatus;
}