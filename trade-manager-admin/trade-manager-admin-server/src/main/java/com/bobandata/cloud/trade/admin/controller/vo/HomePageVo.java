package com.bobandata.cloud.trade.admin.controller.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-22日 11:21
 * @description
 */
@Schema(description = "首页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class HomePageVo {

    @Schema(description = "电量排名")
    private List<OrderNode> energyOrders;

    @Schema(description = "偏差排名")
    private List<OrderNode> offsetOrders;

    @Schema(description = "实际电量")
    private KeyValueVo realEnergy;

    @Schema(description = "交易电量")
    private KeyValueVo contractEnergy;

    @Schema(description = "月度收入")
    private KeyValueVo monthIn;

    @Schema(description = "月度支出")
    private KeyValueVo monthOut;

    @Data
    public static class OrderNode {

        private Integer order;

        private String name;

        private Object value;

    }
}
