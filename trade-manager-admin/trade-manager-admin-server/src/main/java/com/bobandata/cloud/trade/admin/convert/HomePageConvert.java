package com.bobandata.cloud.trade.admin.convert;

import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.utils.date.YearMonthUtils;
import com.bobandata.cloud.trade.admin.controller.vo.HomePageVo;
import java.math.BigDecimal;
import java.time.Year;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-22日 11:23
 * @description
 */
public class HomePageConvert {

    public static final HomePageConvert INSTANCE = new HomePageConvert();

    public HomePageVo convertVo(List<KeyValue> energyOrders,
                                List<KeyValue> realEnergySum,
                                List<KeyValue<String, ?>> contractEnergySum) {
        List<HomePageVo.OrderNode> energyNodes = new ArrayList<>();
        List<HomePageVo.OrderNode> offsetNodes = new ArrayList<>();
        List<KeyValue> sortList = energyOrders.stream()
                                              .sorted(new Comparator<KeyValue>() {
                                                  @Override
                                                  public int compare(final KeyValue o1, final KeyValue o2) {
                                                      return new BigDecimal(o2.getValue().toString())
                                                              .compareTo(new BigDecimal(o1.getValue().toString()));
                                                  }
                                              }).collect(Collectors.toList());
        for (int i = 0; i < sortList.size(); i++) {
            KeyValue keyValue = sortList.get(i);
            String key = keyValue.getKey().toString();
            Object value = keyValue.getValue();
            HomePageVo.OrderNode energyNode = new HomePageVo.OrderNode()
                    .setOrder(i + 1)
                    .setName(key)
                    .setValue(value);
            energyNodes.add(energyNode);
            HomePageVo.OrderNode offsetNode = new HomePageVo.OrderNode()
                    .setOrder(i + 1)
                    .setName(key)
                    .setValue(BigDecimal.ZERO);
            offsetNodes.add(offsetNode);
        }
        List<String> yearMonths = YearMonthUtils.getYearMonthsStrList(Year.now());
        KeyValueVo realEnergy = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        realEnergy.putKeyValues(realEnergySum);
        KeyValueVo contractEnergy = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        contractEnergy.putKeyValues2(contractEnergySum);
        KeyValueVo monthIn = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        KeyValueVo monthExpend = KeyValueVo.build(yearMonths, BigDecimal.ZERO);

        HomePageVo homePageVo = new HomePageVo();
        return homePageVo.setEnergyOrders(energyNodes)
                         .setOffsetOrders(offsetNodes)
                         .setRealEnergy(realEnergy)
                         .setContractEnergy(contractEnergy)
                         .setMonthIn(monthIn)
                         .setMonthOut(monthExpend);
    }
}
