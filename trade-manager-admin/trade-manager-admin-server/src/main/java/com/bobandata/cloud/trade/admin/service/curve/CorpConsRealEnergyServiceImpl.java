package com.bobandata.cloud.trade.admin.service.curve;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealEnergyMapper;
import com.bobandata.cloud.trade.mvc.core.query.QueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.github.yulichang.query.MPJQueryWrapper;
import java.sql.Date;
import java.time.Year;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_real_energy】的数据库操作Service实现
 * @createDate 2024-03-20 15:27:52
 */
@Service
public class CorpConsRealEnergyServiceImpl extends BaseServiceImpl<CorpConsRealEnergyMapper, CorpConsRealEnergyDo> implements CorpConsRealEnergyService {

    @Override
    public List<CorpConsRealEnergyDo> listRealEnergies(Long consId, Date startDate, Date endDate) {
        QueryWrapper<CorpConsRealEnergyDo> wrapper = new QueryWrapperX<CorpConsRealEnergyDo>().between(
                "data_date", startDate, endDate).eq("cons_id", consId);
        return this.list(wrapper);
    }

    @Override
    public List<KeyValue<String, ?>> getContractSum() {
        return this.getBaseMapper().selectContractEnergy();
    }

    @Override
    public List<KeyValue> getRealEnergySum() {
        //        QueryWrapper<CorpConsRealEnergyDo> wrapper = new QueryWrapperX<CorpConsRealEnergyDo>();
        //        wrapper.select("month(data_date)", "sum(total_battery) as sumEnergy")
        //               .eq("year(data_date)", Year.now().getValue())
        //               .groupBy("month(data_date)");

        MPJQueryWrapper<CorpConsRealEnergyDo> wrapper = new MPJQueryWrapper<CorpConsRealEnergyDo>()
                .select("date_format(data_date,'%Y-%m') as `key`", "sum(total_battery) as `value`")
                .eq("year(data_date)", Year.now().getValue())
                .groupBy("month(data_date)");
        return this.getBaseMapper().selectJoinList(KeyValue.class, wrapper);
    }

    @Override
    public List<KeyValue> listConsEnergyOrder() {
        MPJQueryWrapper<CorpConsRealEnergyDo> wrapper = new MPJQueryWrapper<CorpConsRealEnergyDo>()
                .select("cc.cons_name as `key`", "ifnull(sum(t.total_battery),0) as `value`")
                .leftJoin("corp_cons cc on cc.id = t.cons_id")
                .eq("year(t.data_date)", Year.now().getValue())
                .groupBy("t.cons_id");
        return this.getBaseMapper().selectJoinList(KeyValue.class, wrapper);
    }
}




