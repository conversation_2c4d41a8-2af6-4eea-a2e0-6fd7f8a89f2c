package com.bobandata.cloud.trade.admin.controller.curve.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.sql.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-20日 13:57
 * @description
 */
@Schema(description = "客户信息管理-负荷预测及分析 负荷预测对比")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LoadForecastReqVo {

    @Schema(description = "用户ID")
    private Long consId;

    @Schema(description = "预测时间", example = "2024-03-10")
    private Date forecastDate;

    @Schema(description = "特性类型")
    private Integer type;
}
