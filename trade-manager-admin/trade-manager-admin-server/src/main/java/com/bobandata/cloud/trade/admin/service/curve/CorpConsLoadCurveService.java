package com.bobandata.cloud.trade.admin.service.curve;

import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsLoadCurveDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsLoadCurveMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_load_curve(用户相关负荷曲线定义)】的数据库操作Service
 * @createDate 2024-03-20 10:15:40
 */
public interface CorpConsLoadCurveService extends BaseService<CorpConsLoadCurveMapper, CorpConsLoadCurveDo> {

    List<CorpConsLoadCurveDo> listLoadCurves(Long consId, Date startDate, Date endDate, Integer... types);
}
