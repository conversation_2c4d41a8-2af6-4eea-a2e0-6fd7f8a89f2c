package com.bobandata.cloud.trade.admin.service.curve;

import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo;
import com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealEnergyMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【corp_cons_real_energy】的数据库操作Service
 * @createDate 2024-03-20 15:27:52
 */
public interface CorpConsRealEnergyService extends BaseService<CorpConsRealEnergyMapper, CorpConsRealEnergyDo> {

    List<CorpConsRealEnergyDo> listRealEnergies(Long consId, Date startDate, Date endDate);

    List<KeyValue<String, ?>> getContractSum();

    List<KeyValue> getRealEnergySum();

    List<KeyValue> listConsEnergyOrder();
}
