package com.bobandata.cloud.trade.admin.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.label.DateLabelValue;
import com.bobandata.cloud.trade.admin.controller.curve.vo.ConsLoadRespVo;
import com.bobandata.cloud.trade.admin.controller.curve.vo.LoadForecastRespVo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsLoadCurveDo;
import com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-21日 11:22
 * @description
 */
public class ConsLoadCurveConvert {

    public static final ConsLoadCurveConvert INSTANCE = new ConsLoadCurveConvert();

    public LoadForecastRespVo convertForecastVo(List<CorpConsLoadCurveDo> corpConsLoadCurveDos,
                                                List<CorpConsRealLoadDo> corpConsRealLoadDos) {
        LoadForecastRespVo respVo = new LoadForecastRespVo();
        // 随机数 后面改
        respVo.setAccuracy(RandomUtil.randomBigDecimal(new BigDecimal("100")));
        if (CollUtil.isNotEmpty(corpConsLoadCurveDos)) {
            CorpConsLoadCurveDo corpConsLoadCurveDo = corpConsLoadCurveDos.get(0);
            respVo.setForecastCurve(
                    corpConsLoadCurveDo.checkDataCurve().convertKeyValue(corpConsLoadCurveDo.getStartDate()));
            respVo.setAccuracy(BigDecimal.ZERO);
        }
        if (CollUtil.isNotEmpty(corpConsRealLoadDos)) {
            CorpConsRealLoadDo corpConsRealLoadDo = corpConsRealLoadDos.get(0);
            respVo.setRealCurve(KeyValueVo.build(
                    corpConsRealLoadDo.checkDataCurve().getTimeDataMap(),
                    new DateLabelValue(corpConsRealLoadDo.getDataDate())
            ));
            respVo.setAccuracy(BigDecimal.ZERO);
        }
        return respVo;
    }

    public void setLoadCurves(ConsLoadRespVo respVo, List<CorpConsLoadCurveDo> loadCurves) {
        List<KeyValueVo> typicalLoadCurves = loadCurves.stream()
                                                       .filter(corpConsLoadCurveDo -> corpConsLoadCurveDo.getDataType()
                                                                                                         .equals(1))
                                                       .map(curveDo -> {
                                                           TwentyFourMapDataCurve dataCurve = curveDo.checkDataCurve();
                                                           Date dataDate = curveDo.getStartDate();
                                                           return dataCurve.convertKeyValue(dataDate);
                                                       })
                                                       .collect(Collectors.toList());
        respVo.setTypicalLoadCurves(typicalLoadCurves);
        List<KeyValueVo> otherLoadCurves = loadCurves.stream()
                                                     .filter(corpConsLoadCurveDo -> corpConsLoadCurveDo.getDataType()
                                                                                                       .equals(2))
                                                     .map(curveDo -> {
                                                         TwentyFourMapDataCurve dataCurve = curveDo.checkDataCurve();
                                                         Date dataDate = curveDo.getStartDate();
                                                         return dataCurve.convertKeyValue(dataDate);
                                                     })
                                                     .collect(Collectors.toList());
        respVo.setOtherLoadCurves(otherLoadCurves);
    }
}
