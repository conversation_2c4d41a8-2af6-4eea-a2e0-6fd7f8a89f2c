<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealEnergyMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealEnergyDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="consId" column="cons_id" jdbcType="BIGINT"/>
        <result property="consNo" column="cons_no" jdbcType="VARCHAR"/>
        <result property="dataDate" column="data_date" jdbcType="DATE"/>
        <result property="energy1" column="energy1" jdbcType="DECIMAL"/>
        <result property="energy2" column="energy2" jdbcType="DECIMAL"/>
        <result property="energy3" column="energy3" jdbcType="DECIMAL"/>
        <result property="energy4" column="energy4" jdbcType="DECIMAL"/>
        <result property="totalBattery" column="total_battery" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>
    <select id="selectContractEnergy" resultType="com.bobandata.cloud.common.core.KeyValue">
        select date_format(ce.start_time, '%Y-%m') as `key`, sum(contract_energy) as `value`
        from ml_term_contract_info ce
        where date_year = year(now())
        group by ce.date_month
    </select>


</mapper>
