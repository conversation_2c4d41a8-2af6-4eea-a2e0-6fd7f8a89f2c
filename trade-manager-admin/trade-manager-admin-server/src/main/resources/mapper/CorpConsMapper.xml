<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.admin.dal.mysql.CorpConsMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="consNo" column="cons_no" jdbcType="VARCHAR"/>
        <result property="consName" column="cons_name" jdbcType="VARCHAR"/>
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
        <result property="voltCode" column="VOLT_CODE" jdbcType="VARCHAR"/>
        <result property="elecTypeBigcls" column="elec_type_bigcls" jdbcType="VARCHAR"/>
        <result property="industTypeDsc" column="indust_type_dsc" jdbcType="VARCHAR"/>
        <result property="elecAddr" column="ELEC_ADDR" jdbcType="VARCHAR"/>
        <result property="contractCap" column="CONTRACT_CAP" jdbcType="DECIMAL"/>
        <result property="runCap" column="RUN_CAP" jdbcType="DECIMAL"/>
        <result property="hecIndustryCode" column="HEC_INDUSTRY_CODE" jdbcType="VARCHAR"/>
        <result property="lodeAttrCode" column="LODE_ATTR_CODE" jdbcType="VARCHAR"/>
        <result property="contactName" column="CONTACT_NAME" jdbcType="VARCHAR"/>
        <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cons_no,cons_name,
        org_no,VOLT_CODE,elec_type_bigcls,
        indust_type_dsc,ELEC_ADDR,CONTRACT_CAP,
        RUN_CAP,HEC_INDUSTRY_CODE,LODE_ATTR_CODE,
        CONTACT_NAME,contact_phone,create_time,
        creator_id,last_refresh_time,last_modifier_id
    </sql>
</mapper>
