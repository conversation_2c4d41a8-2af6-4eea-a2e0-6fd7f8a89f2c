<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.admin.dal.mysql.CorpConsRealLoadMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsRealLoadDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="consId" column="cons_id" jdbcType="BIGINT"/>
        <result property="dataDate" column="data_date" jdbcType="DATE"/>
        <result property="tPq1" column="T_PQ1" jdbcType="DECIMAL"/>
        <result property="tPq2" column="T_PQ2" jdbcType="DECIMAL"/>
        <result property="tPq3" column="T_PQ3" jdbcType="DECIMAL"/>
        <result property="tPq4" column="T_PQ4" jdbcType="DECIMAL"/>
        <result property="tPq5" column="T_PQ5" jdbcType="DECIMAL"/>
        <result property="tPq6" column="T_PQ6" jdbcType="DECIMAL"/>
        <result property="tPq7" column="T_PQ7" jdbcType="DECIMAL"/>
        <result property="tPq8" column="T_PQ8" jdbcType="DECIMAL"/>
        <result property="tPq9" column="T_PQ9" jdbcType="DECIMAL"/>
        <result property="tPq10" column="T_PQ10" jdbcType="DECIMAL"/>
        <result property="tPq11" column="T_PQ11" jdbcType="DECIMAL"/>
        <result property="tPq12" column="T_PQ12" jdbcType="DECIMAL"/>
        <result property="tPq13" column="T_PQ13" jdbcType="DECIMAL"/>
        <result property="tPq14" column="T_PQ14" jdbcType="DECIMAL"/>
        <result property="tPq15" column="T_PQ15" jdbcType="DECIMAL"/>
        <result property="tPq16" column="T_PQ16" jdbcType="DECIMAL"/>
        <result property="tPq17" column="T_PQ17" jdbcType="DECIMAL"/>
        <result property="tPq18" column="T_PQ18" jdbcType="DECIMAL"/>
        <result property="tPq19" column="T_PQ19" jdbcType="DECIMAL"/>
        <result property="tPq20" column="T_PQ20" jdbcType="DECIMAL"/>
        <result property="tPq21" column="T_PQ21" jdbcType="DECIMAL"/>
        <result property="tPq22" column="T_PQ22" jdbcType="DECIMAL"/>
        <result property="tPq23" column="T_PQ23" jdbcType="DECIMAL"/>
        <result property="tPq24" column="T_PQ24" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>


</mapper>
