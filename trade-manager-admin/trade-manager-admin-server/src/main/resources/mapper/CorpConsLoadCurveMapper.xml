<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.admin.dal.mysql.CorpConsLoadCurveMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.admin.dal.dataobject.CorpConsLoadCurveDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="consId" column="cons_id" jdbcType="BIGINT"/>
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
        <result property="dateYear" column="date_year" jdbcType="INTEGER"/>
        <result property="dateMonth" column="date_month" jdbcType="INTEGER"/>
        <result property="dataType" column="data_type" jdbcType="INTEGER"/>
        <result property="startDate" column="start_date" jdbcType="DATE"/>
        <result property="endDate" column="end_date" jdbcType="DATE"/>
        <result property="value1" column="value1" jdbcType="DECIMAL"/>
        <result property="value2" column="value2" jdbcType="DECIMAL"/>
        <result property="value3" column="value3" jdbcType="DECIMAL"/>
        <result property="value4" column="value4" jdbcType="DECIMAL"/>
        <result property="value5" column="value5" jdbcType="DECIMAL"/>
        <result property="value6" column="value6" jdbcType="DECIMAL"/>
        <result property="value7" column="value7" jdbcType="DECIMAL"/>
        <result property="value8" column="value8" jdbcType="DECIMAL"/>
        <result property="value9" column="value9" jdbcType="DECIMAL"/>
        <result property="value10" column="value10" jdbcType="DECIMAL"/>
        <result property="value11" column="value11" jdbcType="DECIMAL"/>
        <result property="value12" column="value12" jdbcType="DECIMAL"/>
        <result property="value13" column="value13" jdbcType="DECIMAL"/>
        <result property="value14" column="value14" jdbcType="DECIMAL"/>
        <result property="value15" column="value15" jdbcType="DECIMAL"/>
        <result property="value16" column="value16" jdbcType="DECIMAL"/>
        <result property="value17" column="value17" jdbcType="DECIMAL"/>
        <result property="value18" column="value18" jdbcType="DECIMAL"/>
        <result property="value19" column="value19" jdbcType="DECIMAL"/>
        <result property="value20" column="value20" jdbcType="DECIMAL"/>
        <result property="value21" column="value21" jdbcType="DECIMAL"/>
        <result property="value22" column="value22" jdbcType="DECIMAL"/>
        <result property="value23" column="value23" jdbcType="DECIMAL"/>
        <result property="value24" column="value24" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>


</mapper>
