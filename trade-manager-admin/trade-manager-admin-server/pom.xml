<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trade-manager-admin</artifactId>
        <groupId>com.bobandata.cloud</groupId>
        <version>${re.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trade-manager-admin-server</artifactId>

    <name>trade : manager :: admin ::: server</name>

    <dependencies>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-framework-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-mvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-curve</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-mybatisplus</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.3</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <attach>false</attach>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <addResources>true</addResources>
                    <mainClass>
                        com.bobandata.cloud.trade.admin.TradeAdminApplication
                    </mainClass>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ttf</include>
                    <include>**/*.yaml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>