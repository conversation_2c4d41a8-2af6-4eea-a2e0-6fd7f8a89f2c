project:
  namespace: trade-manager # 项目, 对应 harbor 的项目和 k8s 的 namespace。（必填）
  imageRegister: # harbor 配置 (账号和密码是环境变量，已经通过 gitlab environment variable 设置)
    address: docker-register.bobandata.com # 地址 （必填）
    username: HARBOR_USER # 账号环境变量，默认设置可以不用更改 （必填）
    password: HARBOR_PASSWD # 密码环境变量，默认设置可以不用更改（必填）
  chart:
    name: trade-manager
    description: 售电管理平台
    version: 0.1.0
  testEnvironments:
    master:
      branch: master
      namespace: trade-manager
      env:
        PROFILE_ACTIVE: prod
        DATABASE_HOST: *************
        DATABASE_PORT: 32372
        DATABASE_DB: trade_sms
        DATABASE_USER: root
        DATABASE_PASSWORD: root
        MINIO_ENDPOINT: http://*************:9000
        MINIO_ACCESS_KEY: Rtrv86RXJxDyMLj5
        MINIO_SECRET_KEY: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
        XXL_JOB_ENABLED: true
        XXL_JOB_ADMIN_ADDRESS: http://{{ $fullName }}-xxl-job-schedule-server:8080/xxl-job-admin
    test1:
      branch: (.*)-test1$
      namespace: trade-manager-test1
      kubeConfigEnv: KUBECONFIG_TEST1
      env:
        PROFILE_ACTIVE: prod
        DATABASE_HOST: *************
        DATABASE_PORT: 32372
        DATABASE_DB: trade_sms
        DATABASE_USER: root
        DATABASE_PASSWORD: root
        MINIO_ENDPOINT: http://*************:9000
        MINIO_ACCESS_KEY: Rtrv86RXJxDyMLj5
        MINIO_SECRET_KEY: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
        XXL_JOB_ENABLED: true
        XXL_JOB_ADMIN_ADDRESS: http://{{ $fullName }}-xxl-job-schedule-server:8080/xxl-job-admin
  deploy:
    enabled: true
    ingress:
      route:
        - module: trade-manager-data
          port: 20020
          prefixPath: /trade/data
        - module: trade-manager-system-server
          port: 20081
          prefixPath: /trade/cloud
        - module: trade-manager-ml-term-server
          port: 20082
          prefixPath: /trade/cloud/ml
        - module: trade-manager-admin-server
          port: 20083
          prefixPath: /trade/cloud/admin
        - service: test-selling-electricity-sys-web-selling-electricity-sys-web
          port: 80
          prefixPath: /
    env:
      PROFILE_ACTIVE: prod
      DATABASE_HOST: *************
      DATABASE_PORT: 32372
      DATABASE_DB: trade_sms
      DATABASE_USER: root
      DATABASE_PASSWORD: root
      MINIO_ENDPOINT: http://*************:9000
      MINIO_ACCESS_KEY: Rtrv86RXJxDyMLj5
      MINIO_SECRET_KEY: 80nVcXH9mIukmmemIZbCKpUeVajB9iHZ
      XXL_JOB_ENABLED: true
      XXL_JOB_ADMIN_ADDRESS: http://{{ $fullName }}-xxl-job-schedule-server:8080/xxl-job-admin
      CHROME_URL: http://selenium-grid-selenium-hub.trade-manager.svc:4444
      CHROMEDRIVER_URL: /test/chromedriverFile/chromedriver
      TUPIAN_URL: /png/
      CRON: 0 50 23 * * ?
      JAVA_TOOL_OPTIONS: "-Dfile.encoding=UTF-8"  # 只在这里保留
  module: # 模块
    - name: trade-manager-data
      dockerfile: trade-manager-data/Dockerfile
      target: trade-manager-data/target
      buildArgs: "--settings=.mvn/settings_boban.xml -Dcheckstyle.skip=true -T 1C -Dmaven.compile.fork=true" # 编译参数，比如 java 项目可以指定 maven 的配置文件路径（选填）
      env: { } # 环境变量 (选填)
      deploy:
        aliasName: 湖南交易数据自动爬取
        ports:
          - name: rest
            port: 20020
        configmap:
          - name: application.yaml
            local: trade-manager-data/src/main/resources/application.yaml
            mount: /workspace/config/application.yml
          - name: logback.xml
            local: trade-manager-data/src/main/resources/logback.xml
            mount: /workspace/config/logback.xml
        volume:
          - name: selenium-download
            mount: /tmp/download
            persistentVolumeClaim:
              storageClassName: managed-nfs-storage
              accessModes:
                - "ReadWriteMany"
              storage: 5Gi


    - name: trade-manager-admin-server # 模块名称 （必填）
      target: trade-manager-admin/trade-manager-admin-server/target # java 项目需要填写为对应模块的 target 目录，vue 可不填（选填）
      #   changes:
      #     - trade-framework/trade-framework-common/**
      #     - trade-framework/trade-springboot-starter-codes/**
      #     - trade-framework/trade-springboot-starter-curve/**
      #     - trade-framework/trade-springboot-starter-expression/**
      #     - trade-framework/trade-springboot-starter-mvc/**
      #     - trade-framework/trade-springboot-starter-mybatisplus/**
      #     - trade-framework/trade-springboot-starter-operatelog/**
      #     - trade-framework/trade-springboot-starter-security/**
      #     - trade-framework/trade-springboot-starter-web/**
      #     - trade-manager-admin/**
      buildArgs: "--settings=.mvn/settings_boban.xml -Dcheckstyle.skip=true -T 1C -Dmaven.compile.fork=true" # 编译参数，比如 java 项目可以指定 maven 的配置文件路径（选填）
      env: { } # 环境变量 (选填)
      deploy:
        aliasName: 客户管理
        ports:
          - name: rest
            port: 20083
        configmap:
          - name: application.yaml
            local: trade-manager-admin/trade-manager-admin-server/src/main/resources/application.yaml
            mount: /workspace/config/application.yml
          - name: application-prod.yaml
            local: trade-manager-admin/trade-manager-admin-server/src/main/resources/application-prod.yaml
            mount: /workspace/config/application-prod.yaml
          - name: logback.xml
            local: trade-manager-admin/trade-manager-admin-server/src/main/resources/logback.xml
            mount: /workspace/config/logback.xml
    - name: trade-manager-ml-term-server # 模块名称 （必填）
      target: trade-manager-ml-term/trade-manager-ml-term-server/target # java 项目需要填写为对应模块的 target 目录，vue 可不填（选填）
      #   changes:
      #     - trade-framework/trade-framework-common/**
      #     - trade-framework/trade-springboot-starter-codes/**
      #     - trade-framework/trade-springboot-starter-curve/**
      #     - trade-framework/trade-springboot-starter-expression/**
      #     - trade-framework/trade-springboot-starter-mvc/**
      #     - trade-framework/trade-springboot-starter-mybatisplus/**
      #     - trade-framework/trade-springboot-starter-operatelog/**
      #     - trade-framework/trade-springboot-starter-security/**
      #     - trade-framework/trade-springboot-starter-web/**
      #     - trade-manager-ml-term/**
      buildArgs: "--settings=.mvn/settings_boban.xml -Dcheckstyle.skip=true -T 1C -Dmaven.compile.fork=true" # 编译参数，比如 java 项目可以指定 maven 的配置文件路径（选填）
      env: { } # 环境变量 (选填)
      deploy:
        aliasName: 中长期管理
        ports:
          - name: rest
            port: 20082
        configmap:
          - name: application.yaml
            local: trade-manager-ml-term/trade-manager-ml-term-server/src/main/resources/application.yaml
            mount: /workspace/config/application.yml
          - name: application-prod.yaml
            local: trade-manager-ml-term/trade-manager-ml-term-server/src/main/resources/application-prod.yaml
            mount: /workspace/config/application-prod.yaml
          - name: logback.xml
            local: trade-manager-ml-term/trade-manager-ml-term-server/src/main/resources/logback.xml
            mount: /workspace/config/logback.xml
    - name: trade-manager-system-server # 模块名称 （必填）
      target: trade-manager-system/trade-manager-system-server/target # java 项目需要填写为对应模块的 target 目录，vue 可不填（选填）
      #   changes:
      #     - trade-framework/trade-framework-common/**
      #     - trade-framework/trade-springboot-starter-codes/**
      #     - trade-framework/trade-springboot-starter-curve/**
      #     - trade-framework/trade-springboot-starter-data-permission/**
      #     - trade-framework/trade-springboot-starter-excel/**
      #     - trade-framework/trade-springboot-starter-expression/**
      #     - trade-framework/trade-springboot-starter-file/**
      #     - trade-framework/trade-springboot-starter-mvc/**
      #     - trade-framework/trade-springboot-starter-mybatisplus/**
      #     - trade-framework/trade-springboot-starter-notice/**
      #     - trade-framework/trade-springboot-starter-operatelog/**
      #     - trade-framework/trade-springboot-starter-security/**
      #     - trade-framework/trade-springboot-starter-variable/**
      #     - trade-framework/trade-springboot-starter-web/**
      #     - trade-manager-system/**
      buildArgs: "--settings=.mvn/settings_boban.xml -Dcheckstyle.skip=true -T 1C -Dmaven.compile.fork=true" # 编译参数，比如 java 项目可以指定 maven 的配置文件路径（选填）
      env: { } # 环境变量 (选填)
      deploy:
        aliasName: 系统管理
        ports:
          - name: rest
            port: 20081
        configmap:
          - name: application.yaml
            local: trade-manager-system/trade-manager-system-server/src/main/resources/application.yaml
            mount: /workspace/config/application.yml
          - name: application-prod.yaml
            local: trade-manager-system/trade-manager-system-server/src/main/resources/application-prod.yaml
            mount: /workspace/config/application-prod.yaml
          - name: logback.xml
            local: trade-manager-system/trade-manager-system-server/src/main/resources/logback.xml
            mount: /workspace/config/logback.xml


