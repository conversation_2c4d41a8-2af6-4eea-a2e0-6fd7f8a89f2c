package com.bobandata.cloud.trade.ml.term;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.SettleTotalRespVo;
import com.bobandata.cloud.trade.ml.term.convert.SettleTotalConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.google.common.collect.Range;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:10
 * @description
 */
public class ITTestMlTermSettle {

    @Test
    public void testSegment() {
        TimeSegment segment = TwentyFourPointTimeSegment.valueOf("01:00-07:00");
        TimeSegment segment1 = TwentyFourPointTimeSegment.valueOf("05:00-06:00");
        segmentScale(segment, segment1);
    }

    private void segmentScale(TimeSegment molSegment, TimeSegment denSegment) {
        Range<Integer> molRange = molSegment.getPointsRange();
        Range<Integer> denRange = denSegment.getPointsRange();
        Range<Integer> intersection;
        try {
            intersection = molRange.intersection(denRange);
        } catch (IllegalArgumentException e) {
            return;
        }
        Integer lowerEndpoint = intersection.lowerEndpoint();
        Integer upperEndpoint = intersection.upperEndpoint();
        int len = denSegment.getEndPoint() - denSegment.getStartPoint();
        double per = (double) (upperEndpoint - lowerEndpoint) / len;
        System.out.println("per = " + per);
    }

    @Test
    public void testConvertTotal() {
        ArrayList<MlSettleTotalInfoDo> totalInfoDos = new ArrayList<>();
        BigDecimal bigDecimal = new BigDecimal("1.0");
        MlSettleTotalInfoDo mlSettleTotalInfoDo = new MlSettleTotalInfoDo();
        mlSettleTotalInfoDo.setDateYear(2024).setDateMonth(1).setActualEnergy(bigDecimal);
        totalInfoDos.add(mlSettleTotalInfoDo);
        List<SettleTotalRespVo> settleTotalRespVos = SettleTotalConvert.INSTANCE.convertTotalVo(totalInfoDos);
        System.out.println("settleTotalRespVos = " + settleTotalRespVos);
    }

    @Test
    public void testSplitPeriodToHours() {
        Map<LocalDateTime, BigDecimal> power = splitPower(
                Timestamp.valueOf("2024-10-23 20:22:32").toLocalDateTime(),
                Timestamp.valueOf("2024-10-24 02:19:49").toLocalDateTime(), new BigDecimal("40.82")
        );
        System.out.println("power = " + power);
        Map<LocalDate, List<Map.Entry<LocalDateTime, BigDecimal>>> collect = power.entrySet()
                                                                                  .stream()
                                                                                  .collect(Collectors.groupingBy(
                                                                                          entry -> entry.getKey()
                                                                                                        .toLocalDate()));
        System.out.println("collect = " + collect);
    }

    public Map<LocalDateTime, BigDecimal> splitPower(LocalDateTime start, LocalDateTime end, BigDecimal value) {
        BigDecimal totalMinutes = new BigDecimal(String.valueOf(Duration.between(start, end).toMinutes()));
        LocalDateTime[] array = splitPeriodToHours(start, end).toArray(new LocalDateTime[0]);
        Map<LocalDateTime, BigDecimal> dateTimeVal = new HashMap<>();
        for (int i = 0; i < array.length - 1; i++) {
            BigDecimal minutes = new BigDecimal(String.valueOf(Duration.between(array[i], array[i + 1]).toMinutes()));
            BigDecimal scale = minutes.divide(totalMinutes, 3, RoundingMode.HALF_UP);
            BigDecimal multiply = scale.multiply(value).setScale(3, RoundingMode.HALF_UP);
            System.out.println("array = " + array[i]);
            dateTimeVal.put(array[i], multiply);
        }
        return dateTimeVal;
    }

    public List<LocalDateTime> splitPeriodToHours(LocalDateTime start, LocalDateTime end) {

        List<LocalDateTime> hours = new ArrayList<>();
        hours.add(start);
        LocalDateTime current = start;

        while (current.isBefore(end)) {
            LocalDateTime hourBoundary = current.plusHours(1).withMinute(0).withSecond(0).withNano(0);
            if (hourBoundary.isAfter(end)) {
                hourBoundary = end;
            }
            hours.add(hourBoundary);
            current = hourBoundary;
        }

        return hours;
    }
}
