package com.bobandata.cloud.trade.ml.term;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic.PvInverterDataMapper;
import com.bobandata.cloud.trade.ml.term.service.charge.ChargeInterfaceService;
import com.bobandata.cloud.trade.ml.term.service.charge.ChargeRecordsService;
import com.bobandata.cloud.trade.ml.term.service.charge.ChargeRecordsStatService;
import com.bobandata.cloud.trade.ml.term.service.contract.MlTermContractInfoService;
import com.bobandata.cloud.trade.ml.term.service.contract.dto.ContractDetailDto;
import com.bobandata.cloud.trade.ml.term.service.othermix.BalanceComputeService;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024-03-19日 14:53
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TradeMlTermApplication.class})
public class ITTestMlTermContract {

    @Autowired
    private MlTermContractInfoService contractInfoService;

    @Autowired
    private PvInverterDataMapper pvInverterDataMapper;

    @Autowired
    private ChargeRecordsStatService recordsStatService;

    @Autowired
    private ChargeRecordsService recordsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ChargeInterfaceService chargeInterfaceService;

    @Autowired
    private BalanceComputeService computeService;

    @Test
    public void testBalance() {
        Date balDate = Date.valueOf("2024-11-15");
        TimeSegment targetSegment = TwentyFourPointTimeSegment.valueOf("09:00-11:00");
        BigDecimal targetPower = new BigDecimal("1000");
        int type = 1;
        computeService.balance(balDate, targetSegment, targetPower, type, null);
    }

    @Test
    public void testDataEtl() {
        LambdaQueryWrapper<ChargeRecordsDo> query = Wrappers.lambdaQuery(
                ChargeRecordsDo.class);
        query.ge(ChargeRecordsDo::getStartTime, "2024-10-01")
             .le(ChargeRecordsDo::getStartTime, "2024-10-31");
        List<ChargeRecordsDo> recordsDos = recordsService.list(query);
        recordsStatService.dataEtl(recordsDos);
    }

    @Test
    public void testSelect() {
        List<KeyValueVo> keyValueVos = pvInverterDataMapper.selectEnergyGroupDate(
                Date.valueOf("2024-01-01"), Date.valueOf("2024-01-03"));
        System.out.println("keyValueVos = " + keyValueVos);
    }

    @Test
    public void testSelect1() {
        BigDecimal monthEnergy = pvInverterDataMapper.selectSumMonthEnergy();
        BigDecimal yearEnergy = pvInverterDataMapper.selectSumYearEnergy();
        System.out.println("monthEnergy = " + monthEnergy);
        System.out.println("yearEnergy = " + yearEnergy);
    }

    @Test
    public void testSelectJoin() {
        List<ContractDetailDto> contractDetailDtos = contractInfoService.listDetailByTradeId("1767370649955987456");
        System.out.println("contractDetailDtos = " + contractDetailDtos);
    }

    @Test
    public void readJsonFiles() {
        // 指定目录路径
        String directoryPath = "C:\\Users\\<USER>\\Desktop\\json";
        // 获取目录下的所有文件
        File directory = new File(directoryPath);
        File[] files = directory.listFiles((dir, name) -> name.toLowerCase().endsWith(".json"));
        if (files != null) {
            for (File file : files) {
                try {
                    // 读取文件内容
                    String content = new String(Files.readAllBytes(file.toPath()));

                    // 解析 JSON 内容
                    JsonNode rootNode = objectMapper.readTree(content);

                    // 提取 data 层的数据
                    JsonNode dataNode = rootNode.path("data");

                    // 将 data 转换为字符串
                    String dataAsString = dataNode.toString();
                    JavaType javaType = objectMapper.getTypeFactory()
                                                    .constructParametricType(ArrayList.class, ChargeRecordsDo.class);
                    List<ChargeRecordsDo> chargeRecordsDoList = objectMapper.readValue(
                            objectMapper.readTree(dataAsString).toString(), javaType);
                    // 更新、新增
                    chargeInterfaceService.saveOrUpdateBillsData(chargeRecordsDoList);
                    // 打印结果
                } catch (IOException e) {
                    System.err.println("Error reading file: " + file.getName());
                    e.printStackTrace();
                }
            }
        } else {
            System.out.println("No files found in the directory.");
        }
    }

}
