<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractFlowDoMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="guid" column="guid" jdbcType="VARCHAR"/>
        <result property="tradeSegId" column="tradeseq_id" jdbcType="VARCHAR"/>
        <result property="saleUnitsCode" column="sale_units_code" jdbcType="VARCHAR"/>
        <result property="saleUnitsName" column="sale_units_name" jdbcType="VARCHAR"/>
        <result property="vendeeUnitsCode" column="vendee_units_code" jdbcType="VARCHAR"/>
        <result property="vendeeUnitsName" column="vendee_units_name" jdbcType="VARCHAR"/>
        <result property="saleParticipantId" column="sale_participant_id" jdbcType="VARCHAR"/>
        <result property="saleParticipantName" column="sale_participant_name" jdbcType="VARCHAR"/>
        <result property="vendeeParticipantId" column="vendee_participant_id" jdbcType="VARCHAR"/>
        <result property="vendeeParticipantName" column="vendee_participant_name" jdbcType="VARCHAR"/>
        <result property="vendeeEnergy" column="vendee_energy" jdbcType="DECIMAL"/>
        <result property="saleEnergy" column="sale_energy" jdbcType="DECIMAL"/>
        <result property="vendeePrice" column="vendee_price" jdbcType="DECIMAL"/>
        <result property="salePrice" column="sale_price" jdbcType="DECIMAL"/>
        <result property="contractCreateTime" column="contract_create_time" jdbcType="TIMESTAMP"/>
        <result property="timeDivisionCode" column="time_division_code" jdbcType="VARCHAR"/>
        <result property="timeDivisionName" column="time_division_name" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="DATE"/>
        <result property="endTime" column="end_time" jdbcType="DATE"/>
        <result property="tradeseqCaption" column="tradeseq_caption" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>


</mapper>
