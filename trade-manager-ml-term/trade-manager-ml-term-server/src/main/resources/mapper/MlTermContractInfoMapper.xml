<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractInfoMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo">
        <id property="tradeId" column="trade_id" jdbcType="VARCHAR"/>
        <result property="dateYear" column="date_year" jdbcType="OTHER"/>
        <result property="dateMonth" column="date_month" jdbcType="INTEGER"/>
        <result property="tradeName" column="trade_name" jdbcType="VARCHAR"/>
        <result property="arrId" column="arr_id" jdbcType="VARCHAR"/>
        <result property="arrName" column="arr_name" jdbcType="VARCHAR"/>
        <result property="sellerUnitId" column="seller_unit_id" jdbcType="VARCHAR"/>
        <result property="sellerUnitName" column="seller_unit_name" jdbcType="VARCHAR"/>
        <result property="purchaseUnitId" column="purchase_unit_id" jdbcType="VARCHAR"/>
        <result property="purchaseUnitName" column="purchase_unit_name" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="contractEnergy" column="contract_energy" jdbcType="DECIMAL"/>
        <result property="contractPrice" column="contract_price" jdbcType="DECIMAL"/>
        <result property="tradeCycle" column="trade_cycle" jdbcType="VARCHAR"/>
        <result property="tradeType" column="trade_type" jdbcType="VARCHAR"/>
        <result property="isContract" column="is_contract" jdbcType="DECIMAL"/>
        <result property="srcTradeId" column="src_trade_id" jdbcType="VARCHAR"/>
        <result property="srcTradeName" column="src_trade_name" jdbcType="VARCHAR"/>
        <result property="srcArrId" column="src_arr_id" jdbcType="VARCHAR"/>
        <result property="srcArrName" column="src_arr_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>
    <update id="declare">
        update ml_settle_total_info set yuce_energy = #{energy} where data_time = #{time}
    </update>

</mapper>
