<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractEnergyMapper">

    <resultMap id="BaseResultMap"
               type="com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractEnergyDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tradeId" column="trade_id" jdbcType="VARCHAR"/>
        <result property="segCode" column="seg_code" jdbcType="DECIMAL"/>
        <result property="segName" column="seg_name" jdbcType="VARCHAR"/>
        <result property="segment" column="segment" jdbcType="VARCHAR"/>
        <result property="energy" column="energy" jdbcType="DECIMAL"/>
        <result property="price" column="price" jdbcType="DECIMAL"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>

</mapper>
