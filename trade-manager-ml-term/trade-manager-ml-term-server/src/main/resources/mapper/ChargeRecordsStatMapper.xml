<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeRecordsStatMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsStatDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="stationId" column="station_id" jdbcType="VARCHAR"/>
        <result property="stationName" column="station_name" jdbcType="VARCHAR"/>
        <result property="pileId" column="pile_id" jdbcType="VARCHAR"/>
        <result property="gunId" column="gun_id" jdbcType="VARCHAR"/>
        <result property="pileName" column="pile_name" jdbcType="VARCHAR"/>
        <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
        <result property="dataDate" column="data_date" jdbcType="DATE"/>
        <result property="v1" column="V1" jdbcType="DECIMAL"/>
        <result property="v2" column="V2" jdbcType="DECIMAL"/>
        <result property="v3" column="V3" jdbcType="DECIMAL"/>
        <result property="v4" column="V4" jdbcType="DECIMAL"/>
        <result property="v5" column="V5" jdbcType="DECIMAL"/>
        <result property="v6" column="V6" jdbcType="DECIMAL"/>
        <result property="v7" column="V7" jdbcType="DECIMAL"/>
        <result property="v8" column="V8" jdbcType="DECIMAL"/>
        <result property="v9" column="V9" jdbcType="DECIMAL"/>
        <result property="v10" column="V10" jdbcType="DECIMAL"/>
        <result property="v11" column="V11" jdbcType="DECIMAL"/>
        <result property="v12" column="V12" jdbcType="DECIMAL"/>
        <result property="v13" column="V13" jdbcType="DECIMAL"/>
        <result property="v14" column="V14" jdbcType="DECIMAL"/>
        <result property="v15" column="V15" jdbcType="DECIMAL"/>
        <result property="v16" column="V16" jdbcType="DECIMAL"/>
        <result property="v17" column="V17" jdbcType="DECIMAL"/>
        <result property="v18" column="V18" jdbcType="DECIMAL"/>
        <result property="v19" column="V19" jdbcType="DECIMAL"/>
        <result property="v20" column="V20" jdbcType="DECIMAL"/>
        <result property="v21" column="V21" jdbcType="DECIMAL"/>
        <result property="v22" column="V22" jdbcType="DECIMAL"/>
        <result property="v23" column="V23" jdbcType="DECIMAL"/>
        <result property="v24" column="V24" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,station_id,station_name,
        pile_id,pile_name,area_name,
        data_date,sum_power,sharp_power,
        peak_power,flat_power,valley_power,
        V1,V2,V3,
        V4,V5,V6,
        V7,V8,V9,
        V10,V11,V12,
        V13,V14,V15,
        V16,V17,V18,
        V19,V20,V21,
        V22,V23,V24,
        create_time,creator_id,last_refresh_time,
        last_modifier_id
    </sql>
</mapper>
