<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleBatchMapper">

    <resultMap id="BaseResultMap" type="com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
        <result property="dateYear" column="date_year" jdbcType="INTEGER"/>
        <result property="dateMonth" column="date_month" jdbcType="INTEGER"/>
        <result property="settleSubjectNo" column="settle_subject_no" jdbcType="VARCHAR"/>
        <result property="settleSubjectName" column="settle_subject_name" jdbcType="VARCHAR"/>
        <result property="tradePlanEnergy" column="trade_plan_energy" jdbcType="DECIMAL"/>
        <result property="settleEnergy" column="settle_energy" jdbcType="DECIMAL"/>
        <result property="settlePrice" column="settle_price" jdbcType="DECIMAL"/>
        <result property="settleFee" column="settle_fee" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="lastRefreshTime" column="last_refresh_time" jdbcType="TIMESTAMP"/>
        <result property="lastModifierId" column="last_modifier_id" jdbcType="BIGINT"/>
    </resultMap>


</mapper>
