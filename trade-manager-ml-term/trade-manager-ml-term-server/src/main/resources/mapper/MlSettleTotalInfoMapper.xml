<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleTotalInfoMapper">

    <select id="listByMonth"
            resultType="com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo">
        SELECT
        a.*,
        round( a.settle_energy / b.settle_energy * 100, 2 ) dlbl,
        round( a.settle_fee /( b.price_dif_electricity * 100000000 )* 100, 2 ) sybl
        FROM
        ml_settle_total_info a
        LEFT JOIN ml_settle_total_info_hn b ON a.data_time = concat(b.date_year,'-',b.date_month)
        WHERE 1 = 1
        <if test="date != null and date != ''">
           and a.data_time = #{date}
        </if>
    </select>
    <select id="listJyTotal"
            resultType="com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionRespVo">
        SELECT
            a.data_time AS dateTime,
            b.display_name AS displayName,
            a.power_plant_type AS powerPlantType,
            a.sale_units_name AS saleUnitsName,
            a.sale_participant_name AS saleParticipantName,
            a.green_rights_price AS greenRightPrice,
            IFNULL(a.jian_energy, 0) AS jianEnergy,
            IFNULL(a.feng_energy, 0) AS fengEnergy,
            IFNULL(a.ping_energy, 0) AS pingEnergy,
            IFNULL(a.gu_energy, 0) AS guEnergy,
            IFNULL(
                    (
                        a.jian_energy + a.feng_energy + a.ping_energy + a.gu_energy
                        ),
                    0
            ) AS totalEnergy,
            IFNULL(a.jian_price, 0) AS jianPrice,
            IFNULL(a.feng_price, 0) AS fengPrice,
            IFNULL(a.ping_price, 0) AS pingPrice,
            IFNULL(a.gu_price, 0) AS guPrice,
            IFNULL(
                    round(a.jian_energy * a.jian_price),
                    0
            ) AS jianEnergyPrice,
            IFNULL(
                    round(a.feng_energy * a.feng_price),
                    0
            ) AS fengEnergyPrice,
            IFNULL(
                    round(a.ping_energy * a.ping_price),
                    0
            ) AS pingEnergyPrice,
            IFNULL(
                    round(a.gu_energy * a.gu_price),
                    0
            ) AS guEnergyPrice,
            IFNULL(
                    (
                        round(a.jian_energy * a.jian_price) + round(a.feng_energy * a.feng_price) + round(a.ping_energy * a.ping_price) + round(a.gu_energy * a.gu_price)
                        ),
                    0
            ) AS totalEnergyPrice FROM
            trade_result_zcq_display b
                LEFT JOIN
--             #新华是卖方电量为负，新华买方电量为正。两个拼接起来的。
(
-- #买方
SELECT
 data_time,(case when(instr(display_name, '年度双边') > 0) then '年度双边'
when(instr(display_name, '年度合同电量调整') > 0) then '年度合同电量调整'
when(instr(display_name, '月度双边') > 0) then '月度双边'
when(instr(display_name, '月度竞价') > 0) then '月度竞价'
when(instr(display_name, '月内火电增补') > 0) then '月内火电增补'
when(instr(display_name, '省外燃煤挂牌') > 0) then '省外燃煤挂牌'
when(instr(display_name, '滚动撮合交易') > 0) then '滚动撮合交易'
when(instr(display_name, '月内合同转让') > 0) then '月内合同转让'
when(instr(display_name, '省外燃煤挂牌增补') > 0) then '省外燃煤挂牌增补'
when(instr(display_name, '月前合同转让') > 0) then '月前合同转让'
when(instr(display_name, '省内优先挂牌') > 0) then '省内优先挂牌'
when(instr(display_name, '售电公司增补挂牌') > 0) then '售电公司增补挂牌'
when(instr(display_name, '省外优先挂牌') > 0) then '省外优先挂牌' else null end) as display_name,
power_plant_type,
			sale_units_name,
			sale_participant_name,green_rights_price,sum(jian_energy) as jian_energy,sum(feng_energy) as feng_energy,sum(ping_energy) as ping_energy,sum(gu_energy) as gu_energy,max(jian_price) as jian_price,max(feng_price) as feng_price,max(ping_price) as ping_price,max(gu_price) as gu_price
 from (SELECT
			data_time,
			display_name,power_plant_type,
			sale_units_name,
			sale_participant_name,green_rights_price,

				CASE a.seg_name
				WHEN '尖峰' THEN
					a.trade_energy
				ELSE
					0
				END
			 jian_energy,

				CASE a.seg_name
				WHEN '高峰' THEN
					a.trade_energy
				ELSE
					0
				END
			 feng_energy,

				CASE a.seg_name
				WHEN '平段' THEN
					a.trade_energy
				ELSE
					0
				END
			 ping_energy,

				CASE a.seg_name
				WHEN '谷段' THEN
					a.trade_energy
				ELSE
					0
				END
			 gu_energy,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio1
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio1
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and (
		instr(display_name, '月内合同转让') > 0 OR
		instr(display_name, '增补') > 0  OR
		instr(display_name, '月度竞价') > 0
    ) and seg_name='尖峰'
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='尖峰'
) THEN round(trade_price,2)
WHEN (
		power_plant_type = '绿电'
    and seg_name='尖峰'
) THEN round(trade_price,2)
ELSE
	0
END jian_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio2
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio2
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and (
		instr(display_name, '月内合同转让') > 0 OR
		instr(display_name, '增补') > 0 OR
		instr(display_name, '月度竞价') > 0
    ) and seg_name='高峰'
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='高峰'
) THEN round(trade_price,2)
WHEN (
		power_plant_type = '绿电'
    and seg_name='高峰'
) THEN round(trade_price,2)
ELSE
	0
END feng_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio3
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio3
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and (
		instr(display_name, '月内合同转让') > 0 OR
		instr(display_name, '增补') > 0 OR
		instr(display_name, '月度竞价') > 0
    ) and seg_name='平段'
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='平段'
) THEN round(trade_price,2)
WHEN (
		power_plant_type = '绿电'
    and seg_name='平段'
) THEN round(trade_price,2)
ELSE
	0
END ping_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio4
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio4
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and (
		instr(display_name, '月内合同转让') > 0 OR
		instr(display_name, '增补') > 0 OR
		instr(display_name, '月度竞价') > 0
    ) and seg_name='谷段'
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='谷段'
) THEN round(trade_price,2)
WHEN (
		power_plant_type = '绿电'
    and seg_name='谷段'
) THEN round(trade_price,2)
ELSE
	0
END gu_price
		FROM
			(SELECT
					a.data_time,
					a.seg_name,
					display_name,
					round(sum(a.vendee_energy)) AS trade_energy,
					 a.vendee_price AS trade_price,green_rights_price,
					a.sale_units_name,
					a.power_plant_type,
					a.sale_participant_name
				FROM
					(SELECT
							a.data_time,
							(
								CASE time_division_name
								WHEN '尖峰' THEN
									'尖峰'
								WHEN '高峰' THEN
									'高峰'
								WHEN '平段' THEN
									'平段'
								WHEN '谷段' THEN
									'谷段'
								WHEN '段1' THEN
									'谷段'
								WHEN '段2' THEN
									'谷段'
								WHEN '段3' THEN
									'谷段'
								WHEN '段4' THEN
									'谷段'
								WHEN '段5' THEN
									'谷段'
								WHEN '段6' THEN
									'谷段'
								WHEN '段7' THEN
									'谷段'
								WHEN '段8' THEN
									'平段'
								WHEN '段9' THEN
									'平段'
								WHEN '段10' THEN
									'平段'
								WHEN '段11' THEN
									'平段'
								WHEN '段12' THEN
									'高峰'
								WHEN '段13' THEN
									'高峰'
								WHEN '段14' THEN
									'高峰'
								WHEN '段15' THEN
									'平段'
								WHEN '段16' THEN
									'平段'
								WHEN '段17' THEN
									'平段'
								WHEN '段18' THEN
									'平段'
								WHEN '段19' THEN
									'尖峰'
								WHEN '段20' THEN
									'尖峰'
								WHEN '段21' THEN
									'尖峰'
								WHEN '段22' THEN
									'尖峰'
								WHEN '段23' THEN
									'高峰'
								WHEN '段24' THEN
									'谷段'
								WHEN '1' THEN
									'尖峰'
								WHEN '2' THEN
									'高峰'
								WHEN '3' THEN
									'平段'
								WHEN '4' THEN
									'谷段'
								ELSE
									NULL
								END
							) AS seg_name,
							a.vendee_energy,
							a.vendee_price,
							a.sale_units_name,
							a.display_name,
							a.power_plant_type,
							a.sale_participant_name,green_rights_price
						FROM
							(
								SELECT
									CASE
								WHEN RIGHT (time_division_name, 2) REGEXP '^[0-9]+$' THEN
									time_division_name
								ELSE
									LEFT (time_division_name, 2)
								END AS time_division_name,
								LEFT (start_time, 7) AS data_time,
								vendee_energy,
								vendee_price,
								sale_units_name,
								CASE
							WHEN (
								instr(trade_caption, '年度') > 0
								AND instr(
									trade_caption,
									'双边协商'
								) > 0
							) THEN
								'年度双边'
							WHEN (
								instr(trade_caption, '月度') > 0
								AND instr(
									trade_caption,
									'双边协商'
								) > 0
							) THEN
								'月度双边'
							WHEN (
								instr(
									trade_caption,
									'火电月度集中竞价交易（二次分摊）'
								) > 0
								OR instr(
									trade_caption,
									'新能源月度集中竞价交易'
								) > 0
								OR instr(
									trade_caption,
									'煤电月度集中竞价'
								) > 0
							) THEN
								'月度竞价'
							WHEN instr(
								trade_caption,
								'月内增补交易（火电集中竞价）'
							) > 0 THEN
								'月内火电增补'
							WHEN instr(
								trade_caption,
								'月内增补交易（省外燃煤挂牌）'
							) > 0 THEN
								'省外燃煤挂牌增补'
							WHEN instr(
								trade_caption,
								'滚动撮合'
							) > 0 THEN
								CONCAT(STR_TO_DATE(SUBSTRING_INDEX(left(trade_caption,11),'日',1),'%Y年%m月%d'),'_','滚动撮合交易','_',vendee_participant_name,'_',id)
							WHEN instr(
								trade_caption,
								'面向售电公司挂牌交易'
							) > 0 THEN
								'省外燃煤挂牌'
							WHEN instr(
								trade_caption,
								'月度双边'
							) > 0 THEN
								'月度双边'
							WHEN instr(
								trade_caption,
								'月内市场合同转让'
							) > 0 THEN
								'月内合同转让'
							WHEN instr(
								trade_caption,
								'省内优先电量面向售电公司挂牌交易'
							) > 0 THEN
								'省内优先挂牌'
							WHEN instr(
								trade_caption,
								'售电公司月内增补挂牌交易'
							) > 0 THEN
								'售电公司增补挂牌'
							WHEN instr(
								trade_caption,
								'月优先电量面向售电公司挂牌交易'
							) > 0 THEN
								'省外优先挂牌'
							WHEN instr(
								trade_caption,
								'月前市场合同转让'
							) > 0 THEN
								'月前合同转让'
							ELSE
								''
							END display_name,
							CASE
						WHEN (
							instr(trade_caption, '火电') > 0
							OR instr(trade_caption, '燃煤') > 0
							OR instr(trade_caption, '煤电') > 0
							OR instr(elec_contract_id, '火电') > 0
							OR instr(elec_contract_id, '燃煤') > 0
							OR instr(sale_price, '-1.08') > 0
						) THEN
							'火电'
						WHEN (instr(trade_caption, '新能源') > 0 OR instr(elec_contract_id, '新能源') > 0)THEN
							'新能源'
						ELSE
							''
						END power_plant_type,

						sale_participant_name,
						'' green_rights_price
					FROM
						trade_result_zcq_do_hn
					WHERE
						LEFT (start_time, 7) = #{month}
					AND vendee_participant_name = '湖南新华供电有限公司'
					UNION
						SELECT
							period AS time_division_name,
							left(mkt_month,7) data_time,
							energy_t vendee_energy,
							price_m energy_price,
							SUBSTRING_INDEX(SUBSTRING_INDEX(contract_name,'电力交易',-1),'与',1) as sale_units_name,
							'月度双边' display_name,
							'绿电' power_plant_type,
							'' sale_participant_name,
							green_rights_price
						FROM
							trade_green_power_hn
						WHERE
							LEFT (mkt_month, 7) = #{month}
						AND sell_participant_name = '湖南新华供电有限公司'
							) a) a
				GROUP BY
					data_time,
					seg_name,
					display_name,
					a.sale_units_name,
					a.power_plant_type,
					a.sale_participant_name
			) a) a
		GROUP BY
			data_time,
			display_name,
			power_plant_type,
			sale_participant_name,
			sale_units_name
-- #卖方
UNION
select data_time,
			(case when(instr(display_name, '年度双边') > 0) then '年度双边'
when(instr(display_name, '年度合同电量调整') > 0) then '年度合同电量调整'
when(instr(display_name, '月度双边') > 0) then '月度双边'
when(instr(display_name, '月度竞价') > 0) then '月度竞价'
when(instr(display_name, '月内火电增补') > 0) then '月内火电增补'
when(instr(display_name, '省外燃煤挂牌') > 0) then '省外燃煤挂牌'
when(instr(display_name, '滚动撮合交易') > 0) then '滚动撮合交易'
when(instr(display_name, '月内合同转让') > 0) then '月内合同转让'
when(instr(display_name, '省外燃煤挂牌增补') > 0) then '省外燃煤挂牌增补'
when(instr(display_name, '月前合同转让') > 0) then '月前合同转让'
when(instr(display_name, '省内优先挂牌') > 0) then '省内优先挂牌'
when(instr(display_name, '售电公司增补挂牌') > 0) then '售电公司增补挂牌'
when(instr(display_name, '省外优先挂牌') > 0) then '省外优先挂牌' else null end) as display_name,power_plant_type,
			sale_units_name,
			sale_participant_name,green_rights_price,sum(jian_energy) as jian_energy,sum(feng_energy) as feng_energy,sum(ping_energy) as ping_energy,sum(gu_energy) as gu_energy,max(jian_price) as jian_price,max(feng_price) as feng_price,max(ping_price) as ping_price,max(gu_price) as gu_price
 from (SELECT
			data_time,
			display_name,power_plant_type,
			sale_units_name,
			sale_participant_name,'' as green_rights_price,
				CASE a.seg_name
				WHEN '尖峰' THEN
					a.trade_energy
				ELSE
					0
				END
			 jian_energy,

				CASE a.seg_name
				WHEN '高峰' THEN
					a.trade_energy
				ELSE
					0
				END
			 feng_energy,

				CASE a.seg_name
				WHEN '平段' THEN
					a.trade_energy
				ELSE
					0
				END
			 ping_energy,

				CASE a.seg_name
				WHEN '谷段' THEN
					a.trade_energy
				ELSE
					0
				END
			 gu_energy,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio1
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio1
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and seg_name='尖峰' and (
		instr(display_name, '月内合同转让') > 0
    )
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='尖峰'
) THEN round(trade_price,2)
ELSE
	0
END jian_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio2
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio2
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and seg_name='高峰' and (display_name='月内合同转让') )
 THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='高峰'
) THEN round(trade_price,2)
ELSE
	0
END feng_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio3
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio3
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and
		instr(display_name, '月内合同转让') > 0
    and seg_name='平段'
) THEN round(trade_price,2)
WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='平段'
) THEN round(trade_price,2)
ELSE
	0
END ping_price,
CASE
WHEN power_plant_type = '新能源' THEN
	(
		SELECT
			ratio4
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 1
		AND data_time = #{month}
	)
WHEN (
	power_plant_type = '火电'
	AND (
		instr(display_name, '双边') > 0
		OR instr(display_name, '年度竞价') > 0
		OR instr(display_name,'月度转让') > 0
		OR instr(display_name,'月前合同转让') > 0
	)
) THEN
	(
		SELECT
			ratio4
		FROM
			corp_cons_ratio_transaction
		WHERE
			type = 2
		AND data_time = #{month}
	)
WHEN (power_plant_type = '火电' and(
		instr(display_name, '月内合同转让') > 0
    and seg_name='谷段')
) THEN round(trade_price,2)

WHEN (
		instr(display_name, '滚动撮合') > 0
    and seg_name='谷段'
) THEN round(trade_price,2)
ELSE
	0
END gu_price
		FROM
			(SELECT
					a.data_time,
					a.seg_name,
					display_name,
					round(sum(- a.sale_energy)) AS trade_energy,
					 a.sale_price AS trade_price,
					a.sale_units_name,
					a.power_plant_type,
					a.sale_participant_name
				FROM
					(SELECT
							a.data_time,
							(
								CASE time_division_name
								WHEN '尖峰' THEN
									'尖峰'
								WHEN '高峰' THEN
									'高峰'
								WHEN '平段' THEN
									'平段'
								WHEN '谷段' THEN
									'谷段'
								WHEN '段1' THEN
									'谷段'
								WHEN '段2' THEN
									'谷段'
								WHEN '段3' THEN
									'谷段'
								WHEN '段4' THEN
									'谷段'
								WHEN '段5' THEN
									'谷段'
								WHEN '段6' THEN
									'谷段'
								WHEN '段7' THEN
									'谷段'
								WHEN '段8' THEN
									'平段'
								WHEN '段9' THEN
									'平段'
								WHEN '段10' THEN
									'平段'
								WHEN '段11' THEN
									'平段'
								WHEN '段12' THEN
									'高峰'
								WHEN '段13' THEN
									'高峰'
								WHEN '段14' THEN
									'高峰'
								WHEN '段15' THEN
									'平段'
								WHEN '段16' THEN
									'平段'
								WHEN '段17' THEN
									'平段'
								WHEN '段18' THEN
									'平段'
								WHEN '段19' THEN
									'尖峰'
								WHEN '段20' THEN
									'尖峰'
								WHEN '段21' THEN
									'尖峰'
								WHEN '段22' THEN
									'尖峰'
								WHEN '段23' THEN
									'高峰'
								WHEN '段24' THEN
									'谷段'
								ELSE
									NULL
								END
							) AS seg_name,
							a.sale_energy,
							a.sale_price,
							a.sale_units_name,
							a.display_name,
							a.power_plant_type,
							a.sale_participant_name
						FROM
							(
								SELECT id,
									CASE
								WHEN RIGHT (time_division_name, 2) REGEXP '^[0-9]+$' THEN
									time_division_name
								ELSE
									LEFT (time_division_name, 2)
								END AS time_division_name,
								LEFT (start_time, 7) AS data_time,
								sale_energy,
								sale_price,
								sale_units_name,
								CASE
							WHEN (
								instr(trade_caption, '年度') > 0
								AND instr(
									trade_caption,
									'双边协商'
								) > 0
							) THEN
								'年度双边'
							WHEN (
								instr(trade_caption, '月度') > 0
								AND instr(
									trade_caption,
									'双边协商'
								) > 0
							) THEN
								'月度双边'
							WHEN (
								instr(
									trade_caption,
									'火电月度集中竞价交易（二次分摊）'
								) > 0
								OR instr(
									trade_caption,
									'新能源月度集中竞价交易'
								) > 0
								OR instr(
									trade_caption,
									'煤电月度集中竞价'
								) > 0
							) THEN
								'月度竞价'
							WHEN instr(
								trade_caption,
								'月内增补交易（火电集中竞价）'
							) > 0 THEN
								'月内火电增补'
							WHEN instr(
								trade_caption,
								'月内增补交易（省外燃煤挂牌）'
							) > 0 THEN
								'省外燃煤挂牌增补'
							WHEN instr(
								trade_caption,
								'滚动撮合'
							) > 0 THEN
								CONCAT(STR_TO_DATE(SUBSTRING_INDEX(left(trade_caption,11),'日',1),'%Y年%m月%d'),'_','滚动撮合交易','_',vendee_participant_name,'_',id)
							WHEN instr(
								trade_caption,
								'面向售电公司挂牌交易'
							) > 0 THEN
								'省外燃煤挂牌'
							WHEN instr(
								trade_caption,
								'月度双边'
							) > 0 THEN
								'月度双边'
							WHEN instr(
								trade_caption,
								'月内市场合同转让'
							) > 0 THEN
								'月内合同转让'
							WHEN instr(
								trade_caption,
								'省内优先电量面向售电公司挂牌交易'
							) > 0 THEN
								'省内优先挂牌'
							WHEN instr(
								trade_caption,
								'售电公司月内增补挂牌交易'
							) > 0 THEN
								'售电公司增补挂牌'
							WHEN instr(
								trade_caption,
								'月优先电量面向售电公司挂牌交易'
							) > 0 THEN
								'省外优先挂牌'
							WHEN instr(
								trade_caption,
								'月前市场合同转让'
							) > 0 THEN
								'月前合同转让'
							ELSE
								''
							END display_name,
							CASE
						WHEN (
							instr(trade_caption, '火电') > 0
							OR instr(trade_caption, '燃煤') > 0
							OR instr(trade_caption, '煤电') > 0
							OR instr(elec_contract_id, '火电') > 0
							OR instr(elec_contract_id, '燃煤') > 0
							OR instr(sale_price, '-1.08') > 0
						) THEN
							'火电'
						WHEN (instr(trade_caption, '新能源') > 0 OR instr(elec_contract_id, '新能源') > 0)THEN
							'新能源'
						ELSE
							''
						END power_plant_type,


						sale_participant_name
					FROM
						trade_result_zcq_do_hn
					WHERE
						LEFT (start_time, 7) = #{month}
					AND sale_participant_name = '湖南新华供电有限公司'
							) a) a
				GROUP BY
					data_time,
					seg_name,
					display_name,
					a.sale_units_name,
					a.power_plant_type,
					a.sale_participant_name
			) a) a
		GROUP BY
			data_time,
			display_name,
			power_plant_type,
			sale_participant_name,
			sale_units_name
) a ON (b.display_name = a.display_name)
        WHERE
            1 = 1
        <if test="displayName != null and displayName != ''">
            and b.display_name = #{displayName}
        </if>
        <if test="plantType != null and plantType != ''">
            and a.power_plant_type = #{plantType}
        </if>
        ORDER BY
            b.sort,
            a.data_time,a.power_plant_type
    </select>
    <select id="listJyPcTotal"
            resultType="com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo">
        SELECT
            a.*,
            IFNULL( round( a.pcdl3 * a.fpckhdj, 2 ), 0 ) bkhdf
        FROM
            (
                SELECT
                    a.data_time dateTime,
                    round(
                            IFNULL( a.contract_energy, 0 )) AS contractEnergy,
                    round(
                            IFNULL( b.settle_energy, 0 )) AS greenEnergy,
                    IFNULL( a.settle_energy, 0 ) AS settleEnergy,
                    IFNULL( c.value1, 0 ) AS gdjjc,
                    IFNULL( c.value2, 0 ) AS gwdlgdjc,
                    IFNULL( c.value3, 0 ) AS scjjc,
                    IFNULL(( a.settle_energy - a.contract_energy ), 0 ) AS pcdl,
                    CASE

                        WHEN ABS( a.settle_energy - a.contract_energy ) > a.contract_energy * 0.03
                            AND ( a.settle_energy - a.contract_energy )> 0 THEN
                            round( a.settle_energy - a.contract_energy * 1.03, 3 )
                        WHEN ABS( a.settle_energy - a.contract_energy ) > a.contract_energy * 0.03
                            AND 0 >=(
                                a.settle_energy - a.contract_energy
                                ) THEN
                            round( a.settle_energy - a.contract_energy * 0.97, 3 ) ELSE 0
                        END pcdl3,
                    IFNULL( CONCAT( TRUNCATE (( a.settle_energy - a.contract_energy )/ a.contract_energy * 100, 2 ), '%' ), 0 ) AS pcl,
                    IFNULL(d.k1,0) k1,
                    IFNULL(d.k2,0) k2,
                    IFNULL(round(( d.stjj * d.k1 )- 450, 4 ),0) zpckhdj,
                    IFNULL(CASE
                               d.k2
                               WHEN 0.1 THEN
                                   d.k2 * 30 ELSE d.k2
                               END,0) fpckhdj,
                    round( IFNULL( a.contract_energy * 2.5, 0 ), 2 ) sdsr
                FROM ml_settle_total_info a left join
        (select settle_energy,data_time from ml_settle_batch b where  settle_subject_no = '1010202') b
        on a.data_time = b.data_time
        left join
        (select data_time,value1,value2,value3 from jsc_hnxh where data_type = '交易价格') c
        on a.data_time = c.data_time
        left join corp_cons_ratio d on a.data_time = d.data_time
        ) a where LEFT(a.dateTime,4) = #{year} order by a.dateTime
    </select>
</mapper>
