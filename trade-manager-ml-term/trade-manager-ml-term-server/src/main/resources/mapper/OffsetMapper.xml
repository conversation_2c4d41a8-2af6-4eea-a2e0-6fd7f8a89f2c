<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bobandata.cloud.trade.ml.term.dal.mysql.offset.OffsetMapper">


    <select id="listDlTotal" resultType="com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetRespVo">
        SELECT
        a.data_date dataTime,
        round(a.energy1,3) AS highestEnergy,
        round(a.energy2,3) AS peakEnergy,
        round(a.energy3,3) AS flatEnergy,
        round(a.energy4,3) AS valleyEnergy,
        round(a.total_battery,3) AS totalEnergy
        FROM
        (
        SELECT
        a.data_date,
        sum(a.total_battery) / 1000 AS total_battery,
        sum(a.energy1) / 1000 AS energy1,
        sum(a.energy2) / 1000 AS energy2,
        sum(a.energy3) / 1000 AS energy3,
        sum(a.energy4) / 1000 AS energy4
        FROM
        corp_cons_real_energy_hunan a
        LEFT JOIN corp_cons b ON a.cons_no = b.cons_no
        WHERE
        a.data_date >= #{startTime} and #{endTime} >=a.data_date
        <if test="ids != null and ids.size()>0" >
            and  a.cons_no in
            <foreach collection="ids" item="id" separator="," open="(" close=")" index="index">
                #{id}
            </foreach>
        </if>
        group by a.data_date
        ) a
    </select>
</mapper>
