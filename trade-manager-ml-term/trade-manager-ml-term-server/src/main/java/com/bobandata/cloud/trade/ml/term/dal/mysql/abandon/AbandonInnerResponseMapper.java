package com.bobandata.cloud.trade.ml.term.dal.mysql.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonInnerResponseDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonInnerResponseDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 11:56
 * @Classname AbandonInnerResponseMapper
 * @Description
 */
public interface AbandonInnerResponseMapper extends BaseCrudMapper<AbandonInnerResponseDo> {
    default PagingResult<AbandonInnerResponseDo> selectPage(AbandonReleaseInfoReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<AbandonInnerResponseDo>()
            .eqIfPresent(AbandonInnerResponseDo::getRunDate, reqVO.getRunDate())
            .orderByDesc(AbandonInnerResponseDo::getRunDate));
    }
}
