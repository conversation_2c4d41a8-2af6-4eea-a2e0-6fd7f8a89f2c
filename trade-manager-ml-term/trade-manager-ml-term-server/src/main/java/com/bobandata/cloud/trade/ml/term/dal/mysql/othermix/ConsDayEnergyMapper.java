package com.bobandata.cloud.trade.ml.term.dal.mysql.othermix;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ConsDayEnergyReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ConsDayEnergyDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;

import java.sql.Date;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/10/29 15:35
 * @Classname ConsDayEnergyMapper
 * @Description
 */
public interface ConsDayEnergyMapper extends BaseCrudMapper<ConsDayEnergyDo> {

    default List<ConsDayEnergyDo> selectList(ConsDayEnergyReqVo reqVO) {
        QueryWrapper<ConsDayEnergyDo> queryWrapper = new QueryWrapper<>();
        Optional.ofNullable(reqVO.getCurveDate()).ifPresent(curveDate -> queryWrapper.eq("this_ymd", curveDate));
        Optional.ofNullable(reqVO.getConsNo()).ifPresent(s -> queryWrapper.like("cons_no", StrUtil.format("%{}%", s)));
        Optional.ofNullable(reqVO.getConsName())
            .ifPresent(s -> queryWrapper.like("cons_name", StrUtil.format("%{}%", s)));
        queryWrapper.orderByDesc("this_ymd");
        queryWrapper.orderByAsc("cons_name");
        return this.selectList(queryWrapper);
    }

}
