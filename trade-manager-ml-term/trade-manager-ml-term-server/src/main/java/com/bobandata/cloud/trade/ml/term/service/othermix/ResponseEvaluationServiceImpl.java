package com.bobandata.cloud.trade.ml.term.service.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ProxyUserInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ResponseEvaluationDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.UserResponseDetailsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.othermix.ProxyUserInfoMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.othermix.ResponseEvaluationMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.othermix.UserResponseDetailsMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:35
 * @Classname ResponseEvaluationServiceImpl
 * @Description
 */
@Service
public class ResponseEvaluationServiceImpl implements ResponseEvaluationService{

    @Resource
    private ResponseEvaluationMapper responseEvaluationMapper;

    @Resource
    private UserResponseDetailsMapper userResponseDetailsMapper;

    @Resource
    private ProxyUserInfoMapper proxyUserInfoMapper;

    @Override
    public PagingResult<ResponseEvaluationDo> getResponseEvaluationPage(ResponseEvaluationReqVo reqVo) {
        return responseEvaluationMapper.selectPage(reqVo);
    }

    @Override
    public PagingResult<UserResponseDetailsDo> getUserResponseDetailsPage(ResponseEvaluationReqVo reqVo) {
        return userResponseDetailsMapper.selectPage(reqVo);
    }

    @Override
    public PagingResult<ProxyUserInfoDo> getProxyUserInfoPage(ResponseEvaluationReqVo reqVo) {
        return proxyUserInfoMapper.selectPage(reqVo);
    }
}
