package com.bobandata.cloud.trade.ml.term.dal.dataobject.settle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.time.YearMonth;
import lombok.Data;

/**
 * 中长期交易：结算汇总单
 *
 * @TableName ml_settle_total_info
 */
@TableName(value = "ml_settle_total_info")
@Data
public class MlSettleTotalInfoDo extends BaseDo {

    /**
     * 默认主键字段id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属区域
     */
    @TableField(value = "org_no")
    private String orgNo;


    /**
     * 所属年份
     */
    @TableField(value = "data_time")
    private String dataTime;

    /**
     * 所属年份
     */
    @TableField(exist = false)
    private Integer dateYear;

    /**
     * 所属月份
     */
    @TableField(exist = false)
    private Integer dateMonth;

    /**
     * 实际用电量
     */
    @TableField(value = "actual_energy")
    private BigDecimal actualEnergy;

    /**
     * 结算电量
     */
    @TableField(value = "settle_energy")
    private BigDecimal settleEnergy;

    /**
     * 合同电量
     */
    @TableField(value = "contract_energy")
    private BigDecimal contractEnergy;

    /**
     * 偏差用电量
     */
    @TableField(value = "offset_energy")
    private BigDecimal offsetEnergy;

    /**
     * 结算电费
     */
    @TableField(value = "settle_fee")
    private BigDecimal settleFee;

    /**
     * 预测电量
     */
    @TableField(value = "yuce_energy")
    private BigDecimal yuceEnergy;

    /**
     * 电量比例
     */
    @TableField(exist = false)
    private String dlbl;

    /**
     * 收益比例
     */
    @TableField(exist = false)
    private String sybl;

    public String getSettleMonth() {
        return dataTime;
    }
}