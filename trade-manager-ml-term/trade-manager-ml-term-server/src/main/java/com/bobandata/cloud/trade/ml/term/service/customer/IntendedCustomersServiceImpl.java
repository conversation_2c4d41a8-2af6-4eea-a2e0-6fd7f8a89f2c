package com.bobandata.cloud.trade.ml.term.service.customer;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoRespVo;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.ConsRespVo;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.IntendedCustomersReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.Cons;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.IntendedCustomers;
import com.bobandata.cloud.trade.ml.term.dal.mysql.customer.ConsMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.customer.IntendedCustomersMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service实现
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class IntendedCustomersServiceImpl extends BaseServiceImpl<IntendedCustomersMapper, IntendedCustomers> implements IntendedCustomersService {

    @Autowired
    IntendedCustomersMapper intendedCustomersMapper;

    @Autowired
    ConsMapper consMapper;
    

    @Override
    public ServiceResult<Boolean> insert(IntendedCustomers intendedCustomers) {
        if(this.getBaseMapper().insert(intendedCustomers)>0){
            return ServiceResult.success(true);
        }
        return ServiceResult.success(true);
    }

    @Override
    public PagingResult<IntendedCustomers> listIntendedCustomer(IntendedCustomersReqVo reqVo) {
        return this.getBaseMapper().selectPage(reqVo);
    }

    @Override
    public ServiceResult<Boolean> updateIntendedCustomer(IntendedCustomers intendedCustomers) {
        return ServiceResult.success(this.getBaseMapper().updateById(intendedCustomers)>0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(this.getBaseMapper().deleteById(id)>0);
    }

    @Override
    public ServiceResult<List<ConsRespVo>> getConsNameAndNo(String key) {
        List<Map> maps = intendedCustomersMapper.getConsName(key);
        ArrayList<ConsRespVo> list = new ArrayList<>();
        for(int j = 0;j<maps.size();j++){
            Map m = maps.get(j);
            ConsRespVo consRespVo = new ConsRespVo();
            List<Long> ids = Arrays.stream(m.get("ids").toString().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());;
            List<String> cons = Arrays.asList(m.get("cons").toString().split(","));
            ArrayList<Map> maps1 = new ArrayList<>();
            for(int i = 0;i<ids.size();i++){
                HashMap map = new HashMap<>();
                map.put("id",ids.get(i));
                map.put("caption",cons.get(i));
                maps1.add(map);
            }
            consRespVo.setId(Long.valueOf(j+1));
            consRespVo.setChildren(maps1);
            consRespVo.setCaption(m.get("caption").toString());
            list.add(consRespVo);
        }
        return ServiceResult.success(list);
    }

    @Override
    public ServiceResult<Boolean> toCons(Long id) {
        IntendedCustomers intendedCustomers = intendedCustomersMapper.selectById(id);
        Cons cons = new Cons();
        BeanUtils.copyProperties(intendedCustomers,cons);
        cons.setId(null);
        cons.setCaption(intendedCustomers.getConsName());
        return ServiceResult.success(consMapper.insert(cons)>0);
    }

    @Override
    public List<Map> getLevel() {
        return consMapper.getLevel();
    }

    @Override
    public List<Map> getYd() {
        return consMapper.getYd();
    }
}




