package com.bobandata.cloud.trade.ml.term.service.contract;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractEnergyDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractEnergyMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_energy_test(中长期交易：交易合同拆分信息)】的数据库操作Service实现
 * @createDate 2024-03-12 15:35:24
 */
@Service
public class MlTermContractEnergyServiceImpl extends BaseServiceImpl<MlTermContractEnergyMapper, MlTermContractEnergyDo> implements MlTermContractEnergyService {

}




