package com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-11-04日 15:10
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
@Schema(description = "日电量统计")
public class DayEnergyVo {

    private String curveDate;

    private String sumEnergy;
}
