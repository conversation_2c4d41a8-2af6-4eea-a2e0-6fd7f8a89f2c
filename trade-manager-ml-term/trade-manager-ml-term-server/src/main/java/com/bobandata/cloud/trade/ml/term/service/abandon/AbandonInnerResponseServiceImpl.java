package com.bobandata.cloud.trade.ml.term.service.abandon;

import cn.hutool.core.date.DateTime;
import com.bobandata.cloud.common.enums.ErrorCode;
import com.bobandata.cloud.common.exception.ServiceException;
import com.bobandata.cloud.common.exception.util.ServiceExceptionUtil;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonInnerResponseDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonReleaseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.AbandonInnerResponseMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.AbandonReleaseInfoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:01
 * @Classname AbandonInnerResponseServiceImpl
 * @Description
 */
@Service
public class AbandonInnerResponseServiceImpl extends BaseServiceImpl<AbandonInnerResponseMapper, AbandonInnerResponseDo> implements AbandonInnerResponseService {

    @Autowired
    private AbandonReleaseInfoMapper releaseInfoMapper;

    @Override
    public PagingResult<AbandonInnerResponseDo> getAbandonInnerResponsePage(AbandonReleaseInfoReqVo reqVo) {
        return this.getBaseMapper().selectPage(reqVo);
    }

    @Override
    public ServiceResult updFeedBackStatus(Long id) {
        AbandonInnerResponseDo responseDo = this.getBaseMapper().selectById(id);
        if (responseDo == null) {
            return ServiceResult.error(1001, "未查询到数据");
        }
        if (responseDo.getFeedStatus() != null && responseDo.getFeedStatus() == 1) {
            return ServiceResult.error(1002, "数据已反馈");
        }
        List<AbandonReleaseInfoDo> releaseInfoDos = releaseInfoMapper.selectList("trade_no", responseDo.getTradeNo());
        if (CollectionUtils.isEmpty(releaseInfoDos)) {
            return ServiceResult.error(1003, "未查询到数据");
        }
        if (releaseInfoDos.get(0).getEndFeedbackTime() != null && releaseInfoDos.get(0).getEndFeedbackTime().before(DateTime.now())) {
            return ServiceResult.error(1004, "反馈时间已过");
        }
        responseDo.setFeedStatus(1);
        responseDo.setFeedbackTime(new Timestamp(System.currentTimeMillis()));
        this.getBaseMapper().updateById(responseDo);
        return ServiceResult.success();
    }
}
