package com.bobandata.cloud.trade.ml.term.dal.dataobject.charge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.curve.core.util.DataCurveUtil;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.Data;

/**
 * 充电桩电量数据汇总
 *
 * @TableName charge_records_stat
 */
@TableName(value = "charge_records_stat")
@Data
public class ChargeRecordsStatDo extends BaseDo {

    /**
     * 充电站ID
     */
    @TableField(value = "station_id")
    private String stationId;

    /**
     * 站点名称
     */
    @TableField(value = "station_name")
    private String stationName;

    /**
     * 充电桩ID
     */
    @TableField(value = "pile_id")
    private String pileId;

    @TableField(value = "pile_no")
    private String pileNo;

    @TableField(value = "gun_id")
    private String gunId;

    /**
     * 充电桩名称
     */
    @TableField(value = "pile_name")
    private String pileName;

    /**
     * 区域
     */
    @TableField(value = "area_name")
    private String areaName;

    /**
     * 时间
     */
    @TableField(value = "data_date")
    private Date dataDate;

    /**
     *
     */
    @TableField(value = "V1")
    private BigDecimal v1;

    /**
     *
     */
    @TableField(value = "V2")
    private BigDecimal v2;

    /**
     *
     */
    @TableField(value = "V3")
    private BigDecimal v3;

    /**
     *
     */
    @TableField(value = "V4")
    private BigDecimal v4;

    /**
     *
     */
    @TableField(value = "V5")
    private BigDecimal v5;

    /**
     *
     */
    @TableField(value = "V6")
    private BigDecimal v6;

    /**
     *
     */
    @TableField(value = "V7")
    private BigDecimal v7;

    /**
     *
     */
    @TableField(value = "V8")
    private BigDecimal v8;

    /**
     *
     */
    @TableField(value = "V9")
    private BigDecimal v9;

    /**
     *
     */
    @TableField(value = "V10")
    private BigDecimal v10;

    /**
     *
     */
    @TableField(value = "V11")
    private BigDecimal v11;

    /**
     *
     */
    @TableField(value = "V12")
    private BigDecimal v12;

    /**
     *
     */
    @TableField(value = "V13")
    private BigDecimal v13;

    /**
     *
     */
    @TableField(value = "V14")
    private BigDecimal v14;

    /**
     *
     */
    @TableField(value = "V15")
    private BigDecimal v15;

    /**
     *
     */
    @TableField(value = "V16")
    private BigDecimal v16;

    /**
     *
     */
    @TableField(value = "V17")
    private BigDecimal v17;

    /**
     *
     */
    @TableField(value = "V18")
    private BigDecimal v18;

    /**
     *
     */
    @TableField(value = "V19")
    private BigDecimal v19;

    /**
     *
     */
    @TableField(value = "V20")
    private BigDecimal v20;

    /**
     *
     */
    @TableField(value = "V21")
    private BigDecimal v21;

    /**
     *
     */
    @TableField(value = "V22")
    private BigDecimal v22;

    /**
     *
     */
    @TableField(value = "V23")
    private BigDecimal v23;

    /**
     *
     */
    @TableField(value = "V24")
    private BigDecimal v24;

    public void setPointValue(int hour, BigDecimal value) {
        DataCurveUtil.setPointValue(this, value, hour + 1, "v");
    }
}