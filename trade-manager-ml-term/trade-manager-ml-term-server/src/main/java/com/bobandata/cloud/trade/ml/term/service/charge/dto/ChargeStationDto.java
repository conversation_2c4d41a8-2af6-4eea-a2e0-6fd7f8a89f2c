package com.bobandata.cloud.trade.ml.term.service.charge.dto;

import com.bobandata.cloud.trade.curve.core.internal.DataPointCurve;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-11-05日 10:33
 * @description
 */
@Getter
@Setter
@ToString
public class ChargeStationDto implements DataPointCurve {

    private Date dataDate;

    private BigDecimal v1;
    private BigDecimal v2;
    private BigDecimal v3;
    private BigDecimal v4;
    private BigDecimal v5;
    private BigDecimal v6;
    private BigDecimal v7;
    private BigDecimal v8;
    private BigDecimal v9;
    private BigDecimal v10;
    private BigDecimal v11;
    private BigDecimal v12;
    private BigDecimal v13;
    private BigDecimal v14;
    private BigDecimal v15;
    private BigDecimal v16;
    private BigDecimal v17;
    private BigDecimal v18;
    private BigDecimal v19;
    private BigDecimal v20;
    private BigDecimal v21;
    private BigDecimal v22;
    private BigDecimal v23;
    private BigDecimal v24;

    @Override
    public String getDataKey() {
        return "电量曲线";
    }

    @Override
    public String getPrefix() {
        return "v";
    }
}
