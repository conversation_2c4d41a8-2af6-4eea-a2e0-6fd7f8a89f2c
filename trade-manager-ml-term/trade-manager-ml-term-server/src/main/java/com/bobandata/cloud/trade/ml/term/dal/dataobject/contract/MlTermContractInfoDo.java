package com.bobandata.cloud.trade.ml.term.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 中长期交易：交易合同基本信息
 *
 * @TableName ml_term_contract_info_test
 */
@TableName(value = "ml_term_contract_info")
@Data
@EqualsAndHashCode
@ToString
public class MlTermContractInfoDo extends BaseDo {

    @TableField(exist = false)
    private Long id;
    /**
     * 交易ID
     */
    @TableId(value = "trade_id")
    private String tradeId;

    /**
     * 所属年份
     */
    @TableField(value = "date_year")
    private Integer dateYear;

    /**
     * 所属月份
     */
    @TableField(value = "date_month")
    private Integer dateMonth;

    /**
     * 交易名称
     */
    @TableField(value = "trade_name")
    private String tradeName;

    /**
     * 交易批次
     */
    @TableField(value = "arr_id")
    private String arrId;

    /**
     * 交易批次名称
     */
    @TableField(value = "arr_name")
    private String arrName;

    /**
     * 卖方
     */
    @TableField(value = "seller_unit_id")
    private String sellerUnitId;

    /**
     *
     */
    @TableField(value = "seller_unit_name")
    private String sellerUnitName;

    /**
     * 买方
     */
    @TableField(value = "purchase_unit_id")
    private String purchaseUnitId;

    /**
     *
     */
    @TableField(value = "purchase_unit_name")
    private String purchaseUnitName;

    /**
     * 合同开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 合同结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 合同电量
     */
    @TableField(value = "contract_energy")
    private BigDecimal contractEnergy;

    /**
     * 合同加权平均电价
     */
    @TableField(value = "contract_price")
    private BigDecimal contractPrice;

    /**
     *
     */
    @TableField(value = "trade_cycle")
    private String tradeCycle;

    /**
     *
     */
    @TableField(value = "trade_type")
    private String tradeType;

    /**
     *
     */
    @TableField(value = "is_contract")
    private Integer isContract;

    /**
     *
     */
    @TableField(value = "src_trade_id")
    private String srcTradeId;

    /**
     *
     */
    @TableField(value = "src_trade_name")
    private String srcTradeName;

    /**
     *
     */
    @TableField(value = "src_arr_id")
    private String srcArrId;

    /**
     *
     */
    @TableField(value = "src_arr_name")
    private String srcArrName;

    public YearMonth getDateMonth() {
        return YearMonth.of(dateYear, dateMonth);
    }
}