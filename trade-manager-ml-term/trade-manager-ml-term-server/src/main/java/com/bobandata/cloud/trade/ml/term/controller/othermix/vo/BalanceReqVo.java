package com.bobandata.cloud.trade.ml.term.controller.othermix.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-11-18日 09:28
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(description = "需求响应-响应计算")
public class BalanceReqVo {

    @Schema(description = "响应时间", example = "2024-01-01")
    private Date balDate;

    @Schema(description = "缺口信息")
    private List<BalanceInfoNode> balanceInfoNodes;

    @Schema(description = "需求响应-可选用户户号")
    private List<String> consNos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    @Schema(description = "需求响应-缺口信息")
    public static class BalanceInfoNode {

        @Schema(description = "需求响应-响应时段", example = "00:00-24:00")
        private String targetSegment;

        @Schema(description = "响应类型 1 削峰 2 填谷", example = "1")
        private Integer type;

        @Schema(description = "需求响应-目标响应负荷")
        private BigDecimal targetPower;
    }
}
