package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRationTransaction;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.CorpConsRationTransactionMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class CorpConsRationTransactionServiceImpl extends BaseServiceImpl<CorpConsRationTransactionMapper, CorpConsRationTransaction> implements CorpConsRationTransactionService {

    @Autowired
    CorpConsRationTransactionMapper corpConsRationTransactionMapper;

    @Override
    public ServiceResult<Boolean> insert(CorpConsRationTransaction corpConsRationTransaction) {
        QueryWrapper<CorpConsRationTransaction> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time",corpConsRationTransaction.getDate());
        wrapper.eq("type",corpConsRationTransaction.getType());
        if(corpConsRationTransactionMapper.selectCount(wrapper)>0){
            return ServiceResult.error(4005,"已存在相同日期数据");
        }
        return ServiceResult.success(corpConsRationTransactionMapper.insert(corpConsRationTransaction)>0);
    }

    @Override
    public ServiceResult<Boolean> updateRation(CorpConsRationTransaction corpConsRation) {
        QueryWrapper<CorpConsRationTransaction> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time",corpConsRation.getDate());
        wrapper.eq("type",corpConsRation.getType());
        return ServiceResult.success(corpConsRationTransactionMapper.update(corpConsRation,wrapper)>0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(corpConsRationTransactionMapper.deleteById(id)>0);
    }

    @Override
    public ServiceResult<List<CorpConsRationTransaction>> listRation(String dateTime) {
        QueryWrapper<CorpConsRationTransaction> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", dateTime);
        List<CorpConsRationTransaction> corpConsRationTransactions = corpConsRationTransactionMapper.selectList(wrapper);
        for(CorpConsRationTransaction corpConsRationTransaction:corpConsRationTransactions){
            if(corpConsRationTransaction.getType()==1){
                corpConsRationTransaction.setDataType("新能源价差");
            }else{
                corpConsRationTransaction.setDataType("火电价差");
            }
        }
        return ServiceResult.success(corpConsRationTransactions);
    }
}




