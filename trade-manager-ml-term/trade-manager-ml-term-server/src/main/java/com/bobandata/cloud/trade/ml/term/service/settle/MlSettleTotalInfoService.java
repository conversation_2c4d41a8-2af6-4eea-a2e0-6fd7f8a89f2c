package com.bobandata.cloud.trade.ml.term.service.settle;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleTotalInfoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_total_info(中长期交易：结算汇总单)】的数据库操作Service
 * @createDate 2024-03-19 09:43:55
 */
public interface MlSettleTotalInfoService extends BaseService<MlSettleTotalInfoMapper, MlSettleTotalInfoDo> {

    List<MlSettleTotalInfoDo> listByMonth(String month);

    List<TransactionRespVo> listJyTotal(String month, String displayName, String plantType);

    List<TransactionsBiasRespVo> listJyPcTotal(String year);

    List<Map> listJyType();

    ServiceResult<Boolean> importJs2(MultipartFile multipartFile);
}
