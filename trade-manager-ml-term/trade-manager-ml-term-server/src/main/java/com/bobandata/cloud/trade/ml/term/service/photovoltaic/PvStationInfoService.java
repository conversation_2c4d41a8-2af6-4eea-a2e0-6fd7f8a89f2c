package com.bobandata.cloud.trade.ml.term.service.photovoltaic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo.PvStatDataVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvStationInfoDo;
import java.sql.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:08
 * @Classname ChargeStationInfoService
 * @Description
 */
public interface PvStationInfoService extends IService<PvStationInfoDo> {

    PvStatDataVo statPvData(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    void insertOrUpdBatch(List<PvStationInfoDo> chargeStationInfoDo);
}
