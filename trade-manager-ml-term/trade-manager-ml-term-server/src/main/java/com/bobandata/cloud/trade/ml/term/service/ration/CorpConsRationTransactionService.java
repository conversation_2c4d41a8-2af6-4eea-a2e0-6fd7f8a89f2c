package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRationTransaction;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
public interface CorpConsRationTransactionService extends IService<CorpConsRationTransaction> {

    ServiceResult<Boolean> insert(CorpConsRationTransaction corpConsRationTransaction);

    ServiceResult<Boolean> updateRation(CorpConsRationTransaction corpConsRationTransaction);

    ServiceResult<Boolean> delete(Long id);

    ServiceResult<List<CorpConsRationTransaction>> listRation(String dateTime);
}
