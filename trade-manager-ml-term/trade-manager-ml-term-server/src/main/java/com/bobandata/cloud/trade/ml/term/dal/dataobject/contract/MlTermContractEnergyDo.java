package com.bobandata.cloud.trade.ml.term.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 中长期交易：交易合同拆分信息
 *
 * @TableName ml_term_contract_energy_test
 */
@Data
@EqualsAndHashCode
@ToString
@TableName(value = "ml_term_contract_energy")
public class MlTermContractEnergyDo extends BaseDo {

    /**
     * 交易序列
     */
    @TableField(value = "trade_id")
    private String tradeId;

    /**
     * 交易时段编号
     */
    @TableField(value = "seg_code")
    private Integer segCode;

    /**
     * 交易时段名称
     */
    @TableField(value = "seg_name")
    private String segName;

    /**
     * 交易时段
     */
    @TableField(value = "segment")
    private String segment;

    /**
     * 电量
     */
    @TableField(value = "energy")
    private BigDecimal energy;

    /**
     * 电价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     *
     */
    @TableField(value = "remark")
    private String remark;

}