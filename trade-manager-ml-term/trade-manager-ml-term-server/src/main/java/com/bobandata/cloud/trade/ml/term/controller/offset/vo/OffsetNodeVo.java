package com.bobandata.cloud.trade.ml.term.controller.offset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-22日 16:36
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class OffsetNodeVo {

    @Schema(description = "尖峰电量")
    private BigDecimal highestEnergy;

    @Schema(description = "高峰电量")
    private BigDecimal peakEnergy;

    @Schema(description = "平段电量")
    private BigDecimal flatEnergy;

    @Schema(description = "谷段电量")
    private BigDecimal valleyEnergy;
}
