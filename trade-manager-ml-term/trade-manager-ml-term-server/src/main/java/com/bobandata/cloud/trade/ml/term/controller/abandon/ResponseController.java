package com.bobandata.cloud.trade.ml.term.controller.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.ResponseReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseDetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseInfoDo;
import com.bobandata.cloud.trade.ml.term.service.abandon.ResponseService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/11/16 11:44
 * @Description
 */
@Tag(name = "需求响应 - 响应信息")
@RequestMapping(value = "/response")
@RestController
public class ResponseController {


    @Autowired
    private ResponseService responseService;

    @Operation(summary = "需求响应-响应信息")
    @GetMapping("/getResponsePage")
    @ResponseBody
    public PagingResult<ResponseInfoDo> getResponsePage(ResponseReqVo reqVo) {
        return responseService.getResponseInfoPage(reqVo);
    }

    @Operation(summary = "需求响应-响应详细")
    @GetMapping("/getResponseDetailPage")
    @ResponseBody
    public PagingResult<ResponseDetailDo> getResponseDetailPage(ResponseReqVo reqVo) {
        return responseService.getResponseDetailPage(reqVo);
    }
}
