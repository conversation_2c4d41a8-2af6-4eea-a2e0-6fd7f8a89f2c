package com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo;

import com.github.yulichang.annotation.FieldMapping;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/11/1 9:29
 * @Classname StationInfoVo
 * @Description
 */

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
public class StationInfoVo implements Serializable {

    @NotBlank(message = "场站ID不能为空")
    private String stationId;

    private String stationName;

    private String areaCode;

    private String areaName;

    private String jd;

    private String wd;

    private String userId;

    private String userName;

    private BigDecimal capatity;

    private String invSn;

    private String invType;

}
