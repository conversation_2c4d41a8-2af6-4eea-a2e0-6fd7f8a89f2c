package com.bobandata.cloud.trade.ml.term.service.charge;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.internal.DataCurveConvert;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStatRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsStatDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeStationsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeRecordsStatMapper;
import com.bobandata.cloud.trade.ml.term.service.charge.dto.ChargeStationDto;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【charge_records_stat(充电桩电量数据汇总)】的数据库操作Service实现
 * @createDate 2024-11-04 14:09:33
 */
@Service
public class ChargeRecordsStatServiceImpl extends BaseServiceImpl<ChargeRecordsStatMapper, ChargeRecordsStatDo> implements ChargeRecordsStatService {

    @Autowired
    private ChargeStationsService chargeStationsService;

    @Override
    public ChargeStatRespVo chargeStat(Date startDate, Date endDate) {
        List<ChargeStationsDo> stationsDos = chargeStationsService.list();
        BigDecimal totalPower = stationsDos.stream()
                                           .map(ChargeStationsDo::getDeviceTotalPower)
                                           .map(data -> data == null ? BigDecimal.ZERO : data)
                                           .reduce(BigDecimal::add)
                                           .get()
                                           .divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP);
        ChargeRecordsStatMapper statMapper = getBaseMapper();
        List<KeyValueVo> stationCount = statMapper.selectStat();
        List<KeyValueVo> energy = statMapper.selectEnergy(startDate, endDate);
        BigDecimal monthEnergy = Optional.ofNullable(statMapper.selectMonthEnergy()).orElse(BigDecimal.ZERO);
        BigDecimal yearEnergy = Optional.ofNullable(statMapper.selectYearEnergy()).orElse(BigDecimal.ZERO);
        return ChargeStatRespVo.builder()
                               .stationSum(stationsDos.size())
                               .totalPower(totalPower)
                               .dayEnergy(monthEnergy.setScale(3, RoundingMode.HALF_UP))
                               .monthEnergy(yearEnergy.setScale(3, RoundingMode.HALF_UP))
                               .energyStat(energy)
                               .stationCount(stationCount)
                               .build();
    }

    @Override
    public List<KeyValueVo> getStationCurve(Date dataDate) {
        List<ChargeStationDto> chargeStationDtos = getBaseMapper().selectDayEnergyCurve(dataDate);
        return DataCurveConvert.convert24LabelValue(chargeStationDtos.toArray(new ChargeStationDto[0]), "name");
    }

    @Override
    public void dataEtl(List<ChargeRecordsDo> chargeRecordsDos) {
        List<ChargeRecordsStatDo> recordsStatDos = chargeRecordsDos.stream().flatMap(chargeDo -> {
            LocalDateTime startTime = chargeDo.getStartTime().toLocalDateTime();
            LocalDateTime endTime = chargeDo.getEndTime().toLocalDateTime();
            BigDecimal sumPower = chargeDo.getSumPower().divide(new BigDecimal("1000"), 3, RoundingMode.HALF_UP);
            if (sumPower.compareTo(BigDecimal.ZERO) == 0) {
                return null;
            }
            Map<LocalDateTime, BigDecimal> dateTimeVal = this.splitPower(startTime, endTime, sumPower);
            Map<LocalDate, List<Map.Entry<LocalDateTime, BigDecimal>>> groupData = dateTimeVal.entrySet()
                                                                                              .stream()
                                                                                              .collect(
                                                                                                      Collectors.groupingBy(
                                                                                                              entry -> entry.getKey()
                                                                                                                            .toLocalDate()));
            return groupData.entrySet().stream().map(entry -> {
                ChargeRecordsStatDo statDo = new ChargeRecordsStatDo().setStationId(chargeDo.getStationId())
                                                                      .setStationName(chargeDo.getStationName())
                                                                      .setPileId(chargeDo.getPileId())
                                                                      .setPileName(chargeDo.getPileName())
                                                                      .setPileNo(chargeDo.getPileNo())
                                                                      .setGunId(chargeDo.getGunId())
                                                                      .setDataDate(Date.valueOf(entry.getKey()));
                List<Map.Entry<LocalDateTime, BigDecimal>> value = entry.getValue();
                for (final Map.Entry<LocalDateTime, BigDecimal> decimalEntry : value) {
                    BigDecimal pointValue = decimalEntry.getValue();
                    LocalDateTime dateTime = decimalEntry.getKey();
                    statDo.setPointValue(dateTime.getHour(), pointValue);
                }
                return statDo;
            });
        }).collect(Collectors.toList());
        for (final ChargeRecordsStatDo recordsStatDo : recordsStatDos) {
            String pileNo = recordsStatDo.getPileNo();
            Date dataDate = recordsStatDo.getDataDate();
            LambdaQueryWrapper<ChargeRecordsStatDo> query = Wrappers.lambdaQuery(
                    ChargeRecordsStatDo.class);
            query.eq(ChargeRecordsStatDo::getDataDate, dataDate)
                 .eq(ChargeRecordsStatDo::getGunId, recordsStatDo.getGunId())
                 .eq(ChargeRecordsStatDo::getPileNo, pileNo);
            ChargeRecordsStatDo statDo = this.getOne(query);
            if (statDo == null) {
                this.save(recordsStatDo);
            } else {
                recordsStatDo.setId(statDo.getId());
                this.updateById(recordsStatDo);
            }
        }

    }

    private Map<LocalDateTime, BigDecimal> splitPower(LocalDateTime start, LocalDateTime end, BigDecimal value) {
        BigDecimal totalMinutes = new BigDecimal(String.valueOf(Duration.between(start, end).toMinutes()));
        if (totalMinutes.compareTo(BigDecimal.ZERO) == 0) {
            return Collections.emptyMap();
        }
        LocalDateTime[] array = splitPeriodToHours(start, end).toArray(new LocalDateTime[0]);
        Map<LocalDateTime, BigDecimal> dateTimeVal = new HashMap<>();
        for (int i = 0; i < array.length - 1; i++) {
            BigDecimal minutes = new BigDecimal(String.valueOf(Duration.between(array[i], array[i + 1]).toMinutes()));
            BigDecimal scale = BigDecimal.ZERO;
            if (totalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                scale = minutes.divide(totalMinutes, 3, RoundingMode.HALF_UP);
            }
            BigDecimal multiply = scale.multiply(value).setScale(3, RoundingMode.HALF_UP);
            dateTimeVal.put(array[i], multiply);
        }
        return dateTimeVal;
    }

    private List<LocalDateTime> splitPeriodToHours(LocalDateTime start, LocalDateTime end) {
        List<LocalDateTime> hours = new ArrayList<>();
        hours.add(start);
        LocalDateTime current = start;

        while (current.isBefore(end)) {
            LocalDateTime hourBoundary = current.plusHours(1).withMinute(0).withSecond(0).withNano(0);
            if (hourBoundary.isAfter(end)) {
                hourBoundary = end;
            }
            hours.add(hourBoundary);
            current = hourBoundary;
        }

        return hours;
    }
}




