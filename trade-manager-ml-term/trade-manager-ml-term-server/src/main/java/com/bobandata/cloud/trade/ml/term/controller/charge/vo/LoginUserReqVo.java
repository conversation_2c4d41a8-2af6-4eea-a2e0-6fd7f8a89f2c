package com.bobandata.cloud.trade.ml.term.controller.charge.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/11/4 16:20
 * @Classname LoginUserReqVo
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LoginUserReqVo {

    private String account;

    private String password;

    private String sellerNumber;

    private Boolean smsCaptchaPass;
}
