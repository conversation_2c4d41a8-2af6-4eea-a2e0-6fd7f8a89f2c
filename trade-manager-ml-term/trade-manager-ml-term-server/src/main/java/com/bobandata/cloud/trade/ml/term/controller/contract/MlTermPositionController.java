package com.bobandata.cloud.trade.ml.term.controller.contract;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractDetailRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractEnergyPriceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoRespVo;
import com.bobandata.cloud.trade.ml.term.convert.ContractPositionConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo;
import com.bobandata.cloud.trade.ml.term.service.contract.MlTermContractEnergyService;
import com.bobandata.cloud.trade.ml.term.service.contract.MlTermContractInfoService;
import com.bobandata.cloud.trade.ml.term.service.contract.dto.ContractDetailDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18日 09:54
 * @description
 */
@Tag(name = "中长期管理-合同持仓管理")
@Slf4j
@RequestMapping("/position")
@RestController
public class MlTermPositionController {

    @Autowired
    private MlTermContractInfoService contractInfoService;

    @Autowired
    private MlTermContractEnergyService energyService;

    @Operation(summary = "获得中长期合同列表")
    @GetMapping("/list")
    public ServiceResult<List<ContractInfoRespVo>> listContracts(@Valid ContractInfoReqVo contractInfoReqVo) {
        String startTime = contractInfoReqVo.getStartTime();
        contractInfoReqVo.setStartYear(Integer.valueOf(startTime.split("-")[0]));
        contractInfoReqVo.setStartMonth(Integer.valueOf(startTime.split("-")[1]));
        String endTime = contractInfoReqVo.getEndTime();
        contractInfoReqVo.setEndYear(Integer.valueOf(endTime.split("-")[0]));
        contractInfoReqVo.setEndMonth(Integer.valueOf(endTime.split("-")[1]));
        List<MlTermContractInfoDo> termContractInfoDos = contractInfoService.listByMonth(contractInfoReqVo);
        return ServiceResult.success(ContractPositionConvert.INSTANCE.convertContractInfos(termContractInfoDos));
    }

    @Operation(summary = "获得中长期合同详情")
    @Parameter(name = "tradeId", description = "合同ID", required = true, example = "1767370649955987456")
    @GetMapping("/detail")
    public ServiceResult<ContractDetailRespVo> getContractsDetail(@RequestParam(value = "tradeId") String tradeId) {
        List<ContractDetailDto> contractDetailDtos = contractInfoService.listDetailByTradeId(tradeId);
        ContractDetailRespVo contractDetailRespVo = ContractPositionConvert.INSTANCE.convertContractDetail(
                contractDetailDtos);
        if (contractDetailRespVo == null) {
            return ServiceResult.success(null, "查询结果为空");
        }
        return ServiceResult.success(contractDetailRespVo);
    }

    @Operation(summary = "获得中长期合同电量电价图")
    @GetMapping("/energy")
    public ServiceResult<ContractEnergyPriceRespVo> getEnergyWithPrice(@Valid ContractInfoReqVo contractInfoReqVo) {
        String startTime = contractInfoReqVo.getStartTime();
        contractInfoReqVo.setStartYear(Integer.valueOf(startTime.split("-")[0]));
        ContractEnergyPriceRespVo contractEnergyPrice = contractInfoService.getContractEnergyPrice(contractInfoReqVo);
        return ServiceResult.success(contractEnergyPrice);
    }
    @Operation(summary = "申报")
    @GetMapping("/declare")
    public ServiceResult<Boolean> declare(@RequestParam(value = "time") String time, @RequestParam(value = "energy") String energy) {
        return contractInfoService.declare(time,energy);
    }
}
