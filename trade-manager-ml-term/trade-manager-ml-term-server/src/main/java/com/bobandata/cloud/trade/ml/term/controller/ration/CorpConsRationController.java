package com.bobandata.cloud.trade.ml.term.controller.ration;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRation;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.CorpConsRationMapper;
import com.bobandata.cloud.trade.ml.term.service.ration.CorpConsRationService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "录入值")
@Slf4j
@RequestMapping("/ration")
@RestController
public class CorpConsRationController extends BaseCrudRestController<CorpConsRation, CorpConsRationMapper> {

    @Autowired
    private CorpConsRationService corpConsRationService;

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertRation(@RequestBody CorpConsRation corpConsRation) {
        return corpConsRationService.insert(corpConsRation);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public ServiceResult<Boolean> updateRation(@RequestBody CorpConsRation corpConsRation) {
        return corpConsRationService.updateRation(corpConsRation);
    }

    @Operation(summary = "list")
    @GetMapping("/list")
    public ServiceResult<List<CorpConsRation>> listRation(@Valid String dateTime) {
        return corpConsRationService.listRation(dateTime);
    }


}
