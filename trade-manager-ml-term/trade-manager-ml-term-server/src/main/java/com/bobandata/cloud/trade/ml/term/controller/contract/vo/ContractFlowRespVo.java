package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "中长期管理-台账管理 分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ContractFlowRespVo {

    @Schema(description = "交易序列", example = "PHZHNxxxxA0001")
    protected String tradeSegId;

    @Schema(description = "交易名称", example = "PHZHNxxxxA0001")
    private String tradeseqCaption;

    @Schema(description = "售方用户编号", example = "xxxcode")
    private String saleUnitsCode;

    @Schema(description = "售方用户", example = "xxx公司")
    protected String saleUnitsName;

    @Schema(description = "购方用户编号", example = "xxxcode")
    private String vendeeUnitsCode;

    @Schema(description = "购方用户", example = "xxx公司")
    protected String vendeeUnitsName;

    @Schema(description = "合同开始时间", example = "日期格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    protected Date startTime;

    @Schema(description = "合同结束时间", example = "日期格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    protected Date endTime;

    @Schema(description = "电量", example = "保留三位小数")
    private BigDecimal vendeeEnergy;

    @Schema(description = "价差", example = "保留两位小数")
    private BigDecimal vendeePrice;

    @Schema(description = "时段")
    private String timeDivisionCode;

    @Schema(description = "交易时间", example = "日期格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp contractCreateTime;

}
