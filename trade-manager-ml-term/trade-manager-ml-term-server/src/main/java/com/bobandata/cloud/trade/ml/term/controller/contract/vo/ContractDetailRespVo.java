package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-19日 09:27
 * @description
 */
@Schema(description = "中长期管理-合同持仓管理 合同详情")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ContractDetailRespVo {

    @Schema(description = "交易合同ID 不显示")
    private String tradeId;

    @Schema(description = "交易名称", nullable = true)
    private String tradeName;

    @Schema(description = "买方用户")
    private String purchaseUnitName;

    @Schema(description = "卖方用户")
    private String sellerUnitName;

    @Schema(description = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Date startTime;

    @Schema(description = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Date endTime;

    @Schema(description = "尖峰电量")
    private BigDecimal highestEnergy;

    @Schema(description = "尖峰价差")
    private BigDecimal highestPrice;

    @Schema(description = "高峰电量")
    private BigDecimal peakEnergy;

    @Schema(description = "高峰价差")
    private BigDecimal peakPrice;

    @Schema(description = "平段电量")
    private BigDecimal flatEnergy;

    @Schema(description = "平段价差")
    private BigDecimal flatPrice;

    @Schema(description = "谷段电量")
    private BigDecimal valleyEnergy;

    @Schema(description = "谷段价差")
    private BigDecimal valleyPrice;
}
