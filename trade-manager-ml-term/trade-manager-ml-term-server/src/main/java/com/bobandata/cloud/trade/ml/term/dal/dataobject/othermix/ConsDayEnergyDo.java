package com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.internal.DataPointCurve;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * E_MKT_CONS_DAY_XH_PQ_OTHER
 * <p>
 * 营销日电量拟合曲线
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@TableName(value = "e_mkt_cons_day_xh_pq_other")
public class ConsDayEnergyDo implements DataPointCurve {

    @TableId
    private Long spotDayId;

    private String amtYm;

    private Date thisYmd;

    private String orgNo;

    private String consNo;

    private String consName;

    private String elecAddr;

    private String consSortCode;

    private String voltCode;

    private String elecTypeCode;

    private String spotHm;

    private String spotPointFlag;

    private String pqMode;

    private Date sendDate;

    private String dataStatus;

    private String batchNo;

    private String remark;

    private String marketType;

    private String calcMode;

    private String forValue;

    private BigDecimal apTl;

    private BigDecimal apLl;

    private BigDecimal tPq1;

    private BigDecimal tPq2;

    private BigDecimal tPq3;

    private BigDecimal tPq4;

    private BigDecimal tPq5;

    private BigDecimal tPq6;

    private BigDecimal tPq7;

    private BigDecimal tPq8;

    private BigDecimal tPq9;

    private BigDecimal tPq10;

    private BigDecimal tPq11;

    private BigDecimal tPq12;

    private BigDecimal tPq13;

    private BigDecimal tPq14;

    private BigDecimal tPq15;

    private BigDecimal tPq16;

    private BigDecimal tPq17;

    private BigDecimal tPq18;

    private BigDecimal tPq19;

    private BigDecimal tPq20;

    private BigDecimal tPq21;

    private BigDecimal tPq22;

    private BigDecimal tPq23;

    private BigDecimal tPq24;

    private BigDecimal tPq25;

    private BigDecimal tPq26;

    private BigDecimal tPq27;

    private BigDecimal tPq28;

    private BigDecimal tPq29;

    private BigDecimal tPq30;

    private BigDecimal tPq31;

    private BigDecimal tPq32;

    private BigDecimal tPq33;

    private BigDecimal tPq34;

    private BigDecimal tPq35;

    private BigDecimal tPq36;

    private BigDecimal tPq37;

    private BigDecimal tPq38;

    private BigDecimal tPq39;

    private BigDecimal tPq40;

    private BigDecimal tPq41;

    private BigDecimal tPq42;

    private BigDecimal tPq43;

    private BigDecimal tPq44;

    private BigDecimal tPq45;

    private BigDecimal tPq46;

    private BigDecimal tPq47;

    private BigDecimal tPq48;

    private BigDecimal tPq49;

    private BigDecimal tPq50;

    private BigDecimal tPq51;

    private BigDecimal tPq52;

    private BigDecimal tPq53;

    private BigDecimal tPq54;

    private BigDecimal tPq55;

    private BigDecimal tPq56;

    private BigDecimal tPq57;

    private BigDecimal tPq58;

    private BigDecimal tPq59;

    private BigDecimal tPq60;

    private BigDecimal tPq61;

    private BigDecimal tPq62;

    private BigDecimal tPq63;

    private BigDecimal tPq64;

    private BigDecimal tPq65;

    private BigDecimal tPq66;

    private BigDecimal tPq67;

    private BigDecimal tPq68;

    private BigDecimal tPq69;

    private BigDecimal tPq70;

    private BigDecimal tPq71;

    private BigDecimal tPq72;

    private BigDecimal tPq73;

    private BigDecimal tPq74;

    private BigDecimal tPq75;

    private BigDecimal tPq76;

    private BigDecimal tPq77;

    private BigDecimal tPq78;

    private BigDecimal tPq79;

    private BigDecimal tPq80;

    private BigDecimal tPq81;

    private BigDecimal tPq82;

    private BigDecimal tPq83;

    private BigDecimal tPq84;

    private BigDecimal tPq85;

    private BigDecimal tPq86;

    private BigDecimal tPq87;

    private BigDecimal tPq88;

    private BigDecimal tPq89;

    private BigDecimal tPq90;

    private BigDecimal tPq91;

    private BigDecimal tPq92;

    private BigDecimal tPq93;

    private BigDecimal tPq94;

    private BigDecimal tPq95;

    private BigDecimal tPq96;

    /**
     * 插入时间
     */
    @TableField(value = "insert_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp insertTime;

    @Override
    public String getDataKey() {
        return "energyQty";
    }

    @Override
    public String getPrefix() {
        return "tPq";
    }

    public KeyValueVo getOtherKeyValue() {
        KeyValueVo keyValueVo = new KeyValueVo();
        keyValueVo.put("consNo", this.consNo);
        keyValueVo.put("consName", this.consName);
        keyValueVo.put("curveDate", this.thisYmd);
        return keyValueVo;
    }
}