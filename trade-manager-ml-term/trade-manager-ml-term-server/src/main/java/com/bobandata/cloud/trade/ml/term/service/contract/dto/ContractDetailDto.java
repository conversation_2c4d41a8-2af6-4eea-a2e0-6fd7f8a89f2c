package com.bobandata.cloud.trade.ml.term.service.contract.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-03-19日 14:28
 * @description
 */
@Data
@EqualsAndHashCode
@ToString
public class ContractDetailDto {

    private String tradeId;

    private String tradeName;

    private String purchaseUnitName;

    private String sellerUnitName;

    private Date startTime;

    private Date endTime;

    private String segName;

    private BigDecimal energy;

    private BigDecimal price;
}
