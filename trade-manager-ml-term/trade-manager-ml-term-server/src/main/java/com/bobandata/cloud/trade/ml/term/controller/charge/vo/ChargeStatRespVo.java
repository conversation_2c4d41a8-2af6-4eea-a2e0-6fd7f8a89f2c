package com.bobandata.cloud.trade.ml.term.controller.charge.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-11-05日 09:36
 * @description
 */
@Schema(description = "充电桩统计信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class ChargeStatRespVo {

    @Schema(description = "场站规模")
    private Integer stationSum;

    @Schema(description = "可响应负荷")
    private BigDecimal totalPower;

    @Schema(description = "日均电量")
    private BigDecimal dayEnergy;

    @Schema(description = "月累计电量")
    private BigDecimal monthEnergy;

    @Schema(description = "充电桩分布柱状图")
    private List<KeyValueVo> stationCount;

    @Schema(description = "日电量分布图")
    private List<KeyValueVo> energyStat;
}
