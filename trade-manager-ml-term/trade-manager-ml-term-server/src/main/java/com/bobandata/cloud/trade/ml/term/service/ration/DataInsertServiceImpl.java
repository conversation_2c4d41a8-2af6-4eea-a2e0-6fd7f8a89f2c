package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRation;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRationTransaction;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnjsc;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnprice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class DataInsertServiceImpl {

    @Autowired
    private CorpConsRationService corpConsRationService;

    @Autowired
    private CorpConsRationTransactionService corpConsRationTransactionService;

    @Autowired
    private HnjscService hnjscService;

    @Autowired
    private HnPriceService hnpriceService;


    @Transactional(rollbackFor = Exception.class)
    public ServiceResult<Boolean> saveData(List<CorpConsRation> corpConsRations, List<CorpConsRationTransaction> transaction, List<Hnjsc> hnjscs, List<Hnprice> hnprices) {
        try {
            if (corpConsRations.size() > 0) {
                for (CorpConsRation corpConsRation : corpConsRations) {
                    QueryWrapper<CorpConsRation> wrapper = new QueryWrapper<>();
                    wrapper.eq("data_time", corpConsRation.getDate());
                    if (corpConsRationService.getBaseMapper().selectCount(wrapper) > 0) {
                        corpConsRationService.updateRation(corpConsRation);
                    } else {
                        corpConsRationService.insert(corpConsRation);
                    }
                }
            }
            if (transaction.size() > 0) {
                for (CorpConsRationTransaction corpConsRationTransaction : transaction) {
                    QueryWrapper<CorpConsRationTransaction> wrapper = new QueryWrapper<>();
                    wrapper.eq("data_time", corpConsRationTransaction.getDate());
                    wrapper.eq("type", corpConsRationTransaction.getType());
                    if (corpConsRationTransactionService.getBaseMapper().selectCount(wrapper) > 0) {
                        corpConsRationTransactionService.updateRation(corpConsRationTransaction);
                    } else {
                        corpConsRationTransactionService.insert(corpConsRationTransaction);
                    }
                }
            }
            if (hnjscs.size() > 0) {
                for (Hnjsc hnjsc : hnjscs) {
                    QueryWrapper<Hnjsc> wrapper = new QueryWrapper<>();
                    wrapper.eq("data_time", hnjsc.getDate());
                    wrapper.eq("data_type", hnjsc.getDataType());
                    if (hnjscService.getBaseMapper().selectCount(wrapper) > 0) {
                        hnjscService.updateRation(hnjsc);
                    } else {
                        hnjscService.insert(hnjsc);
                    }
                }
            }
            if (hnprices.size() > 0) {
                for (Hnprice hnprice : hnprices) {
                    hnprice.setDate(hnprice.getDate() + "-01 00:00:00");
                    QueryWrapper<Hnprice> wrapper = new QueryWrapper<>();
                    wrapper.eq("data_time", hnprice.getDate());
                    wrapper.eq("data_type", hnprice.getDataType());
                    if (hnpriceService.getBaseMapper().selectCount(wrapper) > 0) {
                        hnpriceService.updateRation(hnprice);
                    } else {
                        hnpriceService.insert(hnprice);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ServiceResult.error(500, "数据插入失败");
        }
        return ServiceResult.success(true);
    }


}




