package com.bobandata.cloud.trade.ml.term.controller.ration;

import cn.hutool.json.JSONUtil;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRation;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRationTransaction;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnjsc;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnprice;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.CorpConsRationMapper;
import com.bobandata.cloud.trade.ml.term.service.ration.DataInsertServiceImpl;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.util.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "录入值")
@Slf4j
@RequestMapping("/dataInsert")
@RestController
public class DataInsertController extends BaseCrudRestController<CorpConsRation, CorpConsRationMapper> {

    @Autowired
    private DataInsertServiceImpl dataInsertServiceImpl;


    @Operation(summary = "新增")
    @PostMapping("/saveData")
    public ServiceResult<Boolean> saveData(@RequestBody String jsonString) {
        Map<String, Object> stringObjectMap = JSON.parseObject(jsonString);
        List<LinkedHashMap> corpConsRationMaps = (List<LinkedHashMap>) stringObjectMap.get("ration");
        List<CorpConsRation> corpConsRations = JSONUtil.toList(JSONUtil.parseArray(corpConsRationMaps), CorpConsRation.class);
// 同样的方式转换其他对象
        List<LinkedHashMap> transactionMaps = (List<LinkedHashMap>) stringObjectMap.get("transaction");
        List<CorpConsRationTransaction> transaction = JSONUtil.toList(JSONUtil.parseArray(transactionMaps), CorpConsRationTransaction.class);

        List<LinkedHashMap> hnjscMaps = (List<LinkedHashMap>) stringObjectMap.get("hnjsc");
        List<Hnjsc> hnjscs = JSONUtil.toList(JSONUtil.parseArray(hnjscMaps), Hnjsc.class);

        List<LinkedHashMap> hnpriceMaps = (List<LinkedHashMap>) stringObjectMap.get("hnprice");
        List<Hnprice> hnprices = JSONUtil.toList(JSONUtil.parseArray(hnpriceMaps), Hnprice.class);
//
        return dataInsertServiceImpl.saveData(corpConsRations, transaction, hnjscs, hnprices);
    }
}
