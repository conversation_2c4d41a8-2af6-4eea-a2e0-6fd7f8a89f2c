package com.bobandata.cloud.trade.ml.term.convert;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.trade.curve.core.constant.SegNameCons;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractDetailRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractEnergyPriceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo;
import com.bobandata.cloud.trade.ml.term.service.contract.dto.ContractDetailDto;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;

import java.time.Year;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ContractPositionConvert {

    public static final ContractPositionConvert INSTANCE = new ContractPositionConvert();

    public List<ContractInfoRespVo> convertContractInfos(List<MlTermContractInfoDo> contractInfoDos) {
        return BobanBeanUtils.convertList(contractInfoDos, ContractInfoRespVo.class);
    }

    public ContractEnergyPriceRespVo convertContractEnergyPrice(List<Map> list, List<Map> list2) {
        List<String> dateList = list.stream().map(item -> item.get("dataTime").toString()).collect(Collectors.toList());
        ContractEnergyPriceRespVo respVo = new ContractEnergyPriceRespVo(dateList);
        for (Map m : list) {
            if (m.get("dataType").equals("交易类型")) {
                respVo.getJingjia().putKeyValue(m.get("dataTime").toString(), m.get("value1"));
                respVo.getSbxs().putKeyValue(m.get("dataTime").toString(), m.get("value2"));
                respVo.getGp().putKeyValue(m.get("dataTime").toString(), m.get("value3"));
            } else {
                respVo.getNewEnergy().putKeyValue(m.get("dataTime").toString(), m.get("value1"));
                respVo.getGreenEnergy().putKeyValue(m.get("dataTime").toString(), m.get("value2"));
                respVo.getFireEnergy().putKeyValue(m.get("dataTime").toString(), m.get("value3"));
            }
        }
        for (Map m : list2) {
            respVo.getJjc().putKeyValue(m.get("dataTime").toString(), m.get("value1"));
            respVo.getLjjjc().putKeyValue(m.get("dataTime").toString(), m.get("ljjjc"));
        }
        return respVo;
    }

    public ContractDetailRespVo convertContractDetail(List<ContractDetailDto> contractDetailDtos) {
        if (CollUtil.isEmpty(contractDetailDtos)) {
            return null;
        }
        ContractDetailDto contractDetailDto = contractDetailDtos.get(0);
        ContractDetailRespVo contractDetailRespVo = BobanBeanUtils.convert(
                contractDetailDto, ContractDetailRespVo.class);
        for (final ContractDetailDto detailDto : contractDetailDtos) {
            String segName = detailDto.getSegName();
            switch (segName) {
                case SegNameCons.HIGHEST_SEG_NAME:
                    contractDetailRespVo.setHighestEnergy(detailDto.getEnergy())
                            .setHighestPrice(detailDto.getPrice());
                    break;
                case SegNameCons.PEAK_SEG_NAME:
                    contractDetailRespVo.setPeakEnergy(detailDto.getEnergy())
                            .setPeakPrice(detailDto.getPrice());
                    break;
                case SegNameCons.FLAT_SEG_NAME:
                    contractDetailRespVo.setFlatEnergy(detailDto.getEnergy())
                            .setFlatPrice(detailDto.getPrice());
                    break;
                case SegNameCons.VALLEY_SEG_NAME:
                    contractDetailRespVo.setValleyEnergy(detailDto.getEnergy())
                            .setValleyPrice(detailDto.getPrice());
                    break;
                default:
                    throw new IllegalArgumentException(segName + "不支持此时段");
            }
        }
        return contractDetailRespVo;
    }
}
