package com.bobandata.cloud.trade.ml.term.controller.charge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/4 14:13
 * @Classname ChargeStationRespVo
 * @Description
 */
@Schema(description = "充电站接口RespVo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class ChargeStationRespVo {

    private Integer pageSize;

    //充电站当前页码
    private Integer pageNo;

    //充电桩当前页码
    private Integer current;

    //总页数
    private Integer pages;

    //充电站的总条数
    private Integer totalCount;

    //充电站的总数据
    private List<Object> data;

    //充电桩的总条数
    private Integer total;

    //充电桩的总数据
    private List<Object> records;
}
