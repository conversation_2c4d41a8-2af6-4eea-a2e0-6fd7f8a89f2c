package com.bobandata.cloud.trade.ml.term.service.contract;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractEnergyPriceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoReqVo;
import com.bobandata.cloud.trade.ml.term.convert.ContractPositionConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractInfoMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleTotalInfoMapper;
import com.bobandata.cloud.trade.ml.term.service.contract.dto.ContractDetailDto;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.github.yulichang.interfaces.MPJBaseJoin;
import com.github.yulichang.query.MPJQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Year;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_info_test(中长期交易：交易合同基本信息)】的数据库操作Service实现
 * @createDate 2024-03-12 15:57:26
 */
@Service
public class MlTermContractInfoServiceImpl extends BaseServiceImpl<MlTermContractInfoMapper, MlTermContractInfoDo> implements MlTermContractInfoService {

    @Autowired
    MlTermContractInfoMapper mlTermContractInfoMapper;

    @Autowired
    MlSettleTotalInfoMapper mlSettleTotalInfoMapper;

    @Override
    public List<MlTermContractInfoDo> listByMonth(final ContractInfoReqVo contractInfoReqVo) {
        LambdaQueryWrapperX<MlTermContractInfoDo> wrapper = new LambdaQueryWrapperX<>();
        wrapper.likeIfPresent(MlTermContractInfoDo::getArrName, contractInfoReqVo.getArrName())
                .likeIfPresent(MlTermContractInfoDo::getSellerUnitName, contractInfoReqVo.getSellerUnitName())
                .likeIfPresent(MlTermContractInfoDo::getPurchaseUnitName, contractInfoReqVo.getPurchaseUnitName())
                .betweenIfPresent(
                        MlTermContractInfoDo::getDateYear, contractInfoReqVo.getStartYear(),
                        contractInfoReqVo.getEndYear()
                )
                .betweenIfPresent(
                        MlTermContractInfoDo::getDateMonth, contractInfoReqVo.getStartMonth(),
                        contractInfoReqVo.getEndMonth()
                );
        wrapper.orderByDesc(MlTermContractInfoDo::getDateYear, MlTermContractInfoDo::getDateMonth);
        return this.list(wrapper);
    }


    @Override
    public List<ContractDetailDto> listDetailByTradeId(String tradeId) {
        MPJBaseJoin<MlTermContractInfoDo> wrapper = new MPJQueryWrapper<MlTermContractInfoDo>()
                .selectAll(MlTermContractInfoDo.class)
                .select(" (CASE\n" +
                        "b.seg_name \n" +
                        "WHEN '尖峰' THEN\n" +
                        "'尖峰' \n" +
                        "WHEN '高峰' THEN\n" +
                        "'高峰' \n" +
                        "WHEN '平段' THEN\n" +
                        "'平段' \n" +
                        "WHEN '谷段' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段1' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段2' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段3' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段4' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段5' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段6' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段7' THEN\n" +
                        "'谷段' \n" +
                        "WHEN '段8' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段9' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段10' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段11' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段12' THEN\n" +
                        "'高峰' \n" +
                        "WHEN '段13' THEN\n" +
                        "'高峰' \n" +
                        "WHEN '段14' THEN\n" +
                        "'高峰' \n" +
                        "WHEN '段15' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段16' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段17' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段18' THEN\n" +
                        "'平段' \n" +
                        "WHEN '段19' THEN\n" +
                        "'尖峰' \n" +
                        "WHEN '段20' THEN\n" +
                        "'尖峰' \n" +
                        "WHEN '段21' THEN\n" +
                        "'尖峰' \n" +
                        "WHEN '段22' THEN\n" +
                        "'尖峰' \n" +
                        "WHEN '段23' THEN\n" +
                        "'高峰' \n" +
                        "WHEN '段24' THEN\n" +
                        "'谷段' \n" +
                        "\tWHEN '1' THEN\n" +
                        "'尖峰' \n" +
                        "\tWHEN '2' THEN\n" +
                        "'高峰' \n" +
                        "\tWHEN '3' THEN\n" +
                        "'平段' \n" +
                        "\tWHEN '4' THEN\n" +
                        "'谷段' \n" +
                        "ELSE NULL \n" +
                        "END \n" +
                        ") AS seg_name , sum(b.energy) as energy, b.price")
                .leftJoin("ml_term_contract_energy b on t.trade_id = b.trade_id")
                .eq("t.trade_id", tradeId)
                .groupBy("b.seg_name");
        return getBaseMapper().selectJoinList(ContractDetailDto.class, wrapper);
    }

    @Override
    public ContractEnergyPriceRespVo getContractEnergyPrice(ContractInfoReqVo contractInfoReqVo) {
//        MPJQueryWrapper<MlTermContractInfoDo> wrapper = new MPJQueryWrapper<MlTermContractInfoDo>()
//                .select("data_time as yearMonth, (case b.seg_name\n" +
//                        "when '尖峰' then '尖峰'\n" +
//                        "when '高峰' then '高峰'\n" +
//                        "when '平段' then '平段'\n" +
//                        "when '谷段' then '谷段'\n" +
//                        "when '段1' then '谷段'\n" +
//                        "when '段2' then '谷段'\n" +
//                        "when '段3' then '谷段'\n" +
//                        "when '段4' then '谷段'\n" +
//                        "when '段5' then '谷段'\n" +
//                        "when '段6' then '谷段'\n" +
//                        "when '段7' then '谷段'\n" +
//                        "when '段8' then '平段'\n" +
//                        "when '段9' then '平段'\n" +
//                        "when '段10' then '平段'\n" +
//                        "when '段11' then '平段'\n" +
//                        "when '段12' then '高峰'\n" +
//                        "when '段13' then '高峰'\n" +
//                        "when '段14' then '高峰'\n" +
//                        "when '段15' then '平段'\n" +
//                        "when '段16' then '平段'\n" +
//                        "when '段17' then '平段'\n" +
//                        "when '段18' then '平段'\n" +
//                        "when '段19' then '尖峰'\n" +
//                        "when '段20' then '尖峰'\n" +
//                        "when '段21' then '尖峰'\n" +
//                        "when '段22' then '尖峰'\n" +
//                        "when '段23' then '高峰'\n" +
//                        "when '段24' then '谷段'\n" +
//                        "when '峰' then '高峰'\n" +
//                        "when '平' then '平段'\n" +
//                        "when '谷' then '谷段'\n" +
//                        "when '尖' then '尖峰'\n" +
//                        "else null end) as seg_name, sum(b.energy) as energy")
//                .leftJoin("ml_term_contract_energy b on t.trade_id = b.trade_id");
//        buildWrapper(contractInfoReqVo, wrapper);
//        List<ContractSegEnergyDto> contractSegEnergyDtos = getBaseMapper().selectJoinList(
//                ContractSegEnergyDto.class,
//                wrapper
//        );
//        MPJQueryWrapper<MlTermContractInfoDo> wrapper1 = new MPJQueryWrapper<MlTermContractInfoDo>()
//                .select("date_format(t.start_time, '%Y-%m') as yearMonth, (case b.seg_name\n" +
//                        "when '尖峰' then '尖峰'\n" +
//                        "when '高峰' then '高峰'\n" +
//                        "when '平段' then '平段'\n" +
//                        "when '谷段' then '谷段'\n" +
//                        "when '段1' then '谷段'\n" +
//                        "when '段2' then '谷段'\n" +
//                        "when '段3' then '谷段'\n" +
//                        "when '段4' then '谷段'\n" +
//                        "when '段5' then '谷段'\n" +
//                        "when '段6' then '谷段'\n" +
//                        "when '段7' then '谷段'\n" +
//                        "when '段8' then '平段'\n" +
//                        "when '段9' then '平段'\n" +
//                        "when '段10' then '平段'\n" +
//                        "when '段11' then '平段'\n" +
//                        "when '段12' then '高峰'\n" +
//                        "when '段13' then '高峰'\n" +
//                        "when '段14' then '高峰'\n" +
//                        "when '段15' then '平段'\n" +
//                        "when '段16' then '平段'\n" +
//                        "when '段17' then '平段'\n" +
//                        "when '段18' then '平段'\n" +
//                        "when '段19' then '尖峰'\n" +
//                        "when '段20' then '尖峰'\n" +
//                        "when '段21' then '尖峰'\n" +
//                        "when '段22' then '尖峰'\n" +
//                        "when '段23' then '高峰'\n" +
//                        "when '段24' then '谷段'\n" +
//                        "when '峰' then '高峰'\n" +
//                        "when '平' then '平段'\n" +
//                        "when '谷' then '谷段'\n" +
//                        "when '尖' then '尖峰'\n" +
//                        "else null end) as seg_name, round(sum(energy * (price + 450)) / sum(energy),2) as price")
//                .leftJoin("ml_term_contract_energy b on t.trade_id = b.trade_id");
//        buildWrapper(contractInfoReqVo, wrapper1);
//        List<ContractSegPriceDto> contractSegPriceDtos = getBaseMapper().selectJoinList(
//                ContractSegPriceDto.class,
//                wrapper1
//        );
//        return ContractPositionConvert.INSTANCE.convertContractEnergyPrice(
//                Year.of(contractInfoReqVo.getStartYear()), contractSegEnergyDtos, contractSegPriceDtos);
        List<Map> list = mlTermContractInfoMapper.selectEnergy(contractInfoReqVo);
        List<Map> list2 = mlTermContractInfoMapper.selectJc(contractInfoReqVo);
        return ContractPositionConvert.INSTANCE.convertContractEnergyPrice(list, list2);
    }


    private void buildWrapper(final ContractInfoReqVo contractInfoReqVo,
                              final MPJQueryWrapper<MlTermContractInfoDo> wrapper) {
        Optional.ofNullable(contractInfoReqVo.getStartYear()).ifPresent(num -> wrapper.ge("date_year", num));
        Optional.ofNullable(contractInfoReqVo.getEndYear()).ifPresent(num -> wrapper.le("date_year", num));
        Optional.ofNullable(contractInfoReqVo.getStartMonth()).ifPresent(num -> wrapper.ge("date_month", num));
        Optional.ofNullable(contractInfoReqVo.getEndMonth()).ifPresent(num -> wrapper.le("date_month", num));
        Optional.ofNullable(contractInfoReqVo.getArrName()).ifPresent(num -> wrapper.like("arr_name", num));
        Optional.ofNullable(contractInfoReqVo.getSellerUnitName())
                .ifPresent(num -> wrapper.eq("seller_unit_name", num));
        Optional.ofNullable(contractInfoReqVo.getPurchaseUnitName())
                .ifPresent(num -> wrapper.eq("purchase_unit_name", num));
        wrapper.groupBy("t.start_time, b.seg_name");
    }

    @Override
    public ServiceResult<Boolean> declare(String time, String energy) {
        QueryWrapper<MlSettleTotalInfoDo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_time", time);
        int i = 0;
        if (mlSettleTotalInfoMapper.selectCount(queryWrapper) > 0) {
            i = mlTermContractInfoMapper.declare(time, Double.valueOf(energy));
        } else {
            MlSettleTotalInfoDo mlSettleTotalInfoDo = new MlSettleTotalInfoDo();
            mlSettleTotalInfoDo.setDataTime(time);
            mlSettleTotalInfoDo.setYuceEnergy(new BigDecimal(energy));
            i = mlSettleTotalInfoMapper.insert(mlSettleTotalInfoDo);
        }
        return ServiceResult.success(i>0);
    }

}




