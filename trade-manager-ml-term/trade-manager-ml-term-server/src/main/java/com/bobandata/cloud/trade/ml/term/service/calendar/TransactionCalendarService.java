package com.bobandata.cloud.trade.ml.term.service.calendar;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.calendar.TransactionCalendar;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface TransactionCalendarService extends IService<TransactionCalendar> {


    Map<String,List<TransactionCalendar>> listCalendar(String dateTime);

    ServiceResult<Boolean> insert(TransactionCalendar transactionCalendar);

    ServiceResult<Boolean> updateCalendar(TransactionCalendar transactionCalendar);

    ServiceResult<Boolean> delete(Long id);
}
