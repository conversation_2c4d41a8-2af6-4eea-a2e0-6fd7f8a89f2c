package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnprice;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface HnPriceService extends IService<Hnprice> {

    ServiceResult<Boolean> insert(Hnprice hnprice);

    ServiceResult<Boolean> updateRation(Hnprice hnprice);

    ServiceResult<Boolean> delete(Long id);

    ServiceResult<List<Hnprice>> listRation(String dateTime);
}
