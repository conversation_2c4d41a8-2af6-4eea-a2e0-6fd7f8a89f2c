package com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 *响应信息表
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName(value = "m_abandon_release_info", autoResultMap = true)
public class AbandonReleaseInfoDo extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 工单编号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 工单名称
     */
    @TableField("trade_name")
    private String tradeName;

    /**
     * 运行日期
     */
    @TableField("run_date")
    private Date runDate;

    /**
     * 目标区域
     */
    @TableField("target_area")
    private String targetArea;

    /**
     * 截至反馈时间
     */
    @TableField("end_feedback_time")
    private Timestamp endFeedbackTime;

    /**
     * 时段描述
     */
    @TableField("time_desc")
    private String timeDesc;

    /**
     * 时段开始时间
     */
    @TableField("start_time_segment")
    private String startTimeSegment;

    /**
     * 时段结束时间
     */
    @TableField("end_time_segment")
    private String endTimeSegment;

    /**
     * 预测负荷缺口(万kW)
     */
    @TableField("forecast_load_gap")
    private BigDecimal forecastLoadGap;

    /**
     * 荷端邀约目标值(万kW)
     */
    @TableField("target_value")
    private BigDecimal targetValue;

}
