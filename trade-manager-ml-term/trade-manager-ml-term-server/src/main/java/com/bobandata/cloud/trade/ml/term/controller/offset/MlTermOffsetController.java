package com.bobandata.cloud.trade.ml.term.controller.offset;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.common.utils.array.RandomUtils;
import com.bobandata.cloud.trade.ml.term.controller.offset.vo.*;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo;
import com.bobandata.cloud.trade.ml.term.service.offset.OffsetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-22日 16:35
 * @description
 */
@Tag(name = "售电侧管理-用电偏差管理")
@Slf4j
@RequestMapping("/offset")
@RestController
public class MlTermOffsetController {

    @Autowired
    private OffsetService offsetService;

    @Operation(summary = "获得售电公司偏差")
    @Parameter(name = "yearMonth", description = "月份", example = "2024-01")
    @GetMapping("/total")
    public ServiceResult<ProxyOffsetRespVo> totalOffset(YearMonth yearMonth) {
        if (yearMonth == null) {
            yearMonth = YearMonth.now();
        }
        OffsetNodeVo[] data = createData();
        ProxyOffsetRespVo proxyOffsetRespVo = new ProxyOffsetRespVo()
                .setContract(data[0])
                .setReal(data[1])
                .setOffset(data[2]);
        return ServiceResult.success(proxyOffsetRespVo);
    }

    @Operation(summary = "获得用户偏差")
    @Parameter(name = "yearMonth", description = "月份", example = "2024-01")
    @Parameter(name = "consId", description = "用户ID", example = "2024-01")
    @GetMapping("/cons")
    public ServiceResult<ConsOffsetRespVo> consOffset(Long consId, YearMonth yearMonth) {
        if (yearMonth == null) {
            yearMonth = YearMonth.now();
        }
        OffsetNodeVo[] data = createData();
        ConsOffsetRespVo consOffsetRespVo = new ConsOffsetRespVo()
                .setBidder(data[0])
                .setReal(data[1])
                .setOffset(data[2]);
        return ServiceResult.success(consOffsetRespVo);
    }

    /**
     * 假数据
     *
     * @return
     */
    private OffsetNodeVo[] createData() {
        BigDecimal[] data1 = RandomUtils.randomBigDecimals(4);
        BigDecimal[] data2 = RandomUtils.randomBigDecimals(4);

        OffsetNodeVo offsetNodeVo = new OffsetNodeVo()
                .setHighestEnergy(data1[0])
                .setPeakEnergy(data1[1])
                .setFlatEnergy(data1[2])
                .setValleyEnergy(data1[3]);

        OffsetNodeVo offsetNodeVo1 = new OffsetNodeVo()
                .setHighestEnergy(data2[0])
                .setPeakEnergy(data2[1])
                .setFlatEnergy(data2[2])
                .setValleyEnergy(data2[3]);

        OffsetNodeVo offsetNodeVo2 = new OffsetNodeVo()
                .setHighestEnergy(data1[0].subtract(data2[0]))
                .setPeakEnergy(data1[0].subtract(data2[1]))
                .setFlatEnergy(data1[0].subtract(data2[2]))
                .setValleyEnergy(data1[0].subtract(data2[3]));
        return new OffsetNodeVo[] {
                offsetNodeVo,
                offsetNodeVo1,
                offsetNodeVo2
        };

    }


    @Operation(summary = "电量统计-用户")
    @PostMapping("/listDlTotal")
    public ServiceResult<List<OffsetRespVo>> listDlTotal(@RequestBody OffsetReqVo offsetReqVo) {
        List<OffsetRespVo> mlSettleTotalInfoDos = offsetService.listDlTotal(offsetReqVo);
        return ServiceResult.success(mlSettleTotalInfoDos);
    }

}
