package com.bobandata.cloud.trade.ml.term.controller.abandon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:05
 * @Classname AbandonReleaseInfoRespVo
 * @Description
 */
@Schema(description = "需求响应-响应信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class AbandonReleaseInfoRespVo {


    private Long id;
    /**
     * 工单编号
     */
    private String tradeNo;

    /**
     * 工单名称
     */
    private String tradeName;

    /**
     * 运行日期
     */
    private Date runDate;

    /**
     * 目标区域
     */
    private String targetArea;

    /**
     * 截至反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endFeedbackTime;

    private List<SegmentNodeVo> segmentNodeVoList;

}
