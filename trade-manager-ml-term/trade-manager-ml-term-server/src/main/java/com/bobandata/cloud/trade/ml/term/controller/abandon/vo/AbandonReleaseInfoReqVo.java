package com.bobandata.cloud.trade.ml.term.controller.abandon.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.sql.Date;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:05
 * @Classname AbandonReleaseInfoRespVo
 * @Description
 */
@Schema(description = "需求响应-响应信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AbandonReleaseInfoReqVo extends PageParam {

    @Schema(description = "运行日")
    private Date runDate;

}
