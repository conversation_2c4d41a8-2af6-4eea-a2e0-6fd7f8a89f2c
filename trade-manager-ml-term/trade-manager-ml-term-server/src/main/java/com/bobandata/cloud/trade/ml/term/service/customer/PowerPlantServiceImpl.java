package com.bobandata.cloud.trade.ml.term.service.customer;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.PowerPlantReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.PowerPlant;
import com.bobandata.cloud.trade.ml.term.dal.mysql.customer.PowerPlantMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service实现
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class PowerPlantServiceImpl extends BaseServiceImpl<PowerPlantMapper, PowerPlant> implements PowerPlantService {

    @Autowired
    private PowerPlantMapper powerPlantMapper;

    @Override
    public PagingResult<PowerPlant> listPowerPlant(PowerPlantReqVo reqVo) {
        return this.getBaseMapper().selectPage(reqVo);
    }

    @Override
    public ServiceResult<Boolean> insert(PowerPlant powerPlant) {
        if(this.getBaseMapper().insert(powerPlant)>0){
            return ServiceResult.success(true);
        }
        return ServiceResult.success(true);
    }

    @Override
    public ServiceResult<Boolean> updatePowerPlant(PowerPlant powerPlant) {
        return ServiceResult.success(this.getBaseMapper().updateById(powerPlant)>0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(this.getBaseMapper().deleteById(id)>0);
    }

    @Override
    public List<Map> getPlantType() {
        return powerPlantMapper.getPlantType();
    }

    @Override
    public List<Map> getFdjt() {
        return powerPlantMapper.getFdjt();
    }


}




