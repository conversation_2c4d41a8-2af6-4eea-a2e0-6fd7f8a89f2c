package com.bobandata.cloud.trade.ml.term.service.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ConsDayEnergyReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ConsDayEnergyDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.othermix.ConsDayEnergyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/29 15:42
 * @Classname ConsDayEnergyServiceImpl
 * @Description
 */
@Service
public class ConsDayEnergyServiceImpl implements ConsDayEnergyService {

    @Resource
    private ConsDayEnergyMapper consDayEnergyMapper;

    @Override
    public List<ConsDayEnergyDo> selectList(ConsDayEnergyReqVo reqVO) {
        return consDayEnergyMapper.selectList(reqVO);
    }
}
