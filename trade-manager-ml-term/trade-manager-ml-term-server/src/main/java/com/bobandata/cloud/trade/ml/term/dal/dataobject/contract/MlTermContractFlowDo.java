package com.bobandata.cloud.trade.ml.term.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import lombok.Data;

/**
 * 中长期交易：交易合同流水信息
 *
 * @TableName ml_term_contract_flow
 */
@TableName(value = "ml_term_contract_flow")
@Data
public class MlTermContractFlowDo extends BaseDo {

    /**
     *
     */
    @TableField(value = "guid")
    private String guid;

    /**
     * 交易序列
     */
    @TableField(value = "tradeseq_id")
    private String tradeSegId;

    /**
     *
     */
    @TableField(value = "sale_units_code")
    private String saleUnitsCode;

    /**
     *
     */
    @TableField(value = "sale_units_name")
    private String saleUnitsName;

    /**
     *
     */
    @TableField(value = "vendee_units_code")
    private String vendeeUnitsCode;

    /**
     *
     */
    @TableField(value = "vendee_units_name")
    private String vendeeUnitsName;

    /**
     *
     */
    @TableField(value = "sale_participant_id")
    private String saleParticipantId;

    /**
     *
     */
    @TableField(value = "sale_participant_name")
    private String saleParticipantName;

    /**
     *
     */
    @TableField(value = "vendee_participant_id")
    private String vendeeParticipantId;

    /**
     *
     */
    @TableField(value = "vendee_participant_name")
    private String vendeeParticipantName;

    /**
     *
     */
    @TableField(value = "vendee_energy")
    private BigDecimal vendeeEnergy;

    /**
     *
     */
    @TableField(value = "sale_energy")
    private BigDecimal saleEnergy;

    /**
     *
     */
    @TableField(value = "vendee_price")
    private BigDecimal vendeePrice;

    /**
     *
     */
    @TableField(value = "sale_price")
    private BigDecimal salePrice;

    /**
     *
     */
    @TableField(value = "contract_create_time")
    private Timestamp contractCreateTime;

    /**
     *
     */
    @TableField(value = "time_division_code")
    private String timeDivisionCode;

    /**
     *
     */
    @TableField(value = "time_division_name")
    private String timeDivisionName;

    /**
     *
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     *
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     *
     */
    @TableField(value = "tradeseq_caption")
    private String tradeseqCaption;

}