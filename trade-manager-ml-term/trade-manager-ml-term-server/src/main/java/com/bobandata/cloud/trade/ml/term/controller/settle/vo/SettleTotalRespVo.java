package com.bobandata.cloud.trade.ml.term.controller.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.YearMonth;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:02
 * @description
 */
@Schema(description = "结算管理-正式结算单查询 结算单列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SettleTotalRespVo {

    @Schema(description = "结算单月份")
    private String settleMonth;

    /**
     * 实际用电量
     */
    @Schema(description = "实际用电量", example = "保留三位小数")
    private BigDecimal actualEnergy;

    /**
     * 结算电量
     */
    @Schema(description = "结算电量", example = "保留三位小数")
    private BigDecimal settleEnergy;

    /**
     * 合同电量
     */
    @Schema(description = "合同电量", example = "保留三位小数")
    private BigDecimal contractEnergy;

    /**
     * 偏差用电量
     */
    @Schema(description = "偏差用电量", example = "保留三位小数")
    private BigDecimal offsetEnergy;

    /**
     * 结算电费
     */
    @Schema(description = "结算电费", example = "保留两位位小数")
    private BigDecimal settleFee;

    /**
     * 电量比例
     */
    @Schema(description = "电量比例")
    private String dlbl;

    /**
     * 收益比例
     */
    @Schema(description = "收益比例")
    private String sybl;
}
