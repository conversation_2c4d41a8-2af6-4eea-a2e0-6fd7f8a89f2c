package com.bobandata.cloud.trade.ml.term.service.photovoltaic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.internal.DataCurveConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvInverterDataDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic.PvInverterDataMapper;
import com.bobandata.cloud.trade.ml.term.service.photovoltaic.dto.PvDayPointCurve;
import java.sql.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:09
 * @Classname ChargeInverterDataServiceImpl
 * @Description
 */
@Service
public class PvInverterDataServiceImpl extends ServiceImpl<PvInverterDataMapper, PvInverterDataDo> implements PvInverterDataService {

    @Resource
    private PvInverterDataMapper chargeInverterDataMapper;

    @Override
    public List<KeyValueVo> getDayCurve(Date dataDate) {
        List<PvDayPointCurve> pvDayPointCurves = chargeInverterDataMapper.selectDayEnergy(dataDate);
        return DataCurveConvert.convertLabelValue(
                pvDayPointCurves.toArray(new PvDayPointCurve[0]), "name");
    }

    @Override
    public void saveOrUpdateData(List<PvInverterDataDo> data) {
        for (PvInverterDataDo item : data) {
            LambdaQueryWrapper<PvInverterDataDo> query = Wrappers.lambdaQuery(
                PvInverterDataDo.class);
            query.eq(PvInverterDataDo::getInvId, item.getInvId())
                .eq(PvInverterDataDo::getCurveDate, item.getCurveDate())
                .eq(PvInverterDataDo::getCurveType, item.getCurveType());
            PvInverterDataDo existingData = this.getOne(query);
            if (existingData == null) {
                this.save(item);
            } else {
                item.setId(existingData.getId());
                this.updateById(item);
            }
        }
    }
}
