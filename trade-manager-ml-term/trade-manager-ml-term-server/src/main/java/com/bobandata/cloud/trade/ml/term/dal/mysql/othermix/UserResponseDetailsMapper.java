package com.bobandata.cloud.trade.ml.term.dal.mysql.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.UserResponseDetailsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.UserResponseDetailsDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:02
 * @Classname UserResponseDetailsMapper
 * @Description
 */
public interface UserResponseDetailsMapper extends BaseCrudMapper<UserResponseDetailsDo> {

    default PagingResult<UserResponseDetailsDo> selectPage(ResponseEvaluationReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<UserResponseDetailsDo>()
            .likeIfPresent(UserResponseDetailsDo::getConsId, reqVO.getConsId())
            .likeIfPresent(UserResponseDetailsDo::getConsName, reqVO.getConsName())
            .likeIfPresent(UserResponseDetailsDo::getIntegratorName, reqVO.getIntegratorName())
            .eqIfPresent(UserResponseDetailsDo::getDispatchDate, reqVO.getDispatchDate())
            .orderByDesc(UserResponseDetailsDo::getDispatchDate));
    }
}
