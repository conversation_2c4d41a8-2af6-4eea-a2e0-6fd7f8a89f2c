package com.bobandata.cloud.trade.ml.term.service.calendar;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.calendar.TransactionCalendar;
import com.bobandata.cloud.trade.ml.term.dal.mysql.calendar.TransactionCalendarMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service实现
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class TransactionCalendarServiceImpl extends BaseServiceImpl<TransactionCalendarMapper, TransactionCalendar> implements TransactionCalendarService {

    @Autowired
    TransactionCalendarMapper transactionCalendarMapper;


    @Override
    public Map<String,List<TransactionCalendar>> listCalendar(String dateTime) {
        QueryWrapper<TransactionCalendar> wrapper = new QueryWrapper<>();
        wrapper.like("data_time", dateTime);
        List<TransactionCalendar> list = this.list(wrapper);
        Map<String, List<TransactionCalendar>> result = list.stream()
                .collect(Collectors.groupingBy(TransactionCalendar::getDate));
        Map<String, List<TransactionCalendar>> sortedMap = new TreeMap<>(result);
        return sortedMap;
    }

    @Override
    public ServiceResult<Boolean> insert(TransactionCalendar transactionCalendar) {
        QueryWrapper<TransactionCalendar> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_time",transactionCalendar.getDate());
        int insert = 0;
        if(transactionCalendarMapper.selectCount(queryWrapper) == 0){
            insert = transactionCalendarMapper.insert(transactionCalendar);
        }else {
            UpdateWrapper<TransactionCalendar> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("data_time",transactionCalendar.getDate());
            insert= transactionCalendarMapper.update(transactionCalendar, updateWrapper);
        }
        return ServiceResult.success(insert >0);
    }

    @Override
    public ServiceResult<Boolean> updateCalendar(TransactionCalendar transactionCalendar) {
        return ServiceResult.success(transactionCalendarMapper.updateById(transactionCalendar)>0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(transactionCalendarMapper.deleteById(id)>0);
    }
}




