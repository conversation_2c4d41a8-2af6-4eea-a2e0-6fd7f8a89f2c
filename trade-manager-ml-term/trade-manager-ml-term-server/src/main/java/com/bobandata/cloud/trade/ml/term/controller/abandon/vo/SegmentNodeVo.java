package com.bobandata.cloud.trade.ml.term.controller.abandon.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bobandata.cloud.trade.curve.core.handler.TimeSegmentDeserialize;
import com.bobandata.cloud.trade.curve.core.handler.TimeSegmentSerialize;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:53
 * @Classname SegmentNodeVo
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SegmentNodeVo implements Serializable {

    /**
     * 时段描述
     */

    @NotBlank(message = "时段描述为空")
    private String timeDesc;

    /**
     * 时段开始时间
     */
    @NotBlank(message = "时段开始时间为空")
    private String startTimeSegment;

    /**
     *
     */
    @NotBlank(message = "时段结束时间为空")
    private String endTimeSegment;

    /**
        * 预测负荷缺口(万kW)
     */
    private BigDecimal forecastLoadGap;

    /**
        * 荷端邀约目标值(万kW)
     */
    private BigDecimal targetValue;
}
