package com.bobandata.cloud.trade.ml.term.controller.settle;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.SettleBatchRetailRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.SettleTotalRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo;
import com.bobandata.cloud.trade.ml.term.convert.SettleTotalConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleRetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleTotalInfoMapper;
import com.bobandata.cloud.trade.ml.term.service.settle.MlSettleBatchService;
import com.bobandata.cloud.trade.ml.term.service.settle.MlSettleRetailService;
import com.bobandata.cloud.trade.ml.term.service.settle.MlSettleTotalInfoService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-19日 09:56
 * @description
 */
@Tag(name = "结算管理-正式结算单查询")
@Slf4j
@RequestMapping("/settle")
@RestController
public class MlSettleTotalController extends BaseCrudRestController<MlSettleTotalInfoDo, MlSettleTotalInfoMapper> {

    @Autowired
    private MlSettleTotalInfoService totalInfoService;

    @Autowired
    private MlSettleBatchService batchService;

    @Autowired
    private MlSettleRetailService retailService;

    @Operation(summary = "获得每月结算单汇总")
    @Parameter(name = "month", description = "月份", example = "yyyy-MM格式")
    @GetMapping("/total")
    public ServiceResult<List<SettleTotalRespVo>> listSettleTotal(@RequestParam(value = "month", required = false) String month) {
        List<MlSettleTotalInfoDo> mlSettleTotalInfoDos = totalInfoService.listByMonth(month);
        return ServiceResult.success(SettleTotalConvert.INSTANCE.convertTotalVo(mlSettleTotalInfoDos));
    }

    @Operation(summary = "获得月度购电售电结算单")
    @Parameter(name = "month", description = "月份", example = "yyyy-MM格式", required = true)
    @GetMapping("/detail")
    public ServiceResult<SettleBatchRetailRespVo> getSettleBatchAndRetail(@RequestParam(value = "month") String month) {
        List<MlSettleBatchDo> mlSettleBatchDos = batchService.listByMonth(month);
        List<MlSettleRetailDo> mlSettleRetailDos = retailService.listByMonth(month);
        return ServiceResult.success(SettleTotalConvert.INSTANCE.convertRespVo(month, mlSettleBatchDos, mlSettleRetailDos));
    }

    @Operation(summary = "获得每月交易情况")
    @Parameter(name = "month", description = "月份", example = "yyyy-MM格式")
    @GetMapping("/listJyTotal")
    public ServiceResult<List<TransactionRespVo>> listJyTotal(@RequestParam(value = "month") String month, @RequestParam(value = "displayName", required = false) String displayName, @RequestParam(value = "plantType", required = false) String plantType) {
        List<TransactionRespVo> mlSettleTotalInfoDos = totalInfoService.listJyTotal(month, displayName, plantType);
        return ServiceResult.success(mlSettleTotalInfoDos);
    }

    @Operation(summary = "获得交易电量偏差统计")
    @GetMapping("/listJyPcTotal")
    public ServiceResult<List<TransactionsBiasRespVo>> listJyPcTotal(@RequestParam(value = "year") String year) {
        List<TransactionsBiasRespVo> mlSettleTotalInfoDos = totalInfoService.listJyPcTotal(year);
        return ServiceResult.success(mlSettleTotalInfoDos);
    }

    @Operation(summary = "获得交易类型")
    @GetMapping("/listJyType")
    public ServiceResult<List<Map>> listJyType() {
        List<Map> list = totalInfoService.listJyType();
        return ServiceResult.success(list);
    }

    @Operation(summary = "导入交易结算单")
    @PostMapping("/importJs")
    public ServiceResult<Boolean> importJs(@RequestParam(value = "file", required = true) MultipartFile multipartFile) {
        return totalInfoService.importJs2(multipartFile);
    }

}
