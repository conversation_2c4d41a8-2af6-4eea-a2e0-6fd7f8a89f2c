package com.bobandata.cloud.trade.ml.term.controller.customer.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class IntendedCustomersReqVo extends PageParam {

    @Schema(description = "用户户名")
    protected String consName;

    @Schema(description = "用户户名或户号")
    protected String key;

    @Schema(description = "电压等级")
    protected String level;

    protected String elecTypeBigcls;

    @Schema(description = "经办人")
    private String persion;


}
