package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.utils.date.YearMonthUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.Year;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-03-19日 15:18
 * @description
 */
@Schema(description = "中长期管理-合同持仓管理 电量电价图")
@Data
@EqualsAndHashCode
public class ContractEnergyPriceRespVo {

    @Schema(description = "竞价")
    private KeyValueVo jingjia;

    @Schema(description = "双边协商")
    private KeyValueVo sbxs;

    @Schema(description = "挂牌")
    private KeyValueVo gp;

    @Schema(description = "新能源")
    private KeyValueVo newEnergy;

    @Schema(description = "绿电")
    private KeyValueVo greenEnergy;

    @Schema(description = "火电")
    private KeyValueVo fireEnergy;

    @Schema(description = "均价差")
    private KeyValueVo jjc;

    @Schema(description = "累计均价差")
    private KeyValueVo ljjjc;

    public ContractEnergyPriceRespVo(List<String> yearMonths) {
        this.jingjia = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.sbxs = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.gp = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.newEnergy = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.greenEnergy = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.fireEnergy = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.jjc = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
        this.ljjjc = KeyValueVo.build(yearMonths, BigDecimal.ZERO);
    }
}
