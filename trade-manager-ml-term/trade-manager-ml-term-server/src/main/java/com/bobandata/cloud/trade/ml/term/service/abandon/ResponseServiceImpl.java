package com.bobandata.cloud.trade.ml.term.service.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.ResponseReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseDetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.ResponseDetailMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.ResponseInfoMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/11/16 11:54
 * @Description
 */
@Service
public class ResponseServiceImpl implements ResponseService {

    @Resource
    private ResponseInfoMapper responseInfoMapper;

    @Resource
    private ResponseDetailMapper responseDetailMapper;


    @Override
    public PagingResult<ResponseInfoDo> getResponseInfoPage(ResponseReqVo reqVo) {
        return responseInfoMapper.selectPage(reqVo);
    }

    @Override
    public PagingResult<ResponseDetailDo> getResponseDetailPage(ResponseReqVo reqVo) {
        return responseDetailMapper.selectPage(reqVo);
    }
}
