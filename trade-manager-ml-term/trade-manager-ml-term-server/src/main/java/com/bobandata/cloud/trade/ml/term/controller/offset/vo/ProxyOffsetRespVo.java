package com.bobandata.cloud.trade.ml.term.controller.offset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-22日 16:43
 * @description
 */
@Tag(name = "售电侧管理-用电偏差管理 售电公司偏差")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ProxyOffsetRespVo {

    @Schema(description = "合同电量")
    private OffsetNodeVo contract;

    @Schema(description = "实际电量")
    private OffsetNodeVo real;

    @Schema(description = "偏差电量")
    private OffsetNodeVo offset;
}
