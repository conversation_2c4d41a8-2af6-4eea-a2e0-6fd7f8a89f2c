package com.bobandata.cloud.trade.ml.term.controller.customer;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.ConsRespVo;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.IntendedCustomersReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.IntendedCustomers;
import com.bobandata.cloud.trade.ml.term.dal.mysql.customer.IntendedCustomersMapper;
import com.bobandata.cloud.trade.ml.term.service.customer.IntendedCustomersService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "客户信息管理-意向客户管理")
@Slf4j
@RequestMapping("/customer")
@RestController
public class IntendedCustomersController extends BaseCrudRestController<IntendedCustomers, IntendedCustomersMapper> {

    @Autowired
    private IntendedCustomersService intendedCustomersService;

    @Operation(summary = "新增意向客户")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertIntendedCustomer(@RequestBody IntendedCustomers intendedCustomers) {
        return intendedCustomersService.insert(intendedCustomers);
    }

    @Operation(summary = "修改意向客户")
    @PostMapping("/update")
    public ServiceResult<Boolean> updateIntendedCustomer(@RequestBody IntendedCustomers intendedCustomers) {
        return intendedCustomersService.updateIntendedCustomer(intendedCustomers);
    }

    @Operation(summary = "删除意向客户")
    @GetMapping("/delete")
    public ServiceResult<Boolean> deleteIntendedCustomer(@RequestParam("id") Long id) {
        return intendedCustomersService.delete(id);
    }
    @Operation(summary = "list")
    @GetMapping("/list")
    public PagingResult<IntendedCustomers> listIntendedCustomer(@Valid IntendedCustomersReqVo reqVo) {
        return intendedCustomersService.listIntendedCustomer(reqVo);
    }

    /**
     * 获取用户户名和户号
     */
    @Operation(summary = "getConsNameAndNo")
    @GetMapping("/getConsNameAndNo")
    public ServiceResult<List<ConsRespVo>> getConsNameAndNo(String key) {
        return intendedCustomersService.getConsNameAndNo(key);
    }

    /**
     * 意向客户转为正式客户
     */
    @Operation(summary = "toCons")
    @GetMapping("/toCons")
    public ServiceResult<Boolean> toCons(@RequestParam("id") Long id) {
        return intendedCustomersService.toCons(id);
    }

    @GetMapping("/getLevel")
    @Operation(summary = "电压等级")
    public ServiceResult<List<Map>> getLevel() {
        List<Map> list = intendedCustomersService.getLevel();
        return success(list);
    }

    @GetMapping("/getYd")
    @Operation(summary = "用电类别")
    public ServiceResult<List<Map>> getYd() {
        List<Map> list = intendedCustomersService.getYd();
        return success(list);
    }
}
