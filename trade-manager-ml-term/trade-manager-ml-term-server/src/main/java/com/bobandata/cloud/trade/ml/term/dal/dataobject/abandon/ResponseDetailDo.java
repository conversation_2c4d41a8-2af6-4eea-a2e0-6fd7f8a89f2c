package com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.common.core.deserialize.BooleanStrSerialize;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 响应明细
 *
 * <AUTHOR>
 * @since 2024-11-16
 */
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("m_response_detail")
public class ResponseDetailDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 用户名称（可为 null）负荷集成商
     */
    @TableField("user_name")
    private String userName;

    /**
     * 户号
     */
    @TableField("cons_no")
    private String consNo;

    /**
     * 户名
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 电压等级
     */
    private String level;

    /**
     * 所处地市
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 合同容量
     */
    @TableField("contract_cap")
    private BigDecimal contractCap;

    /**
     * 响应时间，如 2024-05-30
     */
    @TableField("bal_date")
    private Date balDate;

    /**
     * 最大负荷
     */
    @TableField("max_load")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @TableField("min_load")
    private BigDecimal minLoad;

    /**
     * 是否具有上调节能力
     */
    @TableField("have_up")
    @JsonSerialize(using = BooleanStrSerialize.class)
    private Boolean haveUp;

    /**
     * 上调节能力
     */
    @TableField("up_regulation")
    private BigDecimal upRegulation;

    /**
     * 是否具有下调节能力
     */
    @TableField("have_down")
    @JsonSerialize(using = BooleanStrSerialize.class)
    private Boolean haveDown;

    /**
     * 下调节能力
     */
    @TableField("down_regulation")
    private BigDecimal downRegulation;

    /**
     * 上调响应时段初始值 00:00-06:00
     */
    @TableField("start_period")
    private String startPeriod;

    /**
     * 下调响应时段初始值
     */
    @TableField("end_period")
    private String endPeriod;

    /**
     * 用户意愿
     */
    @TableField("user_intention")
    private String userIntention;

    /**
     * 填谷优先级
     */
    @TableField("fill_level")
    private Integer fillLevel;

    /**
     * 削峰优先级
     */
    @TableField("shaving_level")
    private Integer shavingLevel;

    @TableField("just_rate")
    private String justRate;

    @TableField(exist = false)
    private BigDecimal segmentScale;

    public TimeSegment getTimeSegment(int type) {
        if (type == 1) {
            return TwentyFourPointTimeSegment.valueOf(this.endPeriod);
        } else {
            return TwentyFourPointTimeSegment.valueOf(this.startPeriod);
        }
    }
}
