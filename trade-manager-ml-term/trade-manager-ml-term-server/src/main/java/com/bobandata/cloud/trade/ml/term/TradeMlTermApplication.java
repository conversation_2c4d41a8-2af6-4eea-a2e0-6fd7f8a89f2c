package com.bobandata.cloud.trade.ml.term;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2024-03-12日 13:28
 * @description
 */
@MapperScan(basePackages = {
        "com.bobandata.cloud.trade.**.dal.mysql"
})
@SpringBootApplication(scanBasePackages = "com.bobandata")
@EnableFeignClients
public class TradeMlTermApplication {

    public static void main(String[] args) {
        SpringApplication.run(TradeMlTermApplication.class, args);
    }

    @Bean
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }
}
