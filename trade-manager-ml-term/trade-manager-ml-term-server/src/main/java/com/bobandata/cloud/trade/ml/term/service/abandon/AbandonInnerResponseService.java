package com.bobandata.cloud.trade.ml.term.service.abandon;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonInnerResponseDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.AbandonInnerResponseMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 13:58
 * @Classname MlTermAbandonInnerResponseService
 * @Description
 */
public interface AbandonInnerResponseService extends BaseService<AbandonInnerResponseMapper, AbandonInnerResponseDo> {

    PagingResult<AbandonInnerResponseDo> getAbandonInnerResponsePage(AbandonReleaseInfoReqVo reqVo);

    ServiceResult updFeedBackStatus(Long id);

}
