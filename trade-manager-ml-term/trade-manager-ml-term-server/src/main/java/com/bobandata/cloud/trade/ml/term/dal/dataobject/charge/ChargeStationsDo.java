package com.bobandata.cloud.trade.ml.term.dal.dataobject.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <p>
 * 充电站信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charge_stations")
public class ChargeStationsDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 电站编号
     */
    @TableField("station_number")
    private String stationNumber;

    /**
     * 电站名称
     */

    @JsonProperty(value = "name")
    @TableField("station_name")
    private String stationName;

    /**
     * 运行类型/运营模式
     */
    @TableField("run_type")
    private String runType;

    /**
     * 运营商
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 经营状态
     */
    private String status;

    /**
     * 电站类型
     */
    @TableField("station_type")
    private String stationType;

    /**
     * 发布状态
     */
    @TableField("publish_status")
    private String publishStatus;

    /**
     * 服务时间/投运时间
     */
    @TableField("publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp publishTime;

    /**
     * 未找到-建设场所
     */
    @TableField("construction_place")
    private String constructionPlace;

    /**
     * 区域
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 详细地址
     */
    @TableField("area_detail_name")
    private String areaDetailName;

    /**
     * 充电桩总数
     */
    @TableField("pile_sum")
    private Integer pileSum;

    /**
     * 充电枪总数
     */
    @TableField("gun_sum")
    private Integer gunSum;

    /**
     * 设备总功率(kW)
     */
    @JsonProperty(value = "pilePowerSum")
    @TableField("device_total_power")
    private BigDecimal deviceTotalPower;

    /**
     * 车位数量
     */
    @TableField("vehicle_number")
    private Integer vehicleNumber;

    /**
     * 支付方式
     */
    private String payment;

    /**
     * 不确定-联系人
     */
    @JsonProperty(value = "manager_name")
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 不确定-联系电话
     */
    @JsonProperty(value = "manager_phone")
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 服务电话
     */
    @TableField("service_phone")
    private String servicePhone;

    /**
     * 备注
     */
    private String remark;

}
