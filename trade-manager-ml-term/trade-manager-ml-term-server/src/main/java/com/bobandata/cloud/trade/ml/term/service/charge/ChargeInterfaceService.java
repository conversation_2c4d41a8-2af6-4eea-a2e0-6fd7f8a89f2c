package com.bobandata.cloud.trade.ml.term.service.charge;

import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargePilesDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeStationsDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/4 10:03
 * @Classname ChargeInterfaceService
 * @Description
 */
public interface ChargeInterfaceService {

    /**
     *
     * @param reqVo
     * @param dataType 1站 2桩 3订单
     * @return
     */
    List<Object> getChargeDataList(ChargeStationReqVo reqVo, int dataType);

    void saveOrUpdateStationsData(List<ChargeStationsDo> doList);

    void saveOrUpdatePilesData(List<ChargePilesDo> doList);

    void saveOrUpdateBillsData(List<ChargeRecordsDo> doList);

    List<Long> getStationIds();

}
