package com.bobandata.cloud.trade.ml.term.service.charge;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeBillRespVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeIntefaceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStationReqVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.LoginUserReqVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.LoginUserRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargePilesDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeStationsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargePilesMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeRecordsMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeStationsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/11/4 10:04
 * @Classname ChargeInterfaceServiceImpl
 * @Description
 */
@Service
public class ChargeInterfaceServiceImpl implements ChargeInterfaceService {

    @Autowired
    private ChargeInterfaceApi chargeInterfaceApi;

    @Value("${thirdparty.login.account}")
    private String username;

    @Value("${thirdparty.login.password}")
    private String password;

    @Value("${thirdparty.login.sellerNumber}")
    private String sellerNumber;

    @Value("${thirdparty.login.smsCaptchaPass}")
    private Boolean smsCaptchaPass;

    @Resource
    private ChargeStationsMapper chargeStationsMapper;

    @Resource
    private ChargePilesMapper chargePilesMapper;

    @Resource
    private ChargeRecordsMapper chargeRecordsMapper;

    @Override
    public List<Object> getChargeDataList(ChargeStationReqVo reqVo, int dataType) {
        List<Object> allData = new ArrayList<>();
        int pageNo = 1;
        try {
            String token = this.getAuthToken();
            reqVo.setCurrent(pageNo);
            if (dataType == 3) {
                ResponseEntity<ChargeBillRespVo> billRespVoResponseEntity = chargeInterfaceApi.getBillList(reqVo, token);
                ChargeBillRespVo billRespVo = billRespVoResponseEntity.getBody();
                if (billRespVo != null && CollUtil.isNotEmpty(billRespVo.getData())) {
                    allData.addAll(billRespVo.getData());
                    Integer pages = billRespVo.getPages();
                    if (pages != null) {
                        for (int i = 1; i < pages; i++) {
                            reqVo.setCurrent(pageNo + i);
                            ResponseEntity<ChargeBillRespVo> pageResponseEntity = chargeInterfaceApi.getBillList(reqVo, token);
                            ChargeBillRespVo pageBody = pageResponseEntity.getBody();
                            if (pageBody != null && CollUtil.isNotEmpty(billRespVo.getData())) {
                                allData.addAll(pageBody.getData());
                            }
                        }
                    }
                }
                return allData;
            }
            ResponseEntity<ChargeIntefaceRespVo> responseEntity = this.chooseApiMethod(reqVo, token, dataType);
            if (responseEntity.getBody() != null && responseEntity.getBody().getCode() != 0) {
                throw new RuntimeException("调用接口失败");
            }
            ChargeIntefaceRespVo body = responseEntity.getBody();
            if (dataType == 1) {
                allData.addAll(body.getData().getData());
            } else if (dataType == 2) {
                allData.addAll(body.getData().getRecords());
            }
            Integer pages = body.getData().getPages();
            if (pages != null) {
                for (int i = 1; i < pages; i++) {
                    reqVo.setCurrent(pageNo + i);
                    ResponseEntity<ChargeIntefaceRespVo> pageResponseEntity = this.chooseApiMethod(reqVo, token, dataType);
                    ChargeIntefaceRespVo pageBody = pageResponseEntity.getBody();
                    if (pageBody != null && pageBody.getData() != null) {
                        if (dataType == 1) {
                            allData.addAll(pageBody.getData().getData());
                        } else if (dataType == 2) {
                            allData.addAll(pageBody.getData().getRecords());
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("调用接口失败", e);
        }
        return allData;
    }

    private String getAuthToken() {
        LoginUserReqVo reqVo = LoginUserReqVo.builder()
            .sellerNumber(sellerNumber)
            .account(username)
            .password(password)
            .smsCaptchaPass(smsCaptchaPass).build();
        ResponseEntity<LoginUserRespVo> response = chargeInterfaceApi.getAuthToken(reqVo);
        if (response.getStatusCodeValue() == HttpStatus.OK.value()
            && response.getBody() != null && response.getBody().getData() != null) {
            return response.getBody().getData();
        } else {
            throw new RuntimeException("获取token接口失败");
        }
    }

    private ResponseEntity<ChargeIntefaceRespVo> chooseApiMethod(ChargeStationReqVo reqVo, String token, int dataType) {
        if (dataType == 1) {
            return chargeInterfaceApi.getStationList(reqVo, token);
        } else if (dataType == 2) {
            return chargeInterfaceApi.getPilesList(reqVo, token);
        } else {
            throw new RuntimeException("数据类型不存在");
        }
    }

    @Override
    public void saveOrUpdateStationsData(List<ChargeStationsDo> doList) {
        for (ChargeStationsDo item : doList) {
            ChargeStationsDo existingData = chargeStationsMapper.selectById(item.getId());
            if (existingData != null) {
                chargeStationsMapper.updateById(item);
            } else {
                chargeStationsMapper.insert(item);
            }
        }
    }

    @Override
    public void saveOrUpdatePilesData(List<ChargePilesDo> doList) {
        for (ChargePilesDo item : doList) {
            ChargePilesDo existingData = chargePilesMapper.selectById(item.getId());
            if (existingData != null) {
                chargePilesMapper.updateById(item);
            } else {
                chargePilesMapper.insert(item);
            }
        }
    }

    @Override
    public void saveOrUpdateBillsData(List<ChargeRecordsDo> doList) {
        for (ChargeRecordsDo item : doList) {
            ChargeRecordsDo existingData = chargeRecordsMapper.selectById(item.getId());
            if (existingData != null) {
                chargeRecordsMapper.updateById(item);
            } else {
                chargeRecordsMapper.insert(item);
            }
        }
    }

    @Override
    public List<Long> getStationIds() {
        return chargeStationsMapper.selectList().stream().map(ChargeStationsDo::getId).collect(Collectors.toList());
    }
}
