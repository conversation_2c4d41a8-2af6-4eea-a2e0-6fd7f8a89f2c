package com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvStationInfoDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import java.util.List;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:07
 * @Classname ChargeStationInfoMapper
 * @Description
 */
public interface PvStationInfoMapper extends BaseCrudMapper<PvStationInfoDo> {

    @Select("SELECT CASE\n" +
            "           WHEN round(capatity / 1000, 3) >= 0 AND round(capatity / 1000, 3) < 20 THEN '装机容量0-20kw'\n" +
            "           WHEN round(capatity / 1000, 3) >= 20 AND round(capatity / 1000, 3) < 25 THEN '装机容量20-25kw'\n" +
            "           WHEN round(capatity / 1000, 3) >= 25 AND round(capatity / 1000, 3) < 30 THEN '装机容量25-30kw'\n" +
            "           WHEN round(capatity / 1000, 3) >= 30 AND round(capatity / 1000, 3) < 50 THEN '装机容量30-50kw'\n" +
            "           WHEN round(capatity / 1000, 3) >= 50 THEN '装机容量>=50kw'\n" +
            "           ELSE '300+'\n" +
            "           END  AS capatity,\n" +
            "       COUNT(1) AS count\n" +
            "FROM pv_station_info\n" +
            "GROUP BY CASE\n" +
            "             WHEN round(capatity / 1000, 3) >= 0 AND round(capatity / 1000, 3) < 20 THEN '装机容量0-20kw'\n" +
            "             WHEN round(capatity / 1000, 3) >= 20 AND round(capatity / 1000, 3) < 25 THEN '装机容量20-25kw'\n" +
            "             WHEN round(capatity / 1000, 3) >= 25 AND round(capatity / 1000, 3) < 30 THEN '装机容量25-30kw'\n" +
            "             WHEN round(capatity / 1000, 3) >= 30 AND round(capatity / 1000, 3) < 50 THEN '装机容量30-50kw'\n" +
            "             WHEN round(capatity / 1000, 3) >= 50 THEN '装机容量>=50kw'\n" +
            "             ELSE '300+'\n" +
            "             END")
    List<KeyValueVo> selectCapGroup();
}
