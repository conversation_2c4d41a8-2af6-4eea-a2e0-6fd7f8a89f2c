package com.bobandata.cloud.trade.ml.term.service.charge;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeRecordsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【charge_records(充电订单信息表)】的数据库操作Service实现
 * @createDate 2024-11-04 14:20:45
 */
@Service
public class ChargeRecordsServiceImpl extends ServiceImpl<ChargeRecordsMapper, ChargeRecordsDo>
        implements ChargeRecordsService {

}




