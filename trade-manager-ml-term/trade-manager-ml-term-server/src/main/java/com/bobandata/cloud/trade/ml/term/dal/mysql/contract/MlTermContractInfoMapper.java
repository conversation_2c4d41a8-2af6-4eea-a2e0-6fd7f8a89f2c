package com.bobandata.cloud.trade.ml.term.dal.mysql.contract;

import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_info_test(中长期交易：交易合同基本信息)】的数据库操作Mapper
 * @createDate 2024-03-12 15:57:26
 * @Entity com.bobandata.cloud.trade.ml.term.domain.MlTermContractInfo
 */
public interface MlTermContractInfoMapper extends BaseCrudMapper<MlTermContractInfoDo> {

    int declare(@Param("time") String time, @Param("energy") Double energy);

    @Select("select data_time dataTime,data_type dataType,value1,value2,value3 from jsc_hnxh where (data_type = '交易类型' or data_type = '交易品种') and data_time between #{startTime} and #{endTime}")
    List<Map> selectEnergy(ContractInfoReqVo contractInfoReqVo);

    @Select(" select data_time dataTime,value1,\n" +
            " round((SELECT sum(settle_fee)/sum(settle_energy) FROM `ml_settle_batch`\n" +
            " where left(data_time,4) = #{startYear} group by left(data_time,4) ),2) ljjjc\n" +
            " from jsc_hnxh \n" +
            " where data_type = '交易价格' and data_time between #{startTime} and #{endTime} ")
    List<Map> selectJc(ContractInfoReqVo contractInfoReqVo);
}




