package com.bobandata.cloud.trade.ml.term.service.offset;

import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetReqVo;
import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.offset.OffsetDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.offset.OffsetMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_batch(中长期交易：批发侧结算单)】的数据库操作Service实现
 * @createDate 2024-03-19 09:43:56
 */
@Service
public class OffsetServiceImpl extends BaseServiceImpl<OffsetMapper, OffsetDo> implements OffsetService {


    @Autowired
    private OffsetMapper offsetMapper;
    @Override
    public List<OffsetRespVo> listDlTotal(OffsetReqVo offsetReqVo) {
        List<OffsetRespVo> offsetRespVos = offsetMapper.listDlTotal(offsetReqVo);
        return offsetRespVos;
    }
}




