package com.bobandata.cloud.trade.ml.term.service.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonReleaseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.AbandonReleaseInfoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:02
 * @Classname AbandonReleaseInfoService
 * @Description
 */
public interface AbandonReleaseInfoService extends BaseService<AbandonReleaseInfoMapper, AbandonReleaseInfoDo> {

    PagingResult<AbandonReleaseInfoRespVo> getAbandonReleaseInfoPage(AbandonReleaseInfoReqVo reqVo);

}
