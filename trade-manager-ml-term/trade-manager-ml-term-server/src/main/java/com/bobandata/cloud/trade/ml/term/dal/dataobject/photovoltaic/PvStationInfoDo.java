package com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.AbstractDo;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/11/1 10:51
 * @Classname ChargeStationInfoDo
 * @Description 电站档案
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("pv_station_info")
public class PvStationInfoDo extends AbstractDo<Long> {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    @TableField("station_id")
    private String stationId;

    @TableField("station_name")
    private String stationName;

    @TableField("area_code")
    private String areaCode;

    @TableField("area_name")
    private String areaName;

    @TableField("jd")
    private BigDecimal jd;

    @TableField("wd")
    private BigDecimal wd;

    @TableField("user_id")
    private Long userId;

    @TableField("user_name")
    private String userName;

    @TableField("capatity")
    private BigDecimal capatity;

    @TableField("inv_sn")
    private String invSn;

    @TableField("inv_type")
    private String invType;
}
