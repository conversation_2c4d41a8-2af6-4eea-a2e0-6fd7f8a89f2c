package com.bobandata.cloud.trade.ml.term.service.photovoltaic;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvInverterDataDo;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:09
 * @Classname ChargeInverterDataService
 * @Description
 */
public interface PvInverterDataService extends IService<PvInverterDataDo> {

    List<KeyValueVo> getDayCurve(Date dataDate);

    void saveOrUpdateData(List<PvInverterDataDo> data);
}
