package com.bobandata.cloud.trade.ml.term.dal.mysql.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonReleaseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/28 11:59
 * @Classname AbandonReleaseInfoMapper
 * @Description
 */
public interface AbandonReleaseInfoMapper extends BaseCrudMapper<AbandonReleaseInfoDo> {

    default PagingResult<AbandonReleaseInfoDo> selectPage(AbandonReleaseInfoReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<AbandonReleaseInfoDo>()
            .eqIfPresent(AbandonReleaseInfoDo::getRunDate, reqVO.getRunDate())
            .orderByDesc(AbandonReleaseInfoDo::getRunDate));
    }
}
