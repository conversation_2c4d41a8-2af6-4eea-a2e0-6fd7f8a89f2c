package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.CorpConsRation;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.CorpConsRationMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class CorpConsRationServiceImpl extends BaseServiceImpl<CorpConsRationMapper, CorpConsRation> implements CorpConsRationService {

    @Autowired
    CorpConsRationMapper corpConsRationMapper;

    @Override
    public ServiceResult<Boolean> insert(CorpConsRation corpConsRation) {
        QueryWrapper<CorpConsRation> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time",corpConsRation.getDate());
        if(corpConsRationMapper.selectCount(wrapper)>0){
            return ServiceResult.error(4005,"已存在相同日期数据");
        }
        return ServiceResult.success(corpConsRationMapper.insert(corpConsRation)>0);
    }

    @Override
    public ServiceResult<Boolean> updateRation(CorpConsRation corpConsRation) {
        QueryWrapper<CorpConsRation> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time",corpConsRation.getDate());
        return ServiceResult.success(corpConsRationMapper.update(corpConsRation,wrapper)>0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(corpConsRationMapper.deleteById(id)>0);
    }

    @Override
    public ServiceResult<List<CorpConsRation>> listRation(String dateTime) {
        QueryWrapper<CorpConsRation> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", dateTime);
        List<CorpConsRation> corpConsRations = corpConsRationMapper.selectList(wrapper);
        for (CorpConsRation corpConsRation : corpConsRations){
            corpConsRation.setDataType("交易公告信息");
        }
        return ServiceResult.success(corpConsRations);
    }
}




