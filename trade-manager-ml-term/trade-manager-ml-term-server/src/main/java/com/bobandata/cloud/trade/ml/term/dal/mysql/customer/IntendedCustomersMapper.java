package com.bobandata.cloud.trade.ml.term.dal.mysql.customer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.ConsRespVo;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.IntendedCustomersReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.IntendedCustomers;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024-03-12 15:35:24
 * @Entity com.bobandata.cloud.trade.system.domain.MlTermContractEnergy
 */
public interface IntendedCustomersMapper extends BaseCrudMapper<IntendedCustomers> {

    default PagingResult<IntendedCustomers> selectPage(IntendedCustomersReqVo reqVO) {

        LambdaQueryWrapperX<IntendedCustomers> wrapperX = new LambdaQueryWrapperX<IntendedCustomers>()
                .likeIfPresent(IntendedCustomers::getConsName, reqVO.getConsName())
                .likeIfPresent(IntendedCustomers::getPersion, reqVO.getPersion())
                .eqIfPresent(IntendedCustomers::getVlotCode, reqVO.getLevel())
                .eqIfPresent(IntendedCustomers::getElecTypeBigcls, reqVO.getElecTypeBigcls());
        if(StringUtils.isNotEmpty(reqVO.getKey())){
            wrapperX.and((wrapper) -> {
                wrapper.like(IntendedCustomers::getConsNo, reqVO.getKey()).or().like(IntendedCustomers::getConsName, reqVO.getKey());
            });
        }
        wrapperX.orderByDesc(IntendedCustomers::getElecYear);
        return selectPage(reqVO.toPagination(), wrapperX);
    }

    List<Map> getConsName(@Param("key") String key);

}




