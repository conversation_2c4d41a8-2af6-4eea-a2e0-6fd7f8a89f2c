package com.bobandata.cloud.trade.ml.term.dal.dataobject.ration;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.sql.Timestamp;

/**
 *
 * @TableName
 */
@TableName(value = "trade_hnxh_price")
@Data
public class Hnprice extends BaseDo {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *时间
     */
    @TableField(value = "data_time")
    private String date;

    /**
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     */
    @TableField(value = "price")
    private String price;

    /**
     * 创建人
     */
    @TableField(exist = false)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Timestamp createTime;

    /**
     * 最后修改时间
     */
    @TableField(exist = false)
    private Timestamp lastRefreshTime;
    /**
     * 最后修改人
     */
    @TableField(exist = false)
    private Long lastModifierId;

}