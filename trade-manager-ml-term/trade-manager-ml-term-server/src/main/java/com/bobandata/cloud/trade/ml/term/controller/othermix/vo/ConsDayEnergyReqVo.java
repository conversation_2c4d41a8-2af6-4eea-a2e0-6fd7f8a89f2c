package com.bobandata.cloud.trade.ml.term.controller.othermix.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/10/29 15:38
 * @Classname ConsDayEnergyReqVo
 * @Description
 */
@Schema(description = "需求响应-用户需求响应评估")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsDayEnergyReqVo {

    @Schema(description = "用户户号")
    private String consNo;

    @Schema(description = "用户户名")
    private String consName;

    @Schema(description = "曲线日期")
    private String curveDate;

}
