package com.bobandata.cloud.trade.ml.term.controller.contract;

import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowReqVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractFlowDoMapper;
import com.bobandata.cloud.trade.ml.term.service.flow.MlTermContractFlowService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "中长期管理-台账管理")
@Slf4j
@RequestMapping("/flow")
@RestController
public class MlTermFlowController extends BaseCrudRestController<MlTermContractFlowDo, MlTermContractFlowDoMapper> {

    @Autowired
    private MlTermContractFlowService contractFlowService;

    @Operation(summary = "获得合同台账分页列表")
    @GetMapping("/list")
    public PagingResult<ContractFlowRespVo> listCorpCons(@Valid ContractFlowReqVo reqVo) {
        return contractFlowService.pageContractFlow(reqVo);
    }
}
