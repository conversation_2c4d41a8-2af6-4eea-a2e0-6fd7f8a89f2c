package com.bobandata.cloud.trade.ml.term.convert;

import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FlowConvert {

    FlowConvert INSTANCE = Mappers.getMapper(FlowConvert.class);

    PagingResult<ContractFlowRespVo> convertPage(PagingResult<MlTermContractFlowDo> page);
}
