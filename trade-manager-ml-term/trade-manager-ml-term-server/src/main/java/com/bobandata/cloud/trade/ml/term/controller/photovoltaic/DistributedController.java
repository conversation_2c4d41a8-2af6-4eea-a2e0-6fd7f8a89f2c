package com.bobandata.cloud.trade.ml.term.controller.photovoltaic;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.common.utils.array.CollectionUtils;
import com.bobandata.cloud.trade.curve.core.enums.NinetySixPoint;
import com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo.PowerConsumptionVo;
import com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo.PvStatDataVo;
import com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo.StationInfoVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvInverterDataDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvStationInfoDo;
import com.bobandata.cloud.trade.ml.term.service.photovoltaic.PvInverterDataService;
import com.bobandata.cloud.trade.ml.term.service.photovoltaic.PvStationInfoService;
import com.bobandata.cloud.web.util.ApplicationContextHolder;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/11/1 9:27
 * @Classname DistributedController
 * @Description
 */
@Tag(name = "分布式光伏接口")
@RequestMapping("/photovoltaic")
@RestController
@Slf4j
@Validated
public class DistributedController {

    @Autowired
    private PvStationInfoService chargeStationInfoService;

    @Autowired
    private PvInverterDataService chargeInverterDataService;

    @Operation(summary = "光伏统计接口")
    @ResponseBody
    @GetMapping("/stat")
    public ServiceResult<PvStatDataVo> pvStatData(Date startDate, Date endDate) {
        PvStatDataVo pvStatDataVo = chargeStationInfoService.statPvData(startDate, endDate);
        return ServiceResult.success(pvStatDataVo);
    }

    @Operation(summary = "光伏日曲线接口")
    @ResponseBody
    @GetMapping("/curve")
    public ServiceResult<List<KeyValueVo>> pvStatData(Date date) {
        List<KeyValueVo> dayCurve = chargeInverterDataService.getDayCurve(date);
        return ServiceResult.success(dayCurve);
    }

    @Operation(summary = "接入电站档案接口")
    @ResponseBody
    @PostMapping("/updStationInfo")
    public ServiceResult<String> updStationInfo(@RequestBody @Valid List<StationInfoVo> voList) {
        if (voList.isEmpty()) {
            return ServiceResult.error(1, "参数为空");
        }
        try {
            ObjectMapper objectMapper = ApplicationContextHolder.getBean(ObjectMapper.class);
            String json = objectMapper.writeValueAsString(voList);
            log.info("电站档案接口入参json:{}", json);
            List<PvStationInfoDo> doList = CollectionUtils.convertList(voList, PvStationInfoDo.class);
            chargeStationInfoService.insertOrUpdBatch(doList);
            return ServiceResult.success();
        } catch (Exception e) {
            log.error("电站档案接口调用异常", e);
        }
        return ServiceResult.error(1, "电站档案接口调用异常");
    }

    @Operation(summary = "接入逆变器电量功率接口")
    @ResponseBody
    @PostMapping("/inverterPowerConsumption")
    public ServiceResult<Object> inverterPowerConsumption(@RequestBody @Valid List<PowerConsumptionVo> requestData) {
        if (requestData.isEmpty()) {
            return ServiceResult.error(1, "参数为空");
        }
        try {
            ObjectMapper objectMapper = ApplicationContextHolder.getBean(ObjectMapper.class);
            String json = objectMapper.writeValueAsString(requestData);
            log.info("接入逆变器电量功率接口入参json:{}", json);
            List<PvInverterDataDo> dataToSave = convertToPvInverterDataDo(requestData);
            chargeInverterDataService.saveOrUpdateData(dataToSave);
            return ServiceResult.success();
        } catch (Exception e) {
            log.error("接入逆变器电量功率接口调用异常", e);
        }
        return ServiceResult.success(1, "调用成功");
    }

    private List<PvInverterDataDo> convertToPvInverterDataDo(List<PowerConsumptionVo> requestData) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("HH:mm");
        return requestData.stream().flatMap(request -> {
            LocalDateTime dateTime = LocalDateTime.parse(request.getDataTime(), formatter);
            // 会自动把2024-12-02 24:00:00格格式化成2024-12-03 00:00:00
            //String time = dateTime.format(formatter1);
            String time = request.getDataTime().substring(11, 16);
            NinetySixPoint ninetySixPoint = NinetySixPoint.getEnumByTime(time);
            int index = ninetySixPoint.getPoint() + 1;
            if (index == 97) {
                return null;
            }
            PvInverterDataDo dayPowerData = new PvInverterDataDo();
            dayPowerData.setInvId(request.getInvId());
            dayPowerData.setCurveDate(Date.valueOf(dateTime.toLocalDate()));
            dayPowerData.setCurveType(1);
            setPValue(dayPowerData, index, request.getDayPower());

            PvInverterDataDo totalAcpData = new PvInverterDataDo();
            totalAcpData.setInvId(request.getInvId());
            totalAcpData.setCurveDate(Date.valueOf(dateTime.toLocalDate()));
            totalAcpData.setCurveType(2);
            setPValue(totalAcpData, index, request.getTotalAcp());

            return Stream.of(dayPowerData, totalAcpData);
        }).collect(Collectors.toList());
    }

    private void setPValue(PvInverterDataDo data, int index, BigDecimal value) {
        try {
            Field field = PvInverterDataDo.class.getDeclaredField("p" + index);
            field.setAccessible(true);
            field.set(data, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error(e.getMessage(), e);
        }
    }

}
