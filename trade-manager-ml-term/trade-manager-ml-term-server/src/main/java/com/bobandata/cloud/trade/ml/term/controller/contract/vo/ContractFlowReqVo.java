package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import java.sql.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "中长期管理-台账管理 分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ContractFlowReqVo extends PageParam {

    @Schema(description = "交易序列", example = "PHZHNxxxxA0001")
    protected String tradeSegId;

    @Schema(description = "售方用户", example = "xxx公司")
    protected String saleUnitsName;

    @Schema(description = "购方用户", example = "xxx公司")
    protected String vendeeUnitsName;

    @Schema(description = "合同开始时间", example = "日期格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    protected Date startTime;

    @Schema(description = "合同结束时间", example = "日期格式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    protected Date endTime;

    @Schema(description = "交易时间")
    protected String contractCreateDate;

}
