package com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * <p>
 * 响应评估表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("m_response_evaluation")
public class ResponseEvaluationDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     * 市公司名称，如国网岳阳供电公司
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 用户 ID
     */
    @TableField("cons_id")
    private String consId;

    /**
     * 用户名称，如湖南正虹科技发展股份有限公司营田经营分公司
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 县公司名称，如国网岳阳市屈原供电公司
     */
    @TableField("county_name")
    private String countyName;

    /**
     * 调度日期，如 2024-05-30
     */
    @TableField("dispatch_date")
    private Date dispatchDate;

    /**
     * 用电类型描述，如大工业用电
     */
    @TableField("elec_type_desc")
    private String elecTypeDesc;

    /**
     * 结束时间段，如 20:00
     */
    @TableField("end_period")
    private String endPeriod;

    /**
     * 反馈响应值
     */
    @TableField("feedback_response")
    private BigDecimal feedbackResponse;

    /**
     * 行业类型描述，如饲料加工
     */
    @TableField("indust_type_desc")
    private String industTypeDesc;

    /**
     * 是否有效（是/否）
     */
    @TableField("is_valid")
    private String isValid;

    /**
     * 组织名称，如国网岳阳市屈原供电公司
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 组织编号
     */
    @TableField("org_no")
    private String orgNo;

    /**
     * 时段平均基线值
     */
    @TableField("period_avg_line")
    private BigDecimal periodAvgLine;

    /**
     * 时段最大基线值
     */
    @TableField("period_max_line")
    private BigDecimal periodMaxLine;

    /**
     * 时段实际平均值
     */
    @TableField("period_real_avg")
    private BigDecimal periodRealAvg;

    /**
     * 时段实际最大值
     */
    @TableField("period_real_max")
    private BigDecimal periodRealMax;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 比例（可为 null）
     */
    private BigDecimal proportion;

    /**
     * 实际响应值
     */
    @TableField("real_response")
    private BigDecimal realResponse;

    /**
     * 另一个实际响应值
     */
    @TableField("real_response1")
    private BigDecimal realResponse1;

    /**
     * 运行容量，如 2100
     */
    @TableField("run_capatity")
    private String runCapatity;

    /**
     * 开始时间段，如 18:00
     */
    @TableField("start_period")
    private String startPeriod;

    /**
     * 补贴金额
     */
    private BigDecimal subsidy;

    /**
     * 补贴 JSC（可为 null）
     */
    @TableField("subsidy_jsc")
    private BigDecimal subsidyJsc;

    /**
     * 补贴 SJ（可为 null）
     */
    @TableField("subsidy_sj")
    private BigDecimal subsidySj;

    /**
     * 所属组织名称（可为空字符串）
     */
    @TableField("suo_org_name")
    private String suoOrgName;

    /**
     * 用户 ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 用户名称（可为 null）
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户类型，如直接参与用户
     */
    @TableField("user_type")
    private String userType;

    /**
     * 电压名称，如交流 10kV
     */
    @TableField("volt_name")
    private String voltName;


}
