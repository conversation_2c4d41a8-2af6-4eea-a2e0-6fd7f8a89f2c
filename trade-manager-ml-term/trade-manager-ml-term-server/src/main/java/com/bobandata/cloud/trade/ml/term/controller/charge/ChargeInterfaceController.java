package com.bobandata.cloud.trade.ml.term.controller.charge;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStatRespVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargePilesDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeStationsDo;
import com.bobandata.cloud.trade.ml.term.service.charge.ChargeInterfaceService;
import com.bobandata.cloud.trade.ml.term.service.charge.ChargeRecordsStatService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
/**
 * <AUTHOR>
 * @Date 2024/11/4 9:58
 * @Classname ChargeInterfaceController
 * @Description
 */
@Slf4j
@Tag(name = "充电桩接口")
@RequestMapping(value = "/charge")
@RestController
@Validated
public class ChargeInterfaceController {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ChargeInterfaceService chargeInterfaceService;

    @Autowired
    private ChargeRecordsStatService recordsStatService;

    @Operation(summary = "充电站数据变更")
    @PostMapping("/updStationsList")
    @ResponseBody
    public ServiceResult updStationsList(@RequestBody @Valid ChargeStationReqVo reqVo) {
        try {
            //获取充电站数据
            List<Object> objectList = chargeInterfaceService.getChargeDataList(reqVo, 1);
            if (CollUtil.isEmpty(objectList)) {
                return ServiceResult.error(1, "查询充电站列表为空");
            }
            String jsonString = JSONObject.toJSONString(objectList);
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ChargeStationsDo.class);
            List<ChargeStationsDo> chargeStationsDoList = objectMapper.readValue(objectMapper.readTree(jsonString).toString(), javaType);
            if (CollUtil.isEmpty(chargeStationsDoList)) {
                return ServiceResult.error(1, "查询充电站列表为空");
            }
            // 更新、新增
            chargeInterfaceService.saveOrUpdateStationsData(chargeStationsDoList);
        } catch (JsonProcessingException e) {
            log.error("格式转换失败", e);
            return ServiceResult.error(1, "充电站数据格式转换失败");
        } catch (Exception e) {
            log.error("充电站数据变更失败", e);
            return ServiceResult.error(1, "充电站数据变更失败");
        }
        return ServiceResult.success();
    }

    @Operation(summary = "充电桩数据变更")
    @PostMapping("/updPliesList")
    @ResponseBody
    public ServiceResult updPliesList(@RequestBody @Valid ChargeStationReqVo reqVo) {
        try {
            //获取充电桩数据
            List<Object> objectList = chargeInterfaceService.getChargeDataList(reqVo, 2);
            if (CollUtil.isEmpty(objectList)) {
                return ServiceResult.error(1, "查询充电桩列表为空");
            }
            String jsonString = JSONObject.toJSONString(objectList);
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ChargePilesDo.class);
            List<ChargePilesDo> chargePilesDoList = objectMapper.readValue(objectMapper.readTree(jsonString).toString(), javaType);
            if (CollUtil.isEmpty(chargePilesDoList)) {
                return ServiceResult.error(1, "充电桩列表为空");
            }
            // 更新、新增
            chargeInterfaceService.saveOrUpdatePilesData(chargePilesDoList);
        } catch (JsonProcessingException e) {
            log.error("格式转换失败", e);
            return ServiceResult.error(1, "充电桩数据格式转换失败");
        } catch (Exception e) {
            log.error("充电桩数据变更失败", e);
            return ServiceResult.error(1, "充电桩数据变更失败");
        }
        return ServiceResult.success();
    }

    @Operation(summary = "充电订单变更")
    @PostMapping("/updBillRecordList")
    @ResponseBody
    public ServiceResult updBillRecordList(@RequestBody @Valid ChargeStationReqVo reqVo) {
        try {
            List<Long> stationIds = chargeInterfaceService.getStationIds();
            if (CollUtil.isEmpty(stationIds)) {
                return ServiceResult.error(1, "充电站点为空");
            }
            reqVo.setStationIds(stationIds);
            log.info("请求参数：{}", JSONObject.toJSONString(reqVo));
            //获取充电站数据
            List<Object> objectList = chargeInterfaceService.getChargeDataList(reqVo, 3);
            if (CollUtil.isEmpty(objectList)) {
                return ServiceResult.error(1, "查询充电订单列表为空");
            }
            String jsonString = JSONObject.toJSONString(objectList);
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ChargeRecordsDo.class);
            List<ChargeRecordsDo> chargeRecordsDoList = objectMapper.readValue(objectMapper.readTree(jsonString).toString(), javaType);
            if (CollUtil.isEmpty(chargeRecordsDoList)) {
                return ServiceResult.error(1, "查询充电订单列表为空");
            }
            // 更新、新增
            log.info("数据总数：{}", chargeRecordsDoList.size());
            chargeInterfaceService.saveOrUpdateBillsData(chargeRecordsDoList);
            recordsStatService.dataEtl(chargeRecordsDoList);
        } catch (JsonProcessingException e) {
            log.error("格式转换失败", e);
            return ServiceResult.error(1, "充电订单数据格式转换失败");
        } catch (Exception e) {
            log.error("充电订单数据变更失败", e);
            return ServiceResult.error(1, "充电订单数据变更失败");
        }
        return ServiceResult.success();
    }

    @PostMapping("/updBillRecordList1")
    @ResponseBody
    public ServiceResult updBillRecordList1(@RequestBody @Valid ChargeStationReqVo reqVo) {
        try {
            List<Long> stationIds = chargeInterfaceService.getStationIds();
            if (CollUtil.isEmpty(stationIds)) {
                return ServiceResult.error(1, "充电站点为空");
            }
            reqVo.setStationIds(stationIds);
            log.info("请求参数：{}", JSONObject.toJSONString(reqVo));
            // 异步获取充电站数据
            CompletableFuture<List<Object>> future = CompletableFuture.supplyAsync(() -> {
                return chargeInterfaceService.getChargeDataList(reqVo, 3);
            });
            // 链式处理异步任务
            CompletableFuture<ServiceResult> resultFuture = future.thenCompose(objectList -> {
                if (CollUtil.isEmpty(objectList)) {
                    return CompletableFuture.completedFuture(ServiceResult.error(1, "查询充电订单列表为空"));
                }
                String jsonString = JSONObject.toJSONString(objectList);
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, ChargeRecordsDo.class);
                List<ChargeRecordsDo> chargeRecordsDoList;
                try {
                    chargeRecordsDoList = objectMapper.readValue(objectMapper.readTree(jsonString).toString(), javaType);
                } catch (JsonProcessingException e) {
                    log.error("格式转换失败", e);
                    return CompletableFuture.completedFuture(ServiceResult.error(1, "充电订单数据格式转换失败"));
                }
                if (CollUtil.isEmpty(chargeRecordsDoList)) {
                    return CompletableFuture.completedFuture(ServiceResult.error(1, "查询充电订单列表为空"));
                }
                log.info("查询到数据总条数：{}", chargeRecordsDoList.size());
                // 异步处理数据的保存和更新
                CompletableFuture.runAsync(() -> {
                    chargeInterfaceService.saveOrUpdateBillsData(chargeRecordsDoList);
                    recordsStatService.dataEtl(chargeRecordsDoList);
                });

                return CompletableFuture.completedFuture(ServiceResult.success());
            });

            // 等待最终结果
            return resultFuture.get();

        } catch (InterruptedException | ExecutionException e) {
            log.error("充电订单数据变更失败", e);
            return ServiceResult.error(1, "充电订单数据变更失败");
        }
    }


    @Operation(summary = "充电站数据统计查询")
    @GetMapping("/count")
    @ResponseBody
    public ServiceResult<ChargeStatRespVo> statChargeRecords(Date startDate, Date endDate) {
        ChargeStatRespVo chargeStatRespVo = recordsStatService.chargeStat(startDate, endDate);
        return ServiceResult.success(chargeStatRespVo);
    }

    @Operation(summary = "充电站曲线数据查询")
    @GetMapping("/curve")
    @ResponseBody
    public ServiceResult<List<KeyValueVo>> statChargeRecords(Date dataDate) {
        List<KeyValueVo> stationCurve = recordsStatService.getStationCurve(dataDate);
        return ServiceResult.success(stationCurve);
    }
}
