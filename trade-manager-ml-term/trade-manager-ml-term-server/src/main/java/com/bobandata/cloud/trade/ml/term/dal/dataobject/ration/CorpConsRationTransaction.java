package com.bobandata.cloud.trade.ml.term.dal.dataobject.ration;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.sql.Timestamp;

/**
 *
 * @TableName
 */
@TableName(value = "corp_cons_ratio_transaction")
@Data
public class CorpConsRationTransaction extends BaseDo {

    @TableField(exist = false)
    private Long id;
    /**
     *时间
     */
    @TableField(value = "data_time")
    private String date;

    /**
     */
    @TableField(value = "ratio1")
    private String ratio1;

    /**
     */
    @TableField(value = "ratio2")
    private String ratio2;

    /**
     */
    @TableField(value = "ratio3")
    private String ratio3;

    /**
     */
    @TableField(value = "ratio4")
    private String ratio4;

    /**
     */
    @TableField(value = "type")
    private Integer type;


    /**
     */
    @TableField(exist = false)
    private String dataType;

    /**
     * 创建人
     */
    @TableField(exist = false)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Timestamp createTime;

    /**
     * 最后修改时间
     */
    @TableField(exist = false)
    private Timestamp lastRefreshTime;
    /**
     * 最后修改人
     */
    @TableField(exist = false)
    private Long lastModifierId;

}