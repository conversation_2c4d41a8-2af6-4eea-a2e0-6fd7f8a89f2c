package com.bobandata.cloud.trade.ml.term.dal.dataobject.customer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 中长期交易：交易合同流水信息
 *
 * @TableName ml_term_contract_flow
 */
@TableName(value = "corp_cons_hn_yxke")
@Data
public class IntendedCustomers extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     *用户户号
     */
    @TableField(value = "cons_no")
    private String consNo;

    /**
     * 户名
     */
    @TableField(value = "cons_name")
    private String consName;


    /**
     *区域
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     *电压等级
     */
    @TableField(value = "VOLT_CODE")
    private String vlotCode;

    /**
     *用电类别
     */
    @TableField(value = "elec_type_bigcls")
    private String elecTypeBigcls;

    /**
     *用电行业
     */
    @TableField(value = "indust_type_dsc")
    private String industTypeDsc;

    /**
     *地市
     */
    @TableField(value = "city")
    private String city;

    /**
     *区县
     */
    @TableField(value = "county")
    private String county;

    /**
     *用电区域
     */
    @TableField(value = "ELEC_ADDR")
    private String elecAddr;

    /**
     *合同容量
     */
    @TableField(value = "CONTRACT_CAP")
    private BigDecimal contractCap;

    /**
     *运行容量
     */
    @TableField(value = "RUN_CAP")
    private BigDecimal runCap;

    /**
     *高耗能行业类别
     */
    @TableField(value = "HEC_INDUSTRY_CODE")
    private String hecIndustryCode;

    /**
     *负荷性质
     */
    @TableField(value = "LODE_ATTR_CODE")
    private String loadAttrCode;

    /**
     *联系人姓名
     */
    @TableField(value = "persion")
    private String persion;

    /**
     *联系方式
     */
    @TableField(value = "phone")
    private String phone;

    /**
     *交易平台账号
     */
    @TableField(value = "account")
    private String account;

    /**
     *交易平台密码
     */
    @TableField(value = "pwd")
    private String pwd;

    /**
     *代理开始时间
     */
    @TableField(value = "start_time")
    private String startTime;

    /**
     *代理结束时间
     */
    @TableField(value = "end_time")
    private String endTime;

    /**
     *合同分成比例
     */
    @TableField(value = "proportion")
    private Integer proportion;

    /**
     *刷新时间
     */
    @TableField(value = "last_refresh_time")
    private Timestamp lastRefreshTime;

    /**
     *最后修改人
     */
    @TableField(value = "last_modifier_id")
    private Long lastModifierId;

    /**
     *签约售电公司
     */
    @TableField(value = "sign_sell_elec_comp")
    private String signSellElecComp;

    /**
     *年用电量
     */
    @TableField(value = "elec_year")
    private BigDecimal elecYear;

    /**
     *上个签约售电公司到期时间
     */
    @TableField(value = "comp_expiration_date")
    private Date compExpirationDate;


    /**
     * 创建人
     */
    @TableField(exist = false)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Timestamp createTime;


}