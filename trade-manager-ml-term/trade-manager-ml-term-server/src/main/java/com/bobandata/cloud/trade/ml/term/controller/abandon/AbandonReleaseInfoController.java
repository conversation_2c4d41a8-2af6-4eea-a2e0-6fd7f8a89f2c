package com.bobandata.cloud.trade.ml.term.controller.abandon;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonInnerResponseDo;
import com.bobandata.cloud.trade.ml.term.service.abandon.AbandonInnerResponseService;
import com.bobandata.cloud.trade.ml.term.service.abandon.AbandonReleaseInfoService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/10/28 13:55
 * @Classname AbandonReleaseInfoController
 * @Description
 */
@Tag(name = "需求响应 - 市场信息")
@RequestMapping(value = "/abandon")
@RestController
public class AbandonReleaseInfoController {

    @Autowired
    private AbandonReleaseInfoService abandonReleaseInfoService;

    @Autowired
    private AbandonInnerResponseService abandonInnerResponseService;

    @Operation(summary = "需求响应-市场发布信息")
    @GetMapping("/getAbandonReleaseInfoPage")
    @ResponseBody
    public PagingResult<AbandonReleaseInfoRespVo> getAbandonReleaseInfoPage(AbandonReleaseInfoReqVo reqVo) {
        return abandonReleaseInfoService.getAbandonReleaseInfoPage(reqVo);
    }

    @Operation(summary = "需求响应-内部响应邀约查询")
    @GetMapping("/getAbandonInnerResponsePage")
    @ResponseBody
    public PagingResult<AbandonInnerResponseDo> getAbandonInnerResponsePage(AbandonReleaseInfoReqVo reqVo) {
        return abandonInnerResponseService.getAbandonInnerResponsePage(reqVo);
    }

    @Operation(summary = "需求响应-反馈操作")
    @PostMapping("/updFeedBackStatus")
    @ResponseBody
    public ServiceResult updFeedBackStatus(@RequestParam("id") Long id) {
        return abandonInnerResponseService.updFeedBackStatus(id);
    }
}
