package com.bobandata.cloud.trade.ml.term.controller.calendar;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.calendar.TransactionCalendar;
import com.bobandata.cloud.trade.ml.term.dal.mysql.calendar.TransactionCalendarMapper;
import com.bobandata.cloud.trade.ml.term.service.calendar.TransactionCalendarService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "交易日历")
@Slf4j
@RequestMapping("/calendar")
@RestController
public class TransactionCalendarController extends BaseCrudRestController<TransactionCalendar, TransactionCalendarMapper> {

    @Autowired
    private TransactionCalendarService transactionCalendarService;

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertCalendar(@RequestBody TransactionCalendar transactionCalendar) {
        return transactionCalendarService.insert(transactionCalendar);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public ServiceResult<Boolean> updateCalendar(@RequestBody TransactionCalendar transactionCalendar) {
        return transactionCalendarService.updateCalendar(transactionCalendar);
    }

    @Operation(summary = "删除")
    @GetMapping("/delete")
    public ServiceResult<Boolean> deleteCalendar(@RequestParam("id") Long id) {
        return transactionCalendarService.delete(id);
    }
    @Operation(summary = "list")
    @GetMapping("/list")
    public ServiceResult<Map<String,List<TransactionCalendar>>> listCalendar(@Valid String dateTime) {
        return ServiceResult.success(transactionCalendarService.listCalendar(dateTime));
    }


}
