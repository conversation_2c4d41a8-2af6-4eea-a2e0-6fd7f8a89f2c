package com.bobandata.cloud.trade.ml.term.dal.dataobject.calendar;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 中长期交易：交易合同流水信息
 *
 * @TableName ml_term_contract_flow
 */
@TableName(value = "trade_calendar")
@Data
public class TransactionCalendar extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     *时间
     */
    @TableField(value = "data_time")
    private String date;

    /**
     * 类型 1 上午 2 下午
     */
    @TableField(value = "type")
    private Integer type;

    /**
     *交易内容
     */
    @TableField(value = "trade_info")
    private String tradeType;


    /**
     * 创建人
     */
    @TableField(exist = false)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Timestamp createTime;

    /**
     * 最后修改时间
     */
    @TableField(exist = false)
    private Timestamp lastRefreshTime;
    /**
     * 最后修改人
     */
    @TableField(exist = false)
    private Long lastModifierId;

}