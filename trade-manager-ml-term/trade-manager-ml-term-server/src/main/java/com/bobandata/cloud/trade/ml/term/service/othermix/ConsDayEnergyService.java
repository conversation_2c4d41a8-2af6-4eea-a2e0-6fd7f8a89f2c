package com.bobandata.cloud.trade.ml.term.service.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ConsDayEnergyReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ConsDayEnergyDo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/29 15:42
 * @Classname ConsDayEnergyService
 * @Description
 */
public interface ConsDayEnergyService {

    List<ConsDayEnergyDo> selectList(ConsDayEnergyReqVo reqVO);
}
