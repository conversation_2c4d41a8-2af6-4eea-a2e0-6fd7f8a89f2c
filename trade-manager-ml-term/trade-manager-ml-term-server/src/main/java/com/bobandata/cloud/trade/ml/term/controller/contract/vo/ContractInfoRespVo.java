package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.YearMonth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-19日 14:03
 * @description
 */
@Schema(description = "中长期管理-合同持仓管理 合同概况")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ContractInfoRespVo {

    @Schema(description = "月份")
    private YearMonth dateMonth;

    @Schema(description = "合同ID 不显示")
    private String tradeId;

    @Schema(description = "交易序列id")
    private String arrId;

    @Schema(description = "交易序列名称")
    private String arrName;

    @Schema(description = "卖方公司名称")
    private String sellerUnitName;

    @Schema(description = "买方公司名称")
    private String purchaseUnitName;

    @Schema(description = "合同电量")
    private BigDecimal contractEnergy;

    @Schema(description = "合同均价")
    private BigDecimal contractPrice;

}
