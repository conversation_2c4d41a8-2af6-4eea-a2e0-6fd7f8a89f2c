package com.bobandata.cloud.trade.ml.term.controller.offset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-03-22日 16:43
 * @description
 */
@Tag(name = "售电侧管理-用电偏差管理 售电公司偏差")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class OffsetRespVo {

    @Schema(description = "日期")
    private String dataTime;

    @Schema(description = "尖峰电量")
    private String highestEnergy;

    @Schema(description = "高峰电量")
    private String peakEnergy;

    @Schema(description = "平段电量")
    private String flatEnergy;

    @Schema(description = "谷段电量")
    private String valleyEnergy;

    @Schema(description = "总电量")
    private String totalEnergy;
}
