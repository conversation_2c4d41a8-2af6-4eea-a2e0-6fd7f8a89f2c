package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnprice;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.HnpriceMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class HnpriceServiceImpl extends BaseServiceImpl<HnpriceMapper, Hnprice> implements HnPriceService {

    @Autowired
    HnpriceMapper hnpriceMapper;

    @Override
    public ServiceResult<Boolean> insert(Hnprice hnprice) {
        QueryWrapper<Hnprice> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", hnprice.getDate());
        wrapper.eq("data_type", hnprice.getDataType());
        if (hnpriceMapper.selectCount(wrapper) > 0) {
            return ServiceResult.error(4005, "已存在相同日期数据");
        }
        return ServiceResult.success(hnpriceMapper.insert(hnprice) > 0);
    }

    @Override
    public ServiceResult<Boolean> updateRation(Hnprice hnprice) {
        QueryWrapper<Hnprice> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", hnprice.getDate());
        wrapper.eq("data_type", hnprice.getDataType());
        return ServiceResult.success(hnpriceMapper.update(hnprice, wrapper) > 0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(hnpriceMapper.deleteById(id) > 0);
    }

    @Override
    public ServiceResult<List<Hnprice>> listRation(String dateTime) {
        QueryWrapper<Hnprice> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", dateTime + "-01 00:00:00");
        List<Hnprice> hnprices = hnpriceMapper.selectList(wrapper);
        for (Hnprice hnprice : hnprices) {
            hnprice.setDate(hnprice.getDate().substring(0, 7));
        }
        return ServiceResult.success(hnprices);
    }
}




