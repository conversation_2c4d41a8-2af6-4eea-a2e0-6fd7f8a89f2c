package com.bobandata.cloud.trade.ml.term.controller.ration;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnprice;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.HnpriceMapper;
import com.bobandata.cloud.trade.ml.term.service.ration.HnPriceService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "录入值")
@Slf4j
@RequestMapping("/price")
@RestController
public class HnpriceController extends BaseCrudRestController<Hnprice, HnpriceMapper> {

    @Autowired
    private HnPriceService hnpriceService;

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertPrice(@RequestBody Hnprice hnprice) {
        return hnpriceService.insert(hnprice);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public ServiceResult<Boolean> updatePrice(@RequestBody Hnprice hnprice) {
        return hnpriceService.updateRation(hnprice);
    }

    @Operation(summary = "list")
    @GetMapping("/list")
    public ServiceResult<List<Hnprice>> listPrice(@Valid String dateTime) {
        return hnpriceService.listRation(dateTime);
    }


}
