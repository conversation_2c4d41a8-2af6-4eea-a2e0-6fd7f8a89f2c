package com.bobandata.cloud.trade.ml.term.dal.dataobject.customer;

import com.baomidou.mybatisplus.annotation.*;
import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 中长期交易：交易合同流水信息
 *
 * @TableName ml_term_contract_flow
 */
@TableName(value = "power_plant_info")
@Data
public class PowerPlant extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     *电厂名称
     */
    @TableField(value = "power_plant_name")
    private String powerPlantName;

    /**
     * 电厂类型
     */
    @TableField(value = "power_plant_type")
    private String powerPlantType;

    /**
     * 发电集团
     */
    @TableField(value = "power_generation_group")
    private String powerGenerationGroup;

    /**
     * 所属发电公司
     */
    @TableField(value = "power_company")
    private String powerCompany;

    /**
     * 最高电压等级
     */
    @TableField(value = "highest_voltage_level")
    private String highestVoltageLevel;

    /**
     * 投运日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    @TableField(value = "operation_date")
    private Timestamp operationDate;

    /**
     * 场站所在县
     */
    @TableField(value = "country")
    private String country;

    /**
     *投运容量
     */
    @TableField(value = "operation_cap")
    private Double operationCap;

    /**
     *刷新时间
     */
    @TableField(exist = false)
    private Timestamp lastRefreshTime;

    /**
     * 创建人
     */
    @TableField(exist = false)
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Timestamp createTime;

    /**
     * 最后修改人
     */
    @TableField(exist = false)
    private Long lastModifierId;

}