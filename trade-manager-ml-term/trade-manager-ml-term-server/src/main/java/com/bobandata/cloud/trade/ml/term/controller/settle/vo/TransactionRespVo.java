package com.bobandata.cloud.trade.ml.term.controller.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:02
 * @description
 */
@Schema(description = "交易情况表格")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TransactionRespVo {

    @Schema(description = "日期")
    private String dateTime;


    @Schema(description = "交易类型")
    private String displayName;

    @Schema(description = "电厂类型")
    private String powerPlantType;

    @Schema(description = "电厂名称")
    private String saleUnitsName;

    @Schema(description = "所属集团")
    private String saleParticipantName;

    @Schema(description = "电量-尖")
    private Long jianEnergy;

    @Schema(description = "电量-峰")
    private Long fengEnergy;

    @Schema(description = "电量-平")
    private Long pingEnergy;

    @Schema(description = "电量-谷")
    private Long guEnergy;

    @Schema(description = "电量-总")
    private Long totalEnergy;

    @Schema(description = "电价-尖")
    private Double jianPrice;

    @Schema(description = "电价-峰")
    private Double fengPrice;

    @Schema(description = "电价-平")
    private Double pingPrice;

    @Schema(description = "电价-谷")
    private Double guPrice;

    @Schema(description = "电费-尖")
    private Long jianEnergyPrice;

    @Schema(description = "电费-峰")
    private Long fengEnergyPrice;

    @Schema(description = "电费-平")
    private Long pingEnergyPrice;

    @Schema(description = "电费-谷")
    private Long guEnergyPrice;

    @Schema(description = "电费-总")
    private Long totalEnergyPrice;

    @Schema(description = "绿色电量环境价")
    private Double greenRightPrice;



}
