package com.bobandata.cloud.trade.ml.term.dal.mysql.settle;

import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_total_info(中长期交易：结算汇总单)】的数据库操作Mapper
 * @createDate 2024-03-19 09:43:55
 * @Entity com.bobandata.cloud.trade.ml.term.domain.MlSettleTotalInfo
 */
public interface MlSettleTotalInfoMapper extends BaseCrudMapper<MlSettleTotalInfoDo> {

    List<MlSettleTotalInfoDo> listByMonth(@Param("date") String date);

    List<TransactionRespVo> listJyTotal(@Param("month") String month,@Param("displayName") String displayName,@Param("plantType") String plantType);

    List<TransactionsBiasRespVo> listJyPcTotal(@Param("year") String year);

    @Select("select display_name displayName from trade_result_zcq_display")
    List<Map> listJyType();
}




