package com.bobandata.cloud.trade.ml.term.controller.ration;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnjsc;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.HnjscMapper;
import com.bobandata.cloud.trade.ml.term.service.ration.HnjscService;
import com.bobandata.cloud.trade.mvc.core.controller.BaseCrudRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-18日 10:07
 * @description
 */
@Tag(name = "录入值")
@Slf4j
@RequestMapping("/jsc")
@RestController
public class HnjscController extends BaseCrudRestController<Hnjsc, HnjscMapper> {

    @Autowired
    private HnjscService hnjscService;

    @Operation(summary = "新增")
    @PostMapping("/insert")
    public ServiceResult<Boolean> insertJsc(@RequestBody Hnjsc hnjsc) {
        return hnjscService.insert(hnjsc);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public ServiceResult<Boolean> updateJsc(@RequestBody Hnjsc hnjsc) {
        return hnjscService.updateRation(hnjsc);
    }

    @Operation(summary = "list")
    @GetMapping("/list")
    public ServiceResult<List<Hnjsc>> listJsc(@Valid String dateTime) {
        return hnjscService.listRation(dateTime);
    }


}
