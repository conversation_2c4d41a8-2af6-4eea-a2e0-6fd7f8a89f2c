package com.bobandata.cloud.trade.ml.term.service.settle;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleRetailDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleRetailMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_retail(中长期交易：零售侧结算单)】的数据库操作Service
 * @createDate 2024-03-19 09:43:56
 */
public interface MlSettleRetailService extends BaseService<MlSettleRetailMapper, MlSettleRetailDo> {

    List<MlSettleRetailDo> listByMonth(String month);
}
