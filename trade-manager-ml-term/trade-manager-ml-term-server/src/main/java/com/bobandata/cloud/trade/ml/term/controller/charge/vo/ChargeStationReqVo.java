package com.bobandata.cloud.trade.ml.term.controller.charge.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/4 10:00
 * @Classname ChargeStationsReqVo
 * @Description
 */
@Schema(description = "充电站reqvo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class ChargeStationReqVo {

    //@NotNull
    private Long sellerId;

    @NotNull(message = "当前页码不能为空")
    private Integer current;

    private Integer pageSize;

    private Integer total;

   // @NotEmpty(message = "区域不能为空")
    private List<Long> areaIds;

    //@NotBlank(message = "运营模式不能为空")
    private String runType;

    //@NotNull
    private Long pileOwner;

    //pile列表必传
    private Integer size;

    //订单
    private List<Long> stationIds;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Timestamp timeE;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Timestamp timeS;



}
