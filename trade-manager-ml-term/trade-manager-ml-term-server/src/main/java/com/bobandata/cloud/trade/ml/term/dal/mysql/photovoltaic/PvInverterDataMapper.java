package com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvInverterDataDo;
import com.bobandata.cloud.trade.ml.term.service.photovoltaic.dto.PvDayPointCurve;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:07
 * @Classname ChargeInverterDataMapper
 * @Description
 */
public interface PvInverterDataMapper extends BaseCrudMapper<PvInverterDataDo> {

    @Select("select curve_date as curveDate, round(ifnull(sum(p96), 0) / 1000,4) as sumEnergy\n" +
            "from pv_inverter_data\n" +
            "where curve_type = 1 and curve_date <= #{endDate} and curve_date >= #{startDate}\n" +
            "group by curve_date")
    List<KeyValueVo> selectEnergyGroupDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("select round(ifnull(sum(p96), 0) / 1000,4) as monthSumEnergy\n" +
            "from pv_inverter_data\n" +
            "where curve_type = 1 and date_format(curve_date,'%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m');")
    BigDecimal selectSumMonthEnergy();

    @Select("select round(ifnull(sum(p96), 0) / 1000,4) as yearSumEnergy\n" +
            "from pv_inverter_data\n" +
            "where curve_type = 1 and YEAR(curve_date) = YEAR(CURDATE())")
    BigDecimal selectSumYearEnergy();

    @Select("select curve_date,\n" +
            "       curve_type,\n" +
            "       case curve_type\n" +
            "           when 1 then '累计电量'\n" +
            "           when 2 then '电力'\n" +
            "           end                      as curve_name,\n" +
            "       round(sum(p1) / 1000, 4)  as p1,\n" +
            "       round(sum(p2) / 1000, 4)  as p2,\n" +
            "       round(sum(p3) / 1000, 4)  as p3,\n" +
            "       round(sum(p4) / 1000, 4)  as p4,\n" +
            "       round(sum(p5) / 1000, 4)  as p5,\n" +
            "       round(sum(p6) / 1000, 4)  as p6,\n" +
            "       round(sum(p7) / 1000, 4)  as p7,\n" +
            "       round(sum(p8) / 1000, 4)  as p8,\n" +
            "       round(sum(p9) / 1000, 4)  as p9,\n" +
            "       round(sum(p10) / 1000, 4) as p10,\n" +
            "       round(sum(p11) / 1000, 4) as p11,\n" +
            "       round(sum(p12) / 1000, 4) as p12,\n" +
            "       round(sum(p13) / 1000, 4) as p13,\n" +
            "       round(sum(p14) / 1000, 4) as p14,\n" +
            "       round(sum(p15) / 1000, 4) as p15,\n" +
            "       round(sum(p16) / 1000, 4) as p16,\n" +
            "       round(sum(p17) / 1000, 4) as p17,\n" +
            "       round(sum(p18) / 1000, 4) as p18,\n" +
            "       round(sum(p19) / 1000, 4) as p19,\n" +
            "       round(sum(p20) / 1000, 4) as p20,\n" +
            "       round(sum(p21) / 1000, 4) as p21,\n" +
            "       round(sum(p22) / 1000, 4) as p22,\n" +
            "       round(sum(p23) / 1000, 4) as p23,\n" +
            "       round(sum(p24) / 1000, 4) as p24,\n" +
            "       round(sum(p25) / 1000, 4) as p25,\n" +
            "       round(sum(p26) / 1000, 4) as p26,\n" +
            "       round(sum(p27) / 1000, 4) as p27,\n" +
            "       round(sum(p28) / 1000, 4) as p28,\n" +
            "       round(sum(p29) / 1000, 4) as p29,\n" +
            "       round(sum(p30) / 1000, 4) as p30,\n" +
            "       round(sum(p31) / 1000, 4) as p31,\n" +
            "       round(sum(p32) / 1000, 4) as p32,\n" +
            "       round(sum(p33) / 1000, 4) as p33,\n" +
            "       round(sum(p34) / 1000, 4) as p34,\n" +
            "       round(sum(p35) / 1000, 4) as p35,\n" +
            "       round(sum(p36) / 1000, 4) as p36,\n" +
            "       round(sum(p37) / 1000, 4) as p37,\n" +
            "       round(sum(p38) / 1000, 4) as p38,\n" +
            "       round(sum(p39) / 1000, 4) as p39,\n" +
            "       round(sum(p40) / 1000, 4) as p40,\n" +
            "       round(sum(p41) / 1000, 4) as p41,\n" +
            "       round(sum(p42) / 1000, 4) as p42,\n" +
            "       round(sum(p43) / 1000, 4) as p43,\n" +
            "       round(sum(p44) / 1000, 4) as p44,\n" +
            "       round(sum(p45) / 1000, 4) as p45,\n" +
            "       round(sum(p46) / 1000, 4) as p46,\n" +
            "       round(sum(p47) / 1000, 4) as p47,\n" +
            "       round(sum(p48) / 1000, 4) as p48,\n" +
            "       round(sum(p49) / 1000, 4) as p49,\n" +
            "       round(sum(p50) / 1000, 4) as p50,\n" +
            "       round(sum(p51) / 1000, 4) as p51,\n" +
            "       round(sum(p52) / 1000, 4) as p52,\n" +
            "       round(sum(p53) / 1000, 4) as p53,\n" +
            "       round(sum(p54) / 1000, 4) as p54,\n" +
            "       round(sum(p55) / 1000, 4) as p55,\n" +
            "       round(sum(p56) / 1000, 4) as p56,\n" +
            "       round(sum(p57) / 1000, 4) as p57,\n" +
            "       round(sum(p58) / 1000, 4) as p58,\n" +
            "       round(sum(p59) / 1000, 4) as p59,\n" +
            "       round(sum(p60) / 1000, 4) as p60,\n" +
            "       round(sum(p61) / 1000, 4) as p61,\n" +
            "       round(sum(p62) / 1000, 4) as p62,\n" +
            "       round(sum(p63) / 1000, 4) as p63,\n" +
            "       round(sum(p64) / 1000, 4) as p64,\n" +
            "       round(sum(p65) / 1000, 4) as p65,\n" +
            "       round(sum(p66) / 1000, 4) as p66,\n" +
            "       round(sum(p67) / 1000, 4) as p67,\n" +
            "       round(sum(p68) / 1000, 4) as p68,\n" +
            "       round(sum(p69) / 1000, 4) as p69,\n" +
            "       round(sum(p70) / 1000, 4) as p70,\n" +
            "       round(sum(p71) / 1000, 4) as p71,\n" +
            "       round(sum(p72) / 1000, 4) as p72,\n" +
            "       round(sum(p73) / 1000, 4) as p73,\n" +
            "       round(sum(p74) / 1000, 4) as p74,\n" +
            "       round(sum(p75) / 1000, 4) as p75,\n" +
            "       round(sum(p76) / 1000, 4) as p76,\n" +
            "       round(sum(p77) / 1000, 4) as p77,\n" +
            "       round(sum(p78) / 1000, 4) as p78,\n" +
            "       round(sum(p79) / 1000, 4) as p79,\n" +
            "       round(sum(p80) / 1000, 4) as p80,\n" +
            "       round(sum(p81) / 1000, 4) as p81,\n" +
            "       round(sum(p82) / 1000, 4) as p82,\n" +
            "       round(sum(p83) / 1000, 4) as p83,\n" +
            "       round(sum(p84) / 1000, 4) as p84,\n" +
            "       round(sum(p85) / 1000, 4) as p85,\n" +
            "       round(sum(p86) / 1000, 4) as p86,\n" +
            "       round(sum(p87) / 1000, 4) as p87,\n" +
            "       round(sum(p88) / 1000, 4) as p88,\n" +
            "       round(sum(p89) / 1000, 4) as p89,\n" +
            "       round(sum(p90) / 1000, 4) as p90,\n" +
            "       round(sum(p91) / 1000, 4) as p91,\n" +
            "       round(sum(p92) / 1000, 4) as p92,\n" +
            "       round(sum(p93) / 1000, 4) as p93,\n" +
            "       round(sum(p94) / 1000, 4) as p94,\n" +
            "       round(sum(p95) / 1000, 4) as p95,\n" +
            "       round(sum(p96) / 1000, 4) as p96\n" +
            "from pv_inverter_data\n" +
            "where curve_date = #{date} and curve_type = 1\n" +
            "group by curve_type\n" +
            "union all\n" +
            "select curve_date,\n" +
            "       curve_type,\n" +
            "       case curve_type\n" +
            "           when 1 then '电量'\n" +
            "           when 2 then '电力'\n" +
            "           end                      as curve_name,\n" +
            "       round(sum(p1) / 1000000, 4)  as p1,\n" +
            "       round(sum(p2) / 1000000, 4)  as p2,\n" +
            "       round(sum(p3) / 1000000, 4)  as p3,\n" +
            "       round(sum(p4) / 1000000, 4)  as p4,\n" +
            "       round(sum(p5) / 1000000, 4)  as p5,\n" +
            "       round(sum(p6) / 1000000, 4)  as p6,\n" +
            "       round(sum(p7) / 1000000, 4)  as p7,\n" +
            "       round(sum(p8) / 1000000, 4)  as p8,\n" +
            "       round(sum(p9) / 1000000, 4)  as p9,\n" +
            "       round(sum(p10) / 1000000, 4) as p10,\n" +
            "       round(sum(p11) / 1000000, 4) as p11,\n" +
            "       round(sum(p12) / 1000000, 4) as p12,\n" +
            "       round(sum(p13) / 1000000, 4) as p13,\n" +
            "       round(sum(p14) / 1000000, 4) as p14,\n" +
            "       round(sum(p15) / 1000000, 4) as p15,\n" +
            "       round(sum(p16) / 1000000, 4) as p16,\n" +
            "       round(sum(p17) / 1000000, 4) as p17,\n" +
            "       round(sum(p18) / 1000000, 4) as p18,\n" +
            "       round(sum(p19) / 1000000, 4) as p19,\n" +
            "       round(sum(p20) / 1000000, 4) as p20,\n" +
            "       round(sum(p21) / 1000000, 4) as p21,\n" +
            "       round(sum(p22) / 1000000, 4) as p22,\n" +
            "       round(sum(p23) / 1000000, 4) as p23,\n" +
            "       round(sum(p24) / 1000000, 4) as p24,\n" +
            "       round(sum(p25) / 1000000, 4) as p25,\n" +
            "       round(sum(p26) / 1000000, 4) as p26,\n" +
            "       round(sum(p27) / 1000000, 4) as p27,\n" +
            "       round(sum(p28) / 1000000, 4) as p28,\n" +
            "       round(sum(p29) / 1000000, 4) as p29,\n" +
            "       round(sum(p30) / 1000000, 4) as p30,\n" +
            "       round(sum(p31) / 1000000, 4) as p31,\n" +
            "       round(sum(p32) / 1000000, 4) as p32,\n" +
            "       round(sum(p33) / 1000000, 4) as p33,\n" +
            "       round(sum(p34) / 1000000, 4) as p34,\n" +
            "       round(sum(p35) / 1000000, 4) as p35,\n" +
            "       round(sum(p36) / 1000000, 4) as p36,\n" +
            "       round(sum(p37) / 1000000, 4) as p37,\n" +
            "       round(sum(p38) / 1000000, 4) as p38,\n" +
            "       round(sum(p39) / 1000000, 4) as p39,\n" +
            "       round(sum(p40) / 1000000, 4) as p40,\n" +
            "       round(sum(p41) / 1000000, 4) as p41,\n" +
            "       round(sum(p42) / 1000000, 4) as p42,\n" +
            "       round(sum(p43) / 1000000, 4) as p43,\n" +
            "       round(sum(p44) / 1000000, 4) as p44,\n" +
            "       round(sum(p45) / 1000000, 4) as p45,\n" +
            "       round(sum(p46) / 1000000, 4) as p46,\n" +
            "       round(sum(p47) / 1000000, 4) as p47,\n" +
            "       round(sum(p48) / 1000000, 4) as p48,\n" +
            "       round(sum(p49) / 1000000, 4) as p49,\n" +
            "       round(sum(p50) / 1000000, 4) as p50,\n" +
            "       round(sum(p51) / 1000000, 4) as p51,\n" +
            "       round(sum(p52) / 1000000, 4) as p52,\n" +
            "       round(sum(p53) / 1000000, 4) as p53,\n" +
            "       round(sum(p54) / 1000000, 4) as p54,\n" +
            "       round(sum(p55) / 1000000, 4) as p55,\n" +
            "       round(sum(p56) / 1000000, 4) as p56,\n" +
            "       round(sum(p57) / 1000000, 4) as p57,\n" +
            "       round(sum(p58) / 1000000, 4) as p58,\n" +
            "       round(sum(p59) / 1000000, 4) as p59,\n" +
            "       round(sum(p60) / 1000000, 4) as p60,\n" +
            "       round(sum(p61) / 1000000, 4) as p61,\n" +
            "       round(sum(p62) / 1000000, 4) as p62,\n" +
            "       round(sum(p63) / 1000000, 4) as p63,\n" +
            "       round(sum(p64) / 1000000, 4) as p64,\n" +
            "       round(sum(p65) / 1000000, 4) as p65,\n" +
            "       round(sum(p66) / 1000000, 4) as p66,\n" +
            "       round(sum(p67) / 1000000, 4) as p67,\n" +
            "       round(sum(p68) / 1000000, 4) as p68,\n" +
            "       round(sum(p69) / 1000000, 4) as p69,\n" +
            "       round(sum(p70) / 1000000, 4) as p70,\n" +
            "       round(sum(p71) / 1000000, 4) as p71,\n" +
            "       round(sum(p72) / 1000000, 4) as p72,\n" +
            "       round(sum(p73) / 1000000, 4) as p73,\n" +
            "       round(sum(p74) / 1000000, 4) as p74,\n" +
            "       round(sum(p75) / 1000000, 4) as p75,\n" +
            "       round(sum(p76) / 1000000, 4) as p76,\n" +
            "       round(sum(p77) / 1000000, 4) as p77,\n" +
            "       round(sum(p78) / 1000000, 4) as p78,\n" +
            "       round(sum(p79) / 1000000, 4) as p79,\n" +
            "       round(sum(p80) / 1000000, 4) as p80,\n" +
            "       round(sum(p81) / 1000000, 4) as p81,\n" +
            "       round(sum(p82) / 1000000, 4) as p82,\n" +
            "       round(sum(p83) / 1000000, 4) as p83,\n" +
            "       round(sum(p84) / 1000000, 4) as p84,\n" +
            "       round(sum(p85) / 1000000, 4) as p85,\n" +
            "       round(sum(p86) / 1000000, 4) as p86,\n" +
            "       round(sum(p87) / 1000000, 4) as p87,\n" +
            "       round(sum(p88) / 1000000, 4) as p88,\n" +
            "       round(sum(p89) / 1000000, 4) as p89,\n" +
            "       round(sum(p90) / 1000000, 4) as p90,\n" +
            "       round(sum(p91) / 1000000, 4) as p91,\n" +
            "       round(sum(p92) / 1000000, 4) as p92,\n" +
            "       round(sum(p93) / 1000000, 4) as p93,\n" +
            "       round(sum(p94) / 1000000, 4) as p94,\n" +
            "       round(sum(p95) / 1000000, 4) as p95,\n" +
            "       round(sum(p96) / 1000000, 4) as p96\n" +
            "from pv_inverter_data\n" +
            "where curve_date = #{date} and curve_type = 2\n" +
            "group by curve_type\n" +
            "union all\n" +
            "select curve_date,\n" +
            "       curve_type,\n" +
            "       '电量'                                                      as curve_name,\n" +
            "       0                                                         as p1,\n" +
            "       if(round(sum(p3) / 1000, 4) - round(sum(p2) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p3) / 1000, 4) - round(sum(p2) / 1000, 4))   as p2,\n" +
            "       if(round(sum(p4) / 1000, 4) - round(sum(p3) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p4) / 1000, 4) - round(sum(p3) / 1000, 4))   as p3,\n" +
            "       if(round(sum(p5) / 1000, 4) - round(sum(p4) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p5) / 1000, 4) - round(sum(p4) / 1000, 4))   as p4,\n" +
            "       if(round(sum(p6) / 1000, 4) - round(sum(p5) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p6) / 1000, 4) - round(sum(p5) / 1000, 4))   as p5,\n" +
            "       if(round(sum(p7) / 1000, 4) - round(sum(p6) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p7) / 1000, 4) - round(sum(p6) / 1000, 4))   as p6,\n" +
            "       if(round(sum(p8) / 1000, 4) - round(sum(p7) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p8) / 1000, 4) - round(sum(p7) / 1000, 4))   as p7,\n" +
            "       if(round(sum(p9) / 1000, 4) - round(sum(p8) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p9) / 1000, 4) - round(sum(p8) / 1000, 4))   as p8,\n" +
            "       if(round(sum(p10) / 1000, 4) - round(sum(p9) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p10) / 1000, 4) - round(sum(p9) / 1000, 4))  as p9,\n" +
            "       if(round(sum(p11) / 1000, 4) - round(sum(p10) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p11) / 1000, 4) - round(sum(p10) / 1000, 4)) as p10,\n" +
            "       if(round(sum(p12) / 1000, 4) - round(sum(p11) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p12) / 1000, 4) - round(sum(p11) / 1000, 4)) as p11,\n" +
            "       if(round(sum(p13) / 1000, 4) - round(sum(p12) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p13) / 1000, 4) - round(sum(p12) / 1000, 4)) as p12,\n" +
            "       if(round(sum(p14) / 1000, 4) - round(sum(p13) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p14) / 1000, 4) - round(sum(p13) / 1000, 4)) as p13,\n" +
            "       if(round(sum(p15) / 1000, 4) - round(sum(p14) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p15) / 1000, 4) - round(sum(p14) / 1000, 4)) as p14,\n" +
            "       if(round(sum(p16) / 1000, 4) - round(sum(p15) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p16) / 1000, 4) - round(sum(p15) / 1000, 4)) as p15,\n" +
            "       if(round(sum(p17) / 1000, 4) - round(sum(p16) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p17) / 1000, 4) - round(sum(p16) / 1000, 4)) as p16,\n" +
            "       if(round(sum(p18) / 1000, 4) - round(sum(p17) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p18) / 1000, 4) - round(sum(p17) / 1000, 4)) as p17,\n" +
            "       if(round(sum(p19) / 1000, 4) - round(sum(p18) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p19) / 1000, 4) - round(sum(p18) / 1000, 4)) as p18,\n" +
            "       if(round(sum(p20) / 1000, 4) - round(sum(p19) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p20) / 1000, 4) - round(sum(p19) / 1000, 4)) as p19,\n" +
            "       if(round(sum(p21) / 1000, 4) - round(sum(p20) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p21) / 1000, 4) - round(sum(p20) / 1000, 4)) as p20,\n" +
            "       if(round(sum(p22) / 1000, 4) - round(sum(p21) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p22) / 1000, 4) - round(sum(p21) / 1000, 4)) as p21,\n" +
            "       if(round(sum(p23) / 1000, 4) - round(sum(p22) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p23) / 1000, 4) - round(sum(p22) / 1000, 4)) as p22,\n" +
            "       if(round(sum(p24) / 1000, 4) - round(sum(p23) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p24) / 1000, 4) - round(sum(p23) / 1000, 4)) as p23,\n" +
            "       if(round(sum(p25) / 1000, 4) - round(sum(p24) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p25) / 1000, 4) - round(sum(p24) / 1000, 4)) as p24,\n" +
            "       if(round(sum(p26) / 1000, 4) - round(sum(p25) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p26) / 1000, 4) - round(sum(p25) / 1000, 4)) as p25,\n" +
            "       if(round(sum(p27) / 1000, 4) - round(sum(p26) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p27) / 1000, 4) - round(sum(p26) / 1000, 4)) as p26,\n" +
            "       if(round(sum(p28) / 1000, 4) - round(sum(p27) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p28) / 1000, 4) - round(sum(p27) / 1000, 4)) as p27,\n" +
            "       if(round(sum(p29) / 1000, 4) - round(sum(p28) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p29) / 1000, 4) - round(sum(p28) / 1000, 4)) as p28,\n" +
            "       if(round(sum(p30) / 1000, 4) - round(sum(p29) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p30) / 1000, 4) - round(sum(p29) / 1000, 4)) as p29,\n" +
            "       if(round(sum(p31) / 1000, 4) - round(sum(p30) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p31) / 1000, 4) - round(sum(p30) / 1000, 4)) as p30,\n" +
            "       if(round(sum(p32) / 1000, 4) - round(sum(p31) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p32) / 1000, 4) - round(sum(p31) / 1000, 4)) as p31,\n" +
            "       if(round(sum(p33) / 1000, 4) - round(sum(p32) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p33) / 1000, 4) - round(sum(p32) / 1000, 4)) as p32,\n" +
            "       if(round(sum(p34) / 1000, 4) - round(sum(p33) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p34) / 1000, 4) - round(sum(p33) / 1000, 4)) as p33,\n" +
            "       if(round(sum(p35) / 1000, 4) - round(sum(p34) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p35) / 1000, 4) - round(sum(p34) / 1000, 4)) as p34,\n" +
            "       if(round(sum(p36) / 1000, 4) - round(sum(p35) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p36) / 1000, 4) - round(sum(p35) / 1000, 4)) as p35,\n" +
            "       if(round(sum(p37) / 1000, 4) - round(sum(p36) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p37) / 1000, 4) - round(sum(p36) / 1000, 4)) as p36,\n" +
            "       if(round(sum(p38) / 1000, 4) - round(sum(p37) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p38) / 1000, 4) - round(sum(p37) / 1000, 4)) as p37,\n" +
            "       if(round(sum(p39) / 1000, 4) - round(sum(p38) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p39) / 1000, 4) - round(sum(p38) / 1000, 4)) as p38,\n" +
            "       if(round(sum(p40) / 1000, 4) - round(sum(p39) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p40) / 1000, 4) - round(sum(p39) / 1000, 4)) as p39,\n" +
            "       if(round(sum(p41) / 1000, 4) - round(sum(p40) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p41) / 1000, 4) - round(sum(p40) / 1000, 4)) as p40,\n" +
            "       if(round(sum(p42) / 1000, 4) - round(sum(p41) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p42) / 1000, 4) - round(sum(p41) / 1000, 4)) as p41,\n" +
            "       if(round(sum(p43) / 1000, 4) - round(sum(p42) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p43) / 1000, 4) - round(sum(p42) / 1000, 4)) as p42,\n" +
            "       if(round(sum(p44) / 1000, 4) - round(sum(p43) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p44) / 1000, 4) - round(sum(p43) / 1000, 4)) as p43,\n" +
            "       if(round(sum(p45) / 1000, 4) - round(sum(p44) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p45) / 1000, 4) - round(sum(p44) / 1000, 4)) as p44,\n" +
            "       if(round(sum(p46) / 1000, 4) - round(sum(p45) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p46) / 1000, 4) - round(sum(p45) / 1000, 4)) as p45,\n" +
            "       if(round(sum(p47) / 1000, 4) - round(sum(p46) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p47) / 1000, 4) - round(sum(p46) / 1000, 4)) as p46,\n" +
            "       if(round(sum(p48) / 1000, 4) - round(sum(p47) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p48) / 1000, 4) - round(sum(p47) / 1000, 4)) as p47,\n" +
            "       if(round(sum(p49) / 1000, 4) - round(sum(p48) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p49) / 1000, 4) - round(sum(p48) / 1000, 4)) as p48,\n" +
            "       if(round(sum(p50) / 1000, 4) - round(sum(p49) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p50) / 1000, 4) - round(sum(p49) / 1000, 4)) as p49,\n" +
            "       if(round(sum(p51) / 1000, 4) - round(sum(p50) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p51) / 1000, 4) - round(sum(p50) / 1000, 4)) as p50,\n" +
            "       if(round(sum(p52) / 1000, 4) - round(sum(p51) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p52) / 1000, 4) - round(sum(p51) / 1000, 4)) as p51,\n" +
            "       if(round(sum(p53) / 1000, 4) - round(sum(p52) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p53) / 1000, 4) - round(sum(p52) / 1000, 4)) as p52,\n" +
            "       if(round(sum(p54) / 1000, 4) - round(sum(p53) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p54) / 1000, 4) - round(sum(p53) / 1000, 4)) as p53,\n" +
            "       if(round(sum(p55) / 1000, 4) - round(sum(p54) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p55) / 1000, 4) - round(sum(p54) / 1000, 4)) as p54,\n" +
            "       if(round(sum(p56) / 1000, 4) - round(sum(p55) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p56) / 1000, 4) - round(sum(p55) / 1000, 4)) as p55,\n" +
            "       if(round(sum(p57) / 1000, 4) - round(sum(p56) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p57) / 1000, 4) - round(sum(p56) / 1000, 4)) as p56,\n" +
            "       if(round(sum(p58) / 1000, 4) - round(sum(p57) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p58) / 1000, 4) - round(sum(p57) / 1000, 4)) as p57,\n" +
            "       if(round(sum(p59) / 1000, 4) - round(sum(p58) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p59) / 1000, 4) - round(sum(p58) / 1000, 4)) as p58,\n" +
            "       if(round(sum(p60) / 1000, 4) - round(sum(p59) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p60) / 1000, 4) - round(sum(p59) / 1000, 4)) as p59,\n" +
            "       if(round(sum(p61) / 1000, 4) - round(sum(p60) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p61) / 1000, 4) - round(sum(p60) / 1000, 4)) as p60,\n" +
            "       if(round(sum(p62) / 1000, 4) - round(sum(p61) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p62) / 1000, 4) - round(sum(p61) / 1000, 4)) as p61,\n" +
            "       if(round(sum(p63) / 1000, 4) - round(sum(p62) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p63) / 1000, 4) - round(sum(p62) / 1000, 4)) as p62,\n" +
            "       if(round(sum(p64) / 1000, 4) - round(sum(p63) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p64) / 1000, 4) - round(sum(p63) / 1000, 4)) as p63,\n" +
            "       if(round(sum(p65) / 1000, 4) - round(sum(p64) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p65) / 1000, 4) - round(sum(p64) / 1000, 4)) as p64,\n" +
            "       if(round(sum(p66) / 1000, 4) - round(sum(p65) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p66) / 1000, 4) - round(sum(p65) / 1000, 4)) as p65,\n" +
            "       if(round(sum(p67) / 1000, 4) - round(sum(p66) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p67) / 1000, 4) - round(sum(p66) / 1000, 4)) as p66,\n" +
            "       if(round(sum(p68) / 1000, 4) - round(sum(p67) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p68) / 1000, 4) - round(sum(p67) / 1000, 4)) as p67,\n" +
            "       if(round(sum(p69) / 1000, 4) - round(sum(p68) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p69) / 1000, 4) - round(sum(p68) / 1000, 4)) as p68,\n" +
            "       if(round(sum(p70) / 1000, 4) - round(sum(p69) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p70) / 1000, 4) - round(sum(p69) / 1000, 4)) as p69,\n" +
            "       if(round(sum(p71) / 1000, 4) - round(sum(p70) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p71) / 1000, 4) - round(sum(p70) / 1000, 4)) as p70,\n" +
            "       if(round(sum(p72) / 1000, 4) - round(sum(p71) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p72) / 1000, 4) - round(sum(p71) / 1000, 4)) as p71,\n" +
            "       if(round(sum(p73) / 1000, 4) - round(sum(p72) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p73) / 1000, 4) - round(sum(p72) / 1000, 4)) as p72,\n" +
            "       if(round(sum(p74) / 1000, 4) - round(sum(p73) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p74) / 1000, 4) - round(sum(p73) / 1000, 4)) as p73,\n" +
            "       if(round(sum(p75) / 1000, 4) - round(sum(p74) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p75) / 1000, 4) - round(sum(p74) / 1000, 4)) as p74,\n" +
            "       if(round(sum(p76) / 1000, 4) - round(sum(p75) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p76) / 1000, 4) - round(sum(p75) / 1000, 4)) as p75,\n" +
            "       if(round(sum(p77) / 1000, 4) - round(sum(p76) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p77) / 1000, 4) - round(sum(p76) / 1000, 4)) as p76,\n" +
            "       if(round(sum(p78) / 1000, 4) - round(sum(p77) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p78) / 1000, 4) - round(sum(p77) / 1000, 4)) as p77,\n" +
            "       if(round(sum(p79) / 1000, 4) - round(sum(p78) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p79) / 1000, 4) - round(sum(p78) / 1000, 4)) as p78,\n" +
            "       if(round(sum(p80) / 1000, 4) - round(sum(p79) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p80) / 1000, 4) - round(sum(p79) / 1000, 4)) as p79,\n" +
            "       if(round(sum(p81) / 1000, 4) - round(sum(p80) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p81) / 1000, 4) - round(sum(p80) / 1000, 4)) as p80,\n" +
            "       if(round(sum(p82) / 1000, 4) - round(sum(p81) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p82) / 1000, 4) - round(sum(p81) / 1000, 4)) as p81,\n" +
            "       if(round(sum(p83) / 1000, 4) - round(sum(p82) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p83) / 1000, 4) - round(sum(p82) / 1000, 4)) as p82,\n" +
            "       if(round(sum(p84) / 1000, 4) - round(sum(p83) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p84) / 1000, 4) - round(sum(p83) / 1000, 4)) as p83,\n" +
            "       if(round(sum(p85) / 1000, 4) - round(sum(p84) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p85) / 1000, 4) - round(sum(p84) / 1000, 4)) as p84,\n" +
            "       if(round(sum(p86) / 1000, 4) - round(sum(p85) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p86) / 1000, 4) - round(sum(p85) / 1000, 4)) as p85,\n" +
            "       if(round(sum(p87) / 1000, 4) - round(sum(p86) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p87) / 1000, 4) - round(sum(p86) / 1000, 4)) as p86,\n" +
            "       if(round(sum(p88) / 1000, 4) - round(sum(p87) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p88) / 1000, 4) - round(sum(p87) / 1000, 4)) as p87,\n" +
            "       if(round(sum(p89) / 1000, 4) - round(sum(p88) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p89) / 1000, 4) - round(sum(p88) / 1000, 4)) as p88,\n" +
            "       if(round(sum(p90) / 1000, 4) - round(sum(p89) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p90) / 1000, 4) - round(sum(p89) / 1000, 4)) as p89,\n" +
            "       if(round(sum(p91) / 1000, 4) - round(sum(p90) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p91) / 1000, 4) - round(sum(p90) / 1000, 4)) as p90,\n" +
            "       if(round(sum(p92) / 1000, 4) - round(sum(p91) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p92) / 1000, 4) - round(sum(p91) / 1000, 4)) as p91,\n" +
            "       if(round(sum(p93) / 1000, 4) - round(sum(p92) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p93) / 1000, 4) - round(sum(p92) / 1000, 4)) as p92,\n" +
            "       if(round(sum(p94) / 1000, 4) - round(sum(p93) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p94) / 1000, 4) - round(sum(p93) / 1000, 4)) as p93,\n" +
            "       if(round(sum(p95) / 1000, 4) - round(sum(p94) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p95) / 1000, 4) - round(sum(p94) / 1000, 4)) as p94,\n" +
            "       if(round(sum(p96) / 1000, 4) - round(sum(p95) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p96) / 1000, 4) - round(sum(p95) / 1000, 4)) as p95,\n" +
            "       if(round(sum(p96) / 1000, 4) - round(sum(p95) / 1000, 4) < 0, 0,\n" +
            "          round(sum(p96) / 1000, 4) - round(sum(p95) / 1000, 4)) as p96\n" +
            "from pv_inverter_data\n" +
            "where curve_date = #{date}\n" +
            "  and curve_type = 1\n" +
            "group by curve_type")
    List<PvDayPointCurve> selectDayEnergy(@Param("date") Date date);
}
