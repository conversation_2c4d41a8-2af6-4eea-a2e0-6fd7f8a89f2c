package com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @Date 2024/11/1 9:45
 * @Classname PowerConsumptionVo
 * @Description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
public class PowerConsumptionVo implements Serializable {

    //逆变器ID
    //@NotBlank(message = "逆变器ID不能为空")
    private String invId;

    //时间点"2024-01-01 00:15:00"
    //@NotBlank(message = "时间点不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String dataTime;

    //15分钟累积发电量
    private BigDecimal dayPower;

    //15分钟功率
    private BigDecimal totalAcp;

}
