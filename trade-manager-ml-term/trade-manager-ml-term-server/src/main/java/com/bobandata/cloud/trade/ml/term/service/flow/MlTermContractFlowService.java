package com.bobandata.cloud.trade.ml.term.service.flow;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowReqVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface MlTermContractFlowService extends IService<MlTermContractFlowDo> {

    PagingResult<ContractFlowRespVo> pageContractFlow(ContractFlowReqVo reqVo);
}
