package com.bobandata.cloud.trade.ml.term.controller.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:29
 * @description
 */
@Schema(description = "结算管理-正式结算单查询 月度结算明细")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SettleBatchRetailRespVo {

    @Schema(description = "结算单月份")
    private String settleMonth;

    @Schema(description = "购电侧明细")
    private List<Detail> batchInfos;

    @Schema(description = "售电侧明细")
    private List<Detail> retailInfos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class Detail {
        /**
         * 结算科目
         */
        @Schema(description = "结算科目编码")
        private String settleSubjectNo;

        /**
         * 结算科目编码
         */
        @Schema(description = "结算科目")
        private String settleSubjectName;

        /**
         * 交易计划电量
         */
        @Schema(description = "交易计划电量")
        private BigDecimal tradePlanEnergy;

        /**
         * 结算电量
         */
        @Schema(description = "结算电量")
        private BigDecimal settleEnergy;

        /**
         * 结算电价
         */
        @Schema(description = "结算电价")
        private BigDecimal settlePrice;

        /**
         * 结算费用
         */
        @Schema(description = "结算费用")
        private BigDecimal settleFee;
    }
}
