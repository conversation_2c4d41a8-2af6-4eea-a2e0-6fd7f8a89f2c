package com.bobandata.cloud.trade.ml.term.dal.dataobject.offset;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 中长期交易：批发侧结算单
 *
 * @TableName ml_settle_batch
 */
@TableName(value = "corp_cons_real_energy_hunan")
@Data
public class OffsetDo extends BaseDo {

    /**
     * 所属区域
     */
    @TableField(value = "cons_no")
    private String consNo;

    /**
     * 结算科目
     */
    @TableField(value = "data_date")
    private Date data_date;


}