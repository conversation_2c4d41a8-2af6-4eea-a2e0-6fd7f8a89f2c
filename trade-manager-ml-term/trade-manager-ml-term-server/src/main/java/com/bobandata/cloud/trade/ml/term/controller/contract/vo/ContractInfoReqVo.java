package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-22日 10:46
 * @description
 */
@Schema(description = "中长期管理-合同持仓管理")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ContractInfoReqVo {

    @Schema(description = "开始年度")
    private Integer startYear;

    @Schema(description = "结束年度")
    private Integer endYear;


    @Schema(description = "开始年月")
    private String startTime;

    @Schema(description = "结束年月")
    private String endTime;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "数据类型")
    private String dataTypeStr;

    @Schema(description = "开始月份")
    private Integer startMonth;

    @Schema(description = "结束月份")
    private Integer endMonth;

    @Schema(description = "交易序列名称")
    private String arrName;

    @Schema(description = "卖方公司名称")
    private String sellerUnitName;

    @Schema(description = "买方公司名称")
    private String purchaseUnitName;

}
