package com.bobandata.cloud.trade.ml.term.service.flow;

import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowReqVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowRespVo;
import com.bobandata.cloud.trade.ml.term.convert.FlowConvert;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractFlowDoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service实现
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class MlTermContractFlowServiceImpl extends BaseServiceImpl<MlTermContractFlowDoMapper, MlTermContractFlowDo> implements MlTermContractFlowService {

    @Override
    public PagingResult<ContractFlowRespVo> pageContractFlow(ContractFlowReqVo reqVo) {
        PagingResult<MlTermContractFlowDo> pagingResult = this.getBaseMapper().selectPage(reqVo);
        return FlowConvert.INSTANCE.convertPage(pagingResult);
    }
}




