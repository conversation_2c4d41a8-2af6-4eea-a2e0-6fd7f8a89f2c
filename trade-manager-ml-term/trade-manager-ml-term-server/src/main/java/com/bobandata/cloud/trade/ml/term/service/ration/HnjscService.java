package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnjsc;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface HnjscService extends IService<Hnjsc> {

    ServiceResult<Boolean> insert(Hnjsc hnjsc);

    ServiceResult<Boolean> updateRation(Hnjsc hnjsc);

    ServiceResult<Boolean> delete(Long id);

    ServiceResult<List<Hnjsc>> listRation(String dateTime);
}
