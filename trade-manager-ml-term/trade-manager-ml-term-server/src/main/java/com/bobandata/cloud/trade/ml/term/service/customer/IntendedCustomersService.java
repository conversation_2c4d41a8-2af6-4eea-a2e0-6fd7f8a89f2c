package com.bobandata.cloud.trade.ml.term.service.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.ConsRespVo;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.IntendedCustomersReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.IntendedCustomers;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface IntendedCustomersService extends IService<IntendedCustomers> {

    ServiceResult<Boolean> insert(IntendedCustomers intendedCustomers);

    PagingResult<IntendedCustomers> listIntendedCustomer(IntendedCustomersReqVo reqVo);

    ServiceResult<Boolean> updateIntendedCustomer(IntendedCustomers intendedCustomers);

    ServiceResult<Boolean> delete(Long id);

    ServiceResult<List<ConsRespVo>> getConsNameAndNo(String key);

    ServiceResult<Boolean> toCons(Long id);

    List<Map> getLevel();

    List<Map> getYd();
}
