package com.bobandata.cloud.trade.ml.term.dal.mysql.offset;

import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetReqVo;
import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.offset.OffsetDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024-03-19 09:43:56
 * @Entity com.bobandata.cloud.trade.ml.term.domain.MlSettleBatch
 */
public interface OffsetMapper extends BaseCrudMapper<OffsetDo> {

    List<OffsetRespVo> listDlTotal(OffsetReqVo offsetReqVo);
}




