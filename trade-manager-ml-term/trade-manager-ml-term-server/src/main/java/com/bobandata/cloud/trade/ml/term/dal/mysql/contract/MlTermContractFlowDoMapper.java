package com.bobandata.cloud.trade.ml.term.dal.mysql.contract;

import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractFlowReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractFlowDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Mapper
 * @createDate 2024-03-18 10:08:06
 * @Entity com.bobandata.cloud.trade.ml.term.domain.MlTermContractFlowDo
 */
public interface MlTermContractFlowDoMapper extends BaseCrudMapper<MlTermContractFlowDo> {

    default PagingResult<MlTermContractFlowDo> selectPage(ContractFlowReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<MlTermContractFlowDo>()
                .likeIfPresent(MlTermContractFlowDo::getSaleUnitsName, reqVO.getSaleUnitsName())
                .likeIfPresent(MlTermContractFlowDo::getVendeeUnitsName, reqVO.getVendeeUnitsName())
                .eqIfPresent(MlTermContractFlowDo::getTradeSegId, reqVO.getTradeSegId())
                .likeIfPresent(
                        MlTermContractFlowDo::getContractCreateTime,
                        reqVO.getContractCreateDate()
                )
                .betweenIfPresent(
                        MlTermContractFlowDo::getStartTime, reqVO.getStartTime(), reqVO.getEndTime())
                .orderByDesc(MlTermContractFlowDo::getStartTime));
    }
}




