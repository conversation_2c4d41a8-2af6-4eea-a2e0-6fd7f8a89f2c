package com.bobandata.cloud.trade.ml.term.service.settle;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleBatchMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_batch(中长期交易：批发侧结算单)】的数据库操作Service
 * @createDate 2024-03-19 09:43:56
 */
public interface MlSettleBatchService extends BaseService<MlSettleBatchMapper, MlSettleBatchDo> {

    List<MlSettleBatchDo> listByMonth(String month);
}
