package com.bobandata.cloud.trade.ml.term.controller.offset.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "分页")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OffsetReqVo extends PageParam {

    @Schema(description = "用户户号ids")
    protected List<Long> ids;

    @Schema(description = "用户户名或户号")
    protected String startTime;

    @Schema(description = "电压等级")
    protected String endTime;

}
