package com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-11-04日 15:43
 * @description
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
@Schema(description = "日电量统计")
public class PvStatDataVo {

    @Schema(description = "接入装机容量")
    private BigDecimal sumCap;

    @Schema(description = "场站数量")
    private Integer stationSize;

    @Schema(description = "当月累计发电量 ")
    private BigDecimal monthSumEnergy;

    @Schema(description = "年度累计发电量")
    private BigDecimal yearSumEnergy;

    @Schema(description = "装机容量分布")
    private List<KeyValueVo> capData;

    @Schema(description = "日电量")
    private List<KeyValueVo> dayGroupEnergy;
}
