package com.bobandata.cloud.trade.ml.term.controller.abandon.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/16 11:45
 * @Description
 */
@Schema(description = "需求响应-响应信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseReqVo extends PageParam {

    @Schema(description = "户号")
    private String consNo;

    @Schema(description = "户名")
    private String consName;

    /**
     * 响应时间，如 2024-05-30
     */
    @Schema(description = "响应时间")
    private Date balDate;

}
