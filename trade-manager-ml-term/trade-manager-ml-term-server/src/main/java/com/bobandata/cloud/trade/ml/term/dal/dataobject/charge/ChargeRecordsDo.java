package com.bobandata.cloud.trade.ml.term.dal.dataobject.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <p>
 * 充电订单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charge_records")
public class ChargeRecordsDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 流水号
     */
    @TableField("serial_number")
    private String serialNumber;

    /**
     * 第三方流水号ID
     */
    @TableField("third_party_id")
    private String thirdPartyId;

    /**
     * 第三方流水号
     */
    @TableField("third_party_serial_number")
    private String thirdPartySerialNumber;

    /**
     * 充电站ID
     */
    @TableField("station_id")
    private String stationId;

    /**
     * 站点名称
     */
    @TableField("station_name")
    private String stationName;

    /**
     * 充电桩ID
     */
    @TableField("pile_id")
    private String pileId;

    /**
     * 充电桩名称
     */
    @TableField("pile_name")
    private String pileName;

    /**
     * 枪号
     */
    @JsonProperty(value = "gunNo")
    @TableField("gun_number")
    private String gunNumber;

    /**
     * 枪ID
     */
    @TableField("gun_id")
    private String gunId;

    /**
     * 充电桩编号-桩体号
     */
    @TableField("pile_no")
    private String pileNo;

    /**
     * 团队/集团名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 会员名称
     */
    @TableField("member_name")
    private String memberName;

    /**
     * 子成员名称/子会员名称
     */
    @TableField("sub_member_name")
    private String subMemberName;

    /**
     * 子成员/子会员
     */
    @TableField("sub_member")
    private String subMember;

    /**
     * 车牌号
     */
    @TableField("car_plate")
    private String carPlate;

    /**
     * 车辆ID
     */
    @TableField("car_id")
    private String carId;

    /**
     * 车辆 VIN 码
     */
    private String vin;

    /**
     * 生产商/生产厂家
     */
    private String producer;

    /**
     * 列表未找到对应-车型信息
     */
    @TableField("car_type_info")
    private String carTypeInfo;

    /**
     * 车辆编号/车辆自编号
     */
    @TableField("car_no")
    private String carNo;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonProperty(value = "stopTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTime;

    /**
     * 充电时长
     */
    @TableField("charge_duration")
    private String chargeDuration;

    /**
     * 列表未找到对应-结清时间
     */
    @TableField("settle_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp settleTime;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp payTime;

    /**
     * 电量
     */
    @TableField("sum_power")
    private BigDecimal sumPower;

    /**
     * 尖电量
     */
    @TableField("sharp_power")
    private BigDecimal sharpPower;

    /**
     * 峰电量
     */
    @TableField("peak_power")
    private BigDecimal peakPower;

    /**
     * 平电量
     */
    @TableField("flat_power")
    private BigDecimal flatPower;

    /**
     * 谷电量
     */
    @TableField("valley_power")
    private BigDecimal valleyPower;

    /**
     * 尖电费
     */
    @TableField("sharp_balance")
    private BigDecimal sharpBalance;

    /**
     * 峰电费
     */
    @TableField("peak_balance")
    private BigDecimal peakBalance;

    /**
     * 平电费
     */
    @TableField("flat_balance")
    private BigDecimal flatBalance;

    /**
     * 谷电费
     */
    @TableField("valley_balance")
    private BigDecimal valleyBalance;

    /**
     * 总电费
     */
    @TableField("power_balance")
    private BigDecimal powerBalance;

    /**
     * 实际电费/实收电费(元)
     */
    @TableField("actual_power_balance")
    private BigDecimal actualPowerBalance;

    /**
     * 服务费(元)
     */
    @TableField("service_balance")
    private BigDecimal serviceBalance;

    /**
     * 开始 soc
     */
    @TableField("start_soc")
    private BigDecimal startSoc;

    /**
     * 结束 soc
     */
    @TableField("end_soc")
    private BigDecimal endSoc;

    /**
     * 结束原因/停止原因
     */
    @TableField("end_reason")
    private String endReason;

    /**
     * 订单业务类型ID
     */
    @TableField("bus_id")
    private String busId;

    /**
     * 订单业务类型
     */
    @TableField("bus_type")
    private String busType;

    /**
     * 供电余额/供电方金额
     */
    @TableField("power_provide_balance")
    private BigDecimal powerProvideBalance;

    /**
     * 用电余额/用电方金额
     */
    @TableField("power_user_balance")
    private BigDecimal powerUserBalance;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 充电模式
     */
    @JsonProperty(value = "chargePolicy")
    @TableField("charging_mode")
    private String chargingMode;

    /**
     * 列表未找到对应-账单金额（元）
     */
    @TableField("bill_amount")
    private BigDecimal billAmount;

    /**
     * 不确定-实际金额/实收金额（元）
     */
    @TableField("actual_balance")
    private BigDecimal actualBalance;

    /**
     * 列表未找到对应-总起始值
     */
    @TableField("total_start_value")
    private BigDecimal totalStartValue;

    /**
     * 列表未找到对应-总止示值
     */
    @TableField("total_end_value")
    private BigDecimal totalEndValue;

    /**
     * 实际服务费/实收服务费（元）
     */
    @TableField("actual_service_balance")
    private BigDecimal actualServiceBalance;

    /**
     * 会员类型
     */
    @TableField("member_type")
    private String memberType;

    /**
     * 线路
     */
    private String line;

    /**
     * 日里程/日运营里程数
     */
    @TableField("daily_mileage")
    private String dailyMileage;

    /**
     * 卡号
     */
    private String card;

    /**
     * 不确定是否和pile_no一个意思-桩流水号
     */
    @TableField("pile_serial_number")
    private String pileSerialNumber;

    /**
     * 总优惠券/其他优惠项(元)
     */
    @TableField("total_coupon")
    private BigDecimal totalCoupon;

    /**
     * 账单/订单状态
     */
    @TableField("bill_status")
    private String billStatus;

    /**
     * 业务显示编号/用户流水号
     */
    @TableField("bus_show_no")
    private String busShowNo;

    /**
     * 评论/评价状态
     */
    @TableField("comment_status")
    private String commentStatus;

    /**
     * 预充值金额/预存金额(元)
     */
    @TableField("pre_charge_amount")
    private BigDecimal preChargeAmount;

    /**
     * 推送状态/订单推送结果
     */
    @TableField("push_status")
    private String pushStatus;

    /**
     * 是否开票完成/是否已开票
     */
    @TableField("is_invoice_finish")
    private Integer isInvoiceFinish;

    /**
     * 坏账金额
     */
    @TableField("bad_money")
    private BigDecimal badMoney;

    /**
     * 异常类型
     */
    @TableField("abnormal_type")
    private String abnormalType;

    /**
     * 支付方式
     */
    @TableField("pay_mode")
    private String payMode;

    /**
     * 列表未找到对应-启动时手机账户余额
     */
    @TableField("start_account_balance")
    private BigDecimal startAccountBalance;

    /**
     * 列表未找到对应-启动时奖励账户余额
     */
    @TableField("start_reward_account_balance")
    private BigDecimal startRewardAccountBalance;

    /**
     * 运营单位/运营商
     */
    @TableField("operating_unit")
    private String operatingUnit;

    /**
     * 会员 ID
     */
    @TableField("member_id")
    private Integer memberId;

    /**
     * 最大电压/峰电压(V)
     */
    @TableField("max_voltage")
    private BigDecimal maxVoltage;

    /**
     * 最大电流/峰电流(A)
     */
    @TableField("max_current")
    private BigDecimal maxCurrent;

    /**
     * 电压需求/BMS 需求电压(V)
     */
    @TableField("voltage_demand")
    private BigDecimal voltageDemand;

    /**
     * 电流需求/BMS 需求电流(A)
     */
    @TableField("current_dmand")
    private BigDecimal currentDmand;

    /**
     * 账单类型/订单启动类型
     */
    @TableField("bill_type")
    private String billType;

    /**
     * 占用费/应收占位费
     */
    @TableField("occupation_fee")
    private BigDecimal occupationFee;

    /**
     * 总折扣余额/优惠总金额(元)
     */
    @TableField("total_discount_balance")
    private BigDecimal totalDiscountBalance;

    /**
     * 总服务折扣余额/总服务费优惠(元)
     */
    @TableField("total_service_discount_balance")
    private BigDecimal totalServiceDiscountBalance;

    /**
     * 总站点折扣余额/电站促销(元)
     */
    @TableField("total_station_discount_balance")
    private BigDecimal totalStationDiscountBalance;

    /**
     * 会员折扣/集团会员折扣(元)
     */
    @TableField("member_discount")
    private BigDecimal memberDiscount;

    /**
     * 应支付电力余额/结算电费(元)
     */
    @TableField("should_pay_power_balance")
    private BigDecimal shouldPayPowerBalance;

    /**
     * 应支付服务余额/结算服务费(元)
     */
    @TableField("should_pay_service_balance")
    private BigDecimal shouldPayServiceBalance;

    /**
     * 应支付余额/结算总金额(元)
     */
    @TableField("should_pay_balance")
    private BigDecimal shouldPayBalance;

}
