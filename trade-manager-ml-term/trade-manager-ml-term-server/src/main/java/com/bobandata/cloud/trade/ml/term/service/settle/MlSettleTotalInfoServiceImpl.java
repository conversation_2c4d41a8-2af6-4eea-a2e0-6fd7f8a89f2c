package com.bobandata.cloud.trade.ml.term.service.settle;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.TransactionsBiasRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleRetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleBatchMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleRetailMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleTotalInfoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_total_info(中长期交易：结算汇总单)】的数据库操作Service实现
 * @createDate 2024-03-19 09:43:55
 */
@Service
@Slf4j
public class MlSettleTotalInfoServiceImpl extends BaseServiceImpl<MlSettleTotalInfoMapper, MlSettleTotalInfoDo> implements MlSettleTotalInfoService {

    @Autowired
    private MlSettleTotalInfoMapper mlSettleTotalInfoMapper;

    @Autowired
    private MlSettleBatchMapper mlSettleBatchMapper;

    @Autowired
    private MlSettleRetailMapper mlSettleRetailMapper;

    @Override
    public List<MlSettleTotalInfoDo> listByMonth(String month) {
        List<MlSettleTotalInfoDo> mlSettleTotalInfoDos = mlSettleTotalInfoMapper.listByMonth(month);
        return mlSettleTotalInfoDos;
    }

    @Override
    public List<TransactionRespVo> listJyTotal(String month, String displayName, String plantType) {
        List<TransactionRespVo> list = mlSettleTotalInfoMapper.listJyTotal(month, displayName, plantType);

        // 创建年度和月度统计对象
        TransactionRespVo yearTotal = calculateTotal(list, true, "年度合计");
        TransactionRespVo monthTotal = calculateTotal(list, false, "月度合计");

        // 创建总计对象（年度合计+月度合计）
        TransactionRespVo grandTotal = new TransactionRespVo();
        grandTotal.setDisplayName("总计");
        grandTotal.setJianEnergy(yearTotal.getJianEnergy() + monthTotal.getJianEnergy());
        grandTotal.setFengEnergy(yearTotal.getFengEnergy() + monthTotal.getFengEnergy());
        grandTotal.setPingEnergy(yearTotal.getPingEnergy() + monthTotal.getPingEnergy());
        grandTotal.setGuEnergy(yearTotal.getGuEnergy() + monthTotal.getGuEnergy());
        grandTotal.setTotalEnergy(yearTotal.getTotalEnergy() + monthTotal.getTotalEnergy());

        grandTotal.setJianEnergyPrice(yearTotal.getJianEnergyPrice() + monthTotal.getJianEnergyPrice());
        grandTotal.setFengEnergyPrice(yearTotal.getFengEnergyPrice() + monthTotal.getFengEnergyPrice());
        grandTotal.setPingEnergyPrice(yearTotal.getPingEnergyPrice() + monthTotal.getPingEnergyPrice());
        grandTotal.setGuEnergyPrice(yearTotal.getGuEnergyPrice() + monthTotal.getGuEnergyPrice());
        grandTotal.setTotalEnergyPrice(yearTotal.getTotalEnergyPrice() + monthTotal.getTotalEnergyPrice());

        // 添加统计结果到列表
        list.add(yearTotal);
        list.add(monthTotal);
        list.add(grandTotal);
        return list;
    }

    // 抽取统计计算逻辑到独立方法
    private TransactionRespVo calculateTotal(List<TransactionRespVo> list, boolean isYear, String displayName) {
        // 根据年度标志构建过滤条件
        Predicate<TransactionRespVo> filter = isYear ? vo -> vo.getDisplayName().contains("年度") : vo -> !vo.getDisplayName().contains("年度");

        TransactionRespVo total = new TransactionRespVo();
        total.setDisplayName(displayName);

        // 计算各项能源数据
        total.setJianEnergy(list.stream().filter(filter).mapToLong(TransactionRespVo::getJianEnergy).sum());
        total.setFengEnergy(list.stream().filter(filter).mapToLong(TransactionRespVo::getFengEnergy).sum());
        total.setPingEnergy(list.stream().filter(filter).mapToLong(TransactionRespVo::getPingEnergy).sum());
        total.setGuEnergy(list.stream().filter(filter).mapToLong(TransactionRespVo::getGuEnergy).sum());
        total.setTotalEnergy(list.stream().filter(filter).mapToLong(TransactionRespVo::getTotalEnergy).sum());

        // 计算各项能源价格
        total.setJianEnergyPrice(list.stream().filter(filter).mapToLong(TransactionRespVo::getJianEnergyPrice).sum());
        total.setFengEnergyPrice(list.stream().filter(filter).mapToLong(TransactionRespVo::getFengEnergyPrice).sum());
        total.setPingEnergyPrice(list.stream().filter(filter).mapToLong(TransactionRespVo::getPingEnergyPrice).sum());
        total.setGuEnergyPrice(list.stream().filter(filter).mapToLong(TransactionRespVo::getGuEnergyPrice).sum());
        total.setTotalEnergyPrice(list.stream().filter(filter).mapToLong(TransactionRespVo::getTotalEnergyPrice).sum());

        return total;
    }

    @Override
    public List<TransactionsBiasRespVo> listJyPcTotal(String year) {
        List<TransactionsBiasRespVo> transactionsBiasRespVos = mlSettleTotalInfoMapper.listJyPcTotal(year);
        TransactionsBiasRespVo totalRespVo = new TransactionsBiasRespVo();
        Double totalContractEnergy = 0D;
        Double totalGreenEnergy = 0D;
        Double totalSettleEnergy = 0.0;
        Double totalGdPrice = 0.0;
        String totalGdjjc = "";
        Double totalDlgdPrice = 0.0;
        String totalDlgdjjc = "";
        Double totalScPrice = 0.0;
        String totalScjjc = "";
        Double totalPcdl = 0.0;
        Double totalBkhdf = 0.0;
        Double totalPcdl3 = 0.0;
        String totalPcl = "";
        Double totalSdsr = 0.0;
        for (TransactionsBiasRespVo transactionsBiasRespVo : transactionsBiasRespVos) {
            totalContractEnergy += Double.valueOf(transactionsBiasRespVo.getContractEnergy());
            totalGreenEnergy += Double.valueOf(transactionsBiasRespVo.getGreenEnergy());
            totalSettleEnergy += Double.valueOf(transactionsBiasRespVo.getSettleEnergy());
            totalGdPrice += (Double.valueOf(transactionsBiasRespVo.getSettleEnergy()) * Double.valueOf(transactionsBiasRespVo.getGdjjc()));
            totalDlgdPrice += (Double.valueOf(transactionsBiasRespVo.getSettleEnergy()) * Double.valueOf(transactionsBiasRespVo.getGwdlgdjc()));
            totalScPrice += (Double.valueOf(transactionsBiasRespVo.getSettleEnergy()) * Double.valueOf(transactionsBiasRespVo.getScjjc()));
            totalPcdl += Math.abs(Double.valueOf(transactionsBiasRespVo.getPcdl()));
            totalPcdl3 += Double.valueOf(transactionsBiasRespVo.getPcdl3());
            totalSdsr += Double.valueOf(transactionsBiasRespVo.getSdsr());
            totalBkhdf += Double.valueOf(transactionsBiasRespVo.getBkhdf());
        }
        DecimalFormat df = new DecimalFormat("#.##");
        totalGdjjc = df.format(totalGdPrice / totalSettleEnergy);
        totalDlgdjjc = df.format(totalDlgdPrice / totalSettleEnergy);
        totalScjjc = df.format(totalScPrice / totalSettleEnergy);
        totalPcl = df.format((totalPcdl / totalContractEnergy) * 100);
        totalRespVo.setDateTime("合计");
        totalRespVo.setContractEnergy(String.valueOf(Math.round(totalContractEnergy)));
        totalRespVo.setGreenEnergy(String.valueOf(Math.round(totalGreenEnergy)));
        double roundedValue = Math.round(totalSettleEnergy * 100.0) / 100.0;  // 保留两位小数
        String formattedValue = new DecimalFormat("#.000").format(roundedValue);
        totalRespVo.setSettleEnergy(formattedValue);
        totalRespVo.setGdjjc(totalGdjjc);
        totalRespVo.setGwdlgdjc(totalDlgdjjc);
        totalRespVo.setScjjc(totalScjjc);
        totalRespVo.setPcdl(totalPcdl.toString());
        totalRespVo.setPcdl3(totalPcdl3.toString());
        totalRespVo.setPcl(totalPcl + "%");
        totalRespVo.setSdsr(df.format(totalSdsr));
        totalRespVo.setBkhdf(df.format(totalBkhdf));
        transactionsBiasRespVos.add(totalRespVo);
        return transactionsBiasRespVos;
    }

    @Override
    public List<Map> listJyType() {
        return mlSettleTotalInfoMapper.listJyType();
    }

    private static final String TOTAL_INFO_PATTERN = "([\\u4e00-\\u9fa5]+)([-]?\\d+\\.\\d{3})([-]?\\d+\\.\\d{3})([-]?\\d+\\.\\d{3})";
    private static final String SETTLE_ENERGY_PATTERN = "([\\u4e00-\\u9fa5]+)([-]?\\d+\\.\\d{3})([-]?\\d+\\.\\d{3})([-]?\\d+)";
    private static final String PDF_DATA_PATTERN = "^(\\d+)([\\u4e00-\\u9fa5（）\\-]+?)([-]?\\d+\\.\\d{3})([-]?\\d+\\.\\d{2})([-]?\\d+\\.\\d{2})$";
    private static final String PDF_SIMPLE_PATTERN = "(\\d+)([\\u4e00-\\u9fa5（）-]+)";

    @Override
    @Transactional
    public ServiceResult<Boolean> importJs2(MultipartFile multipartFile) {
        try (InputStream inputStream = multipartFile.getInputStream(); PDDocument document = PDDocument.load(inputStream)) {

            // 解析PDF文本
            String pdfText = extractPdfText(document);
            List<String> lines = new BufferedReader(new StringReader(pdfText)).lines().collect(Collectors.toList());
            // 处理结算总信息
            String dataTime = extractDataTime(lines.get(3));
            QueryWrapper<MlSettleTotalInfoDo> wrapper = new QueryWrapper<>();
            wrapper.eq("data_time", dataTime);
            MlSettleTotalInfoDo totalInfo = processTotalInfo(lines, dataTime);
            if (baseMapper.selectCount(wrapper) == 0) {
                baseMapper.insert(totalInfo);
            } else {
                totalInfo.setId(baseMapper.selectOne(wrapper).getId());
                baseMapper.updateById(totalInfo);
            }

            // 处理批次和零售信息
            return processSettlementData(lines, dataTime);
        } catch (Exception e) {
            log.error("PDF处理异常", e);
            return ServiceResult.success(false);
        }
    }

    private String extractPdfText(PDDocument document) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        return stripper.getText(document).replace(" ", "");
    }

    public static String extractDataTime(String title) {
        Pattern pattern = Pattern.compile("(\\d{4})年(\\d{1,2})月");
        Matcher matcher = pattern.matcher(title);
        if (matcher.find()) {
            String year = matcher.group(1);
            String month = matcher.group(2);
            // 如果月份小于10,前面补0
            if (month.length() == 1) {
                month = "0" + month;
            }
            return year + "-" + month;
        }
        return null; // 或者可以返回空字符串 ""，取决于你的需求
    }

    private MlSettleTotalInfoDo processTotalInfo(List<String> lines, String dataTime) {
        MlSettleTotalInfoDo totalInfo = new MlSettleTotalInfoDo();

        // 处理总量信息
        Pattern pattern = Pattern.compile(TOTAL_INFO_PATTERN);
        Matcher matcher = pattern.matcher(lines.get(8));
        if (matcher.find()) {
            totalInfo.setDataTime(dataTime);
            totalInfo.setActualEnergy(new BigDecimal(matcher.group(2)));
            totalInfo.setContractEnergy(new BigDecimal(matcher.group(3)));
            totalInfo.setOffsetEnergy(new BigDecimal(matcher.group(4)));
        }

        totalInfo.setSettleFee(new BigDecimal(lines.get(9)));

        // 处理结算能量
        Pattern settlePattern = Pattern.compile(SETTLE_ENERGY_PATTERN);
        Matcher settleMatcher = settlePattern.matcher(lines.get(10));
        if (settleMatcher.find()) {
            totalInfo.setSettleEnergy(new BigDecimal(settleMatcher.group(2)));
        }

        return totalInfo;
    }

    private ServiceResult<Boolean> processSettlementData(List<String> lines, String dataTime) {
        // 获取索引
        int[] indexes = findDataIndexes(lines);

        // 处理批次和零售数据
        List<String> batchList = lines.subList(indexes[0], indexes[1]);
        List<String> retailList = lines.subList(indexes[2], indexes[3]);

        // 批量插入数据
        QueryWrapper<MlSettleBatchDo> mlSettleBatchDoQueryWrapper = new QueryWrapper<>();
        mlSettleBatchDoQueryWrapper.eq("data_time", dataTime);
        if (mlSettleBatchMapper.selectCount(mlSettleBatchDoQueryWrapper) > 0) {
            return ServiceResult.success(false);
        }
        mlSettleBatchMapper.insertBatch(processBatchData(batchList, dataTime));
        mlSettleRetailMapper.insertBatch(processRetailData(retailList, dataTime));
        return ServiceResult.success(true);
    }

    private int[] findDataIndexes(List<String> lines) {
        // 返回数组：[batchStart, batchEnd, retailStart, retailEnd]
        int[] indexes = new int[]{-1, -1, -1, -1};
        ArrayList<Integer> endIndex = new ArrayList<>();

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            if (line.startsWith("小计")) {
                endIndex.add(i);
            }
            if (line.equals("购电侧")) {
                indexes[0] = i + 1; // batchStart
            }
            if (line.equals("售电侧")) {
                indexes[2] = i + 1; // retailStart
            }
        }

        indexes[1] = endIndex.get(0); // batchEnd
        indexes[3] = endIndex.get(1); // retailEnd
        return indexes;
    }

    private List<MlSettleBatchDo> processBatchData(List<String> batchList, String dataTime) {
        List<MlSettleBatchDo> result = new ArrayList<>();
        for (String batchItem : batchList) {
            List<String> batch = extractTextFromPdf(batchItem);
            MlSettleBatchDo batchDo = new MlSettleBatchDo();
            batchDo.setDataTime(dataTime);
            batchDo.setSettleSubjectNo(batch.get(0));
            batchDo.setSettleSubjectName(batch.get(1));
            batchDo.setTradePlanEnergy(BigDecimal.ZERO);
            batchDo.setOrgNo("4300");
            batchDo.setSettleEnergy(new BigDecimal(batch.get(2)));
            batchDo.setSettlePrice(new BigDecimal(batch.get(3)));
            batchDo.setSettleFee(new BigDecimal(batch.get(4)));
            result.add(batchDo);
        }
        return result;
    }

    private List<MlSettleRetailDo> processRetailData(List<String> retailList, String dataTime) {
        List<MlSettleRetailDo> result = new ArrayList<>();
        for (String retailItem : retailList) {
            List<String> retail = extractTextFromPdf(retailItem);
            MlSettleRetailDo retailDo = new MlSettleRetailDo();
            retailDo.setDataTime(dataTime);
            retailDo.setSettleSubjectNo(retail.get(0));
            retailDo.setOrgNo("4300");
            retailDo.setSettleSubjectName(retail.get(1));
            retailDo.setTradePlanEnergy(BigDecimal.ZERO);
            retailDo.setSettleEnergy(new BigDecimal(retail.get(2)));
            retailDo.setSettlePrice(new BigDecimal(retail.get(3)));
            retailDo.setSettleFee(new BigDecimal(retail.get(4)));
            result.add(retailDo);
        }
        return result;
    }

    public static List<String> extractTextFromPdf(String text) {
        ArrayList<String> result = new ArrayList<>();

        Pattern fullPattern = Pattern.compile(PDF_DATA_PATTERN);
        Pattern simplePattern = Pattern.compile(PDF_SIMPLE_PATTERN);

        Matcher fullMatcher = fullPattern.matcher(text);
        Matcher simpleMatcher = simplePattern.matcher(text);

        if (fullMatcher.find()) {
            result.add(fullMatcher.group(1));
            result.add(fullMatcher.group(2));
            result.add(fullMatcher.group(3));
            result.add(fullMatcher.group(4));
            result.add(fullMatcher.group(5));
        } else if (simpleMatcher.find()) {
            result.add(simpleMatcher.group(1));
            result.add(simpleMatcher.group(2));
            result.add("0.000");
            result.add("0.00");
            result.add("0.00");
        }

        return result;
    }
}