package com.bobandata.cloud.trade.ml.term.dal.mysql.settle;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_batch(中长期交易：批发侧结算单)】的数据库操作Mapper
 * @createDate 2024-03-19 09:43:56
 * @Entity com.bobandata.cloud.trade.ml.term.domain.MlSettleBatch
 */
public interface MlSettleBatchMapper extends BaseCrudMapper<MlSettleBatchDo> {

}




