package com.bobandata.cloud.trade.ml.term.service.charge;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeStationsDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeStationsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【charge_stations(充电站信息表)】的数据库操作Service实现
 * @createDate 2024-11-04 14:20:45
 */
@Service
public class ChargeStationsServiceImpl extends ServiceImpl<ChargeStationsMapper, ChargeStationsDo> implements ChargeStationsService {

}




