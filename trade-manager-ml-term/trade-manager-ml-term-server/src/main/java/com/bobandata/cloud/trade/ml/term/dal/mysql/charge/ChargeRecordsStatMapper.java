package com.bobandata.cloud.trade.ml.term.dal.mysql.charge;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsStatDo;
import com.bobandata.cloud.trade.ml.term.service.charge.dto.ChargeStationDto;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description 针对表【charge_records_stat(充电桩电量数据汇总)】的数据库操作Mapper
 * @createDate 2024-11-04 14:09:33
 * @Entity com.bobandata.cloud.trade.ml.term.domain.ChargeRecordsStat
 */
public interface ChargeRecordsStatMapper extends BaseCrudMapper<ChargeRecordsStatDo> {

    @Select("select area_detail_name as area_name, count(1) as station_num, round(sum(ifnull(device_total_power,0) / 1000),3) as sum_power, sum(pile_sum) as sum_pile\n" +
            "from charge_stations\n" +
            "group by area_detail_name\n" +
            "order by count(1) desc , sum(pile_sum) desc\n" +
            "limit 10")
    List<KeyValueVo> selectStat();

    @Select("select sum(V1 + V2 + V3 + V4 + V5 + V6 + V7 + V8 + V9 + V10 + V11 + V15 + V16 + V17 + V18 + V19 + V20 + V21 + V22 + V23 + V24) / 1000 as month_energy\n" +
            "from charge_records_stat where year(data_date) = year(now())")
    BigDecimal selectYearEnergy();

    @Select("select sum(V1 + V2 + V3 + V4 + V5 + V6 + V7 + V8 + V9 + V10 + V11 + V15 + V16 + V17 + V18 + V19 + V20 + V21 + V22 + V23 + V24) / 1000 as month_energy\n" +
            "from charge_records_stat where month(data_date) = month(now())")
    BigDecimal selectMonthEnergy();

    @Select("select data_date,\n" +
            "       round(ifnull(case data_date\n" +
            "                        when month(data_date) in (1) then ifnull(sum(V19 + V20 + V21 + V22 + V23), 0)\n" +
            "                        when month(data_date) in (7, 8, 9, 12) then ifnull(sum(V19 + V20 + V21 + V22 + V23), 0)\n" +
            "                        else 0\n" +
            "                        end, 0) / 1000, 3)                                          as '尖峰电量',\n" +
            "       round(ifnull(case data_date\n" +
            "                        when month(data_date) in (1, 7, 8, 9, 12) then ifnull(sum(V12 + V13 + V14 + V23), 0)\n" +
            "                        when month(data_date) in (2, 3, 4, 5, 6, 10, 11)\n" +
            "                            then ifnull(sum(V12 + V13 + V14 + V19 + V20 + V21 + V22 + V23), 0)\n" +
            "                        else 0\n" +
            "                        end, 0) / 1000, 3)                                          as '高峰电量',\n" +
            "       round(ifnull(sum(V8 + V9 + V10 + V11 + V15 + V16 + V17 + V18), 0) / 1000, 3) as '平段电量',\n" +
            "       round(ifnull(sum(V1 + V2 + V3 + V4 + V5 + V6 + V7 + V24), 0) / 1000, 3)      as '谷段电量' \n" +
            "from charge_records_stat\n" +
            "where data_date >= #{startDate} and data_date <= #{endDate}\n" +
            "group by data_date")
    List<KeyValueVo> selectEnergy(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("select data_date,\n" +
            "       round(sum(V1)  / 1000,3) as v1,\n" +
            "       round(sum(V2)  / 1000,3) as v2,\n" +
            "       round(sum(V3)  / 1000,3) as v3,\n" +
            "       round(sum(V4)  / 1000,3) as v4,\n" +
            "       round(sum(V5)  / 1000,3) as v5,\n" +
            "       round(sum(V6)  / 1000,3) as v6,\n" +
            "       round(sum(V7)  / 1000,3) as v7,\n" +
            "       round(sum(V8)  / 1000,3) as v8,\n" +
            "       round(sum(V9)  / 1000,3) as v9,\n" +
            "       round(sum(V10) / 1000,3) as v10,\n" +
            "       round(sum(V11) / 1000,3) as v11,\n" +
            "       round(sum(V12) / 1000,3) as v12,\n" +
            "       round(sum(V13) / 1000,3) as v13,\n" +
            "       round(sum(V14) / 1000,3) as v14,\n" +
            "       round(sum(V15) / 1000,3) as v15,\n" +
            "       round(sum(V16) / 1000,3) as v16,\n" +
            "       round(sum(V17) / 1000,3) as v17,\n" +
            "       round(sum(V18) / 1000,3) as v18,\n" +
            "       round(sum(V19) / 1000,3) as v19,\n" +
            "       round(sum(V20) / 1000,3) as v20,\n" +
            "       round(sum(V21) / 1000,3) as v21,\n" +
            "       round(sum(V22) / 1000,3) as v22,\n" +
            "       round(sum(V23) / 1000,3) as v23,\n" +
            "       round(sum(V24) / 1000,3) as v24 \n" +
            "from charge_records_stat\n" +
            "where data_date = #{dataDate}\n" +
            "group by data_date")
    List<ChargeStationDto> selectDayEnergyCurve(@Param("dataDate") Date dataDate);
}




