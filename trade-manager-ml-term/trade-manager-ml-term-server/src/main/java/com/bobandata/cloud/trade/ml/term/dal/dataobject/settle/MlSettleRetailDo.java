package com.bobandata.cloud.trade.ml.term.dal.dataobject.settle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 中长期交易：零售侧结算单
 *
 * @TableName ml_settle_retail
 */
@TableName(value = "ml_settle_retail")
@Data
public class MlSettleRetailDo extends BaseDo {

    /**
     * 所属区域
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     * 结算科目
     */
    @TableField(value = "settle_subject_no")
    private String settleSubjectNo;

    /**
     * 结算科目编码
     */
    @TableField(value = "settle_subject_name")
    private String settleSubjectName;

    /**
     * 交易计划电量
     */
    @TableField(value = "trade_plan_energy")
    private BigDecimal tradePlanEnergy;

    /**
     * 结算电量
     */
    @TableField(value = "settle_energy")
    private BigDecimal settleEnergy;

    /**
     * 结算电价
     */
    @TableField(value = "settle_price")
    private BigDecimal settlePrice;

    /**
     * 结算费用
     */
    @TableField(value = "settle_fee")
    private BigDecimal settleFee;

    /**
     * 日期
     */
    @TableField(value = "data_time")
    private String dataTime;

}