package com.bobandata.cloud.trade.ml.term.service.ration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.ration.Hnjsc;
import com.bobandata.cloud.trade.ml.term.dal.mysql.ration.HnjscMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024-03-18 10:08:06
 */
@Service
public class HnjscServiceImpl extends BaseServiceImpl<HnjscMapper, Hnjsc> implements HnjscService {

    @Autowired
    HnjscMapper hnjscMapper;

    @Override
    public ServiceResult<Boolean> insert(Hnjsc hnjsc) {
        QueryWrapper<Hnjsc> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", hnjsc.getDate());
        wrapper.eq("data_type", hnjsc.getDataType());
        if (hnjscMapper.selectCount(wrapper) > 0) {
            return ServiceResult.error(4005, "已存在相同日期数据");
        }
        return ServiceResult.success(hnjscMapper.insert(hnjsc) > 0);
    }

    @Override
    public ServiceResult<Boolean> updateRation(Hnjsc hnjsc) {
        QueryWrapper<Hnjsc> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", hnjsc.getDate());
        wrapper.eq("data_type", hnjsc.getDataType());
        return ServiceResult.success(hnjscMapper.update(hnjsc, wrapper) > 0);
    }

    @Override
    public ServiceResult<Boolean> delete(Long id) {
        return ServiceResult.success(hnjscMapper.deleteById(id) > 0);
    }

    @Override
    public ServiceResult<List<Hnjsc>> listRation(String dateTime) {
        QueryWrapper<Hnjsc> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", dateTime);
        return ServiceResult.success(hnjscMapper.selectList(wrapper));
    }
}




