package com.bobandata.cloud.trade.ml.term.service.abandon;


import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.ResponseReqVo;
import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseDetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ResponseEvaluationDo;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/11/16 11:49
 * @Description
 */
public interface ResponseService {

    PagingResult<ResponseInfoDo> getResponseInfoPage(ResponseReqVo reqVo);

    PagingResult<ResponseDetailDo> getResponseDetailPage(ResponseReqVo reqVo);

}
