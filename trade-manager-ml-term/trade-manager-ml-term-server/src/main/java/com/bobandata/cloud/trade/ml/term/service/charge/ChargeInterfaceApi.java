package com.bobandata.cloud.trade.ml.term.service.charge;

import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeBillRespVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeIntefaceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStationReqVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.LoginUserReqVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.LoginUserRespVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "trade-manager-ml-term", url = "${thirdparty.interface.ipUrl}")
public interface ChargeInterfaceApi {

    /**
     * 登录获取token
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/api/system-service/user/login" ,produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    ResponseEntity<LoginUserRespVo> getAuthToken(@RequestBody LoginUserReqVo reqVo);

    /**
     * 获取充电站列表
     * @param reqVo
     * @param token
     * @return
     */
    @PostMapping(value = "/api/config-service/stationOptimal/pageList" , headers = "Authorization={token}",produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    ResponseEntity<ChargeIntefaceRespVo> getStationList(@RequestBody ChargeStationReqVo reqVo, @RequestHeader("Authorization") String token);

    /**
     * 获取充电桩列表
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/api/config-service/pile/pages" , headers = "Authorization={token}",produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    ResponseEntity<ChargeIntefaceRespVo> getPilesList(@RequestBody ChargeStationReqVo reqVo, @RequestHeader("Authorization") String token);

    /**
     * 获取账单列表(已结清订单列表)
     * @param reqVo
     * @return
     */
    @PostMapping(value = "/api/statistics-service/billDetailStatisticsController/page" , headers = "Authorization={token}",produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    ResponseEntity<ChargeBillRespVo> getBillList(@RequestBody ChargeStationReqVo reqVo, @RequestHeader("Authorization") String token);


}
