package com.bobandata.cloud.trade.ml.term.controller.othermix;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.curve.core.internal.DataCurveConvert;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.BalanceReqVo;
import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ConsDayEnergyReqVo;
import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ConsDayEnergyDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ProxyUserInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ResponseEvaluationDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.UserResponseDetailsDo;
import com.bobandata.cloud.trade.ml.term.service.othermix.BalanceComputeService;
import com.bobandata.cloud.trade.ml.term.service.othermix.ConsDayEnergyService;
import com.bobandata.cloud.trade.ml.term.service.othermix.ResponseEvaluationService;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:05
 * @Classname OtherMixController
 * @Description
 */
@Tag(name = "需求响应 - 用户需求等")
@RequestMapping("/othermix")
@RestController
public class OtherMixController {

    @Autowired
    private ResponseEvaluationService responseEvaluationService;

    @Autowired
    private ConsDayEnergyService consDayEnergyService;

    @Autowired
    private BalanceComputeService computeService;

    @Operation(summary = "需求响应-响应匹配触发计算")
    @PostMapping("/balance")
    @ResponseBody
    public ServiceResult<String> balance(@RequestBody BalanceReqVo reqVo) {
        List<BalanceReqVo.BalanceInfoNode> balanceInfoNodes = reqVo.getBalanceInfoNodes();
        for (final BalanceReqVo.BalanceInfoNode infoNode : balanceInfoNodes) {
            computeService.balance(reqVo.getBalDate(), TwentyFourPointTimeSegment.valueOf(infoNode.getTargetSegment()),
                                   infoNode.getTargetPower(),
                                   infoNode.getType(), reqVo.getConsNos()
            );
        }
        return ServiceResult.success();
    }

    @Operation(summary = "需求响应-用户需求响应评估")
    @GetMapping("/getResponseEvaluationPage")
    @ResponseBody
    public PagingResult<ResponseEvaluationDo> getResponseEvaluationPage(ResponseEvaluationReqVo reqVo) {
        return responseEvaluationService.getResponseEvaluationPage(reqVo);
    }

    @Operation(summary = "需求响应-用户响应补偿明细")
    @GetMapping("/getUserResponseDetailsPage")
    @ResponseBody
    public PagingResult<UserResponseDetailsDo> getUserResponseDetailsPage(ResponseEvaluationReqVo reqVo) {
        return responseEvaluationService.getUserResponseDetailsPage(reqVo);
    }

    @Operation(summary = "需求响应-代理用户信息（结算补偿页面）")
    @GetMapping("/getProxyUserInfoPage")
    @ResponseBody
    public PagingResult<ProxyUserInfoDo> getProxyUserInfoPage(ResponseEvaluationReqVo reqVo) {
        return responseEvaluationService.getProxyUserInfoPage(reqVo);
    }

    @ResponseBody
    @GetMapping(value = "/day/energy")
    @Description("需求响应-查询用户实际日电量")
    public List<List<KeyValueVo>> listDayEnergy(ConsDayEnergyReqVo reqVo) {
        List<ConsDayEnergyDo> consDayEnergyDoList = consDayEnergyService.selectList(reqVo);
        if (consDayEnergyDoList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, List<ConsDayEnergyDo>> listMap = consDayEnergyDoList.stream()
                                                                        .collect(Collectors.groupingBy(
                                                                                ConsDayEnergyDo::getConsNo));
        List<List<KeyValueVo>> keyValues = listMap.entrySet().stream().map(stringListEntry -> {
            List<ConsDayEnergyDo> value = stringListEntry.getValue();
            return DataCurveConvert.convertLabelValue(value.toArray(new ConsDayEnergyDo[0]), "name");

        }).collect(Collectors.toList());

        /*ConsDayEnergyDo[] dataCurves = consDayEnergyDoList.toArray(new ConsDayEnergyDo[0]);
        List<KeyValueVo> keyValues = DataCurveConvert.convertLabelValue(dataCurves, "name");*/
        return keyValues;
    }

}
