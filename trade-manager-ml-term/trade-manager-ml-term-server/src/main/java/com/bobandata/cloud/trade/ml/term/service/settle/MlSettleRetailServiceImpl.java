package com.bobandata.cloud.trade.ml.term.service.settle;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleRetailDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.settle.MlSettleRetailMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import java.time.YearMonth;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_retail(中长期交易：零售侧结算单)】的数据库操作Service实现
 * @createDate 2024-03-19 09:43:56
 */
@Service
public class MlSettleRetailServiceImpl extends BaseServiceImpl<MlSettleRetailMapper, MlSettleRetailDo> implements MlSettleRetailService {

    @Override
    public List<MlSettleRetailDo> listByMonth(String month) {
        QueryWrapper<MlSettleRetailDo> wrapper = new QueryWrapper<>();
        wrapper.eq("data_time", month);
        return this.list(wrapper);
    }
}




