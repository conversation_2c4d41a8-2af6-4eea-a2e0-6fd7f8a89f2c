package com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 响应信息
 *
 * <AUTHOR>
 * @since 2024-11-16
 */
@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("m_response_info")
public class ResponseInfoDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 户号
     */
    @TableField("cons_no")
    private String consNo;

    /**
     * 户名
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 响应时间，如 2024-05-30
     */
    @TableField("bal_date")
    private Date balDate;

    /**
     * 响应时段
     */
    @TableField("response_period")
    private String responsePeriod;

    /**
     * 响应类型
     */
    @TableField("response_type")
    private String responseType;

    /**
     * 调节负荷
     */
    @TableField("adjust_load")
    private BigDecimal adjustLoad;

    /**
     * 重合率
     */
    @TableField("coincidence_rate")
    private BigDecimal coincidenceRate;

    /**
     * 上调优先级
     */
    @TableField("raise_priority")
    private BigDecimal raisePriority;

    /**
     * 下调优先级
     */
    @TableField("lower_priority")
    private BigDecimal lowerPriority;

    /**
     * 上调节能力
     */
    @TableField("up_regulation")
    private BigDecimal upRegulation;

    /**
     * 下调节能力
     */
    @TableField("down_regulation")
    private BigDecimal downRegulation;

    /**
     * 用户意愿
     */
    @TableField("user_intention")
    private String userIntention;

    @TableField("just_rate")
    private String justRate;

    /**
     * 最大负荷
     */
    @TableField("max_load")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @TableField("min_load")
    private BigDecimal minLoad;
}
