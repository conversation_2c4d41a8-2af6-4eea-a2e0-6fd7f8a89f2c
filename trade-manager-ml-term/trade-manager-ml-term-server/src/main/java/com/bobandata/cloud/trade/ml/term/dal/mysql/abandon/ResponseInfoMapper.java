package com.bobandata.cloud.trade.ml.term.dal.mysql.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.ResponseReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseInfoDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 11:56
 * @Description
 */
public interface ResponseInfoMapper extends BaseCrudMapper<ResponseInfoDo> {
    default PagingResult<ResponseInfoDo> selectPage(ResponseReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<ResponseInfoDo>()
            .likeIfPresent(ResponseInfoDo::getConsNo, reqVO.getConsNo())
            .likeIfPresent(ResponseInfoDo::getConsName, reqVO.getConsName())
            .eqIfPresent(ResponseInfoDo::getBalDate, reqVO.getBalDate())
            .orderByDesc(ResponseInfoDo::getBalDate)
            .orderByAsc(ResponseInfoDo::getConsName));
    }
}
