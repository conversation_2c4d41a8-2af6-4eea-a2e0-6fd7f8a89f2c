package com.bobandata.cloud.trade.ml.term.service.contract;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractEnergyDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractEnergyMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_energy_test(中长期交易：交易合同拆分信息)】的数据库操作Service
 * @createDate 2024-03-12 15:35:24
 */
public interface MlTermContractEnergyService extends BaseService<MlTermContractEnergyMapper, MlTermContractEnergyDo> {

}
