package com.bobandata.cloud.trade.ml.term.service.photovoltaic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.controller.photovoltaic.vo.PvStatDataVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.photovoltaic.PvStationInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic.PvInverterDataMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.photovoltaic.PvStationInfoMapper;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/1 11:08
 * @Classname ChargeStationInfoServiceImpl
 * @Description
 */
@Service
public class PvStationInfoServiceImpl extends ServiceImpl<PvStationInfoMapper, PvStationInfoDo> implements PvStationInfoService {

    @Autowired
    private PvInverterDataMapper inverterDataMapper;

    @Resource
    private PvStationInfoMapper chargeStationInfoMapper;

    @Override
    public PvStatDataVo statPvData(@Param("startDate") Date startDate, @Param("endDate") Date endDate) {
        List<PvStationInfoDo> infoDos = this.list();
        List<PvStationInfoDo> filterInfoDos = infoDos.stream()
                                                     .filter(pvStationInfoDo -> pvStationInfoDo.getCapatity() != null)
                                                     .collect(Collectors.toList());
        BigDecimal sumCap = filterInfoDos.stream()
                                         .map(PvStationInfoDo::getCapatity)
                                         .reduce(BigDecimal::add)
                                         .orElse(BigDecimal.ZERO)
                                         .divide(new BigDecimal("1000000"), 3, RoundingMode.HALF_UP);
        List<KeyValueVo> keyValueVos = chargeStationInfoMapper.selectCapGroup();
        List<KeyValueVo> dayGroupEnergy = inverterDataMapper.selectEnergyGroupDate(startDate, endDate);
        BigDecimal monthSumEnergy = inverterDataMapper.selectSumMonthEnergy();
        BigDecimal yearSumEnergy = inverterDataMapper.selectSumYearEnergy();
        return PvStatDataVo.builder()
                           .sumCap(sumCap)
                           .stationSize(infoDos.size())
                           .monthSumEnergy(monthSumEnergy)
                           .yearSumEnergy(yearSumEnergy)
                           .capData(keyValueVos)
                           .dayGroupEnergy(dayGroupEnergy)
                           .build();
    }

    @Override
    public void insertOrUpdBatch(List<PvStationInfoDo> stationInfoDos) {
        ArrayList<PvStationInfoDo> addList = new ArrayList<>(stationInfoDos.size());
        ArrayList<PvStationInfoDo> updList = new ArrayList<>(stationInfoDos.size());
        for (PvStationInfoDo stationInfoDo : stationInfoDos) {
            QueryWrapper<PvStationInfoDo> wrapper = new QueryWrapper<>();
            wrapper.eq("station_id", stationInfoDo.getStationId());
            wrapper.eq("user_id", stationInfoDo.getUserId());
            PvStationInfoDo existDayEnergyDo = chargeStationInfoMapper.selectOne(wrapper);
            if (null != existDayEnergyDo) {
                stationInfoDo.setId(existDayEnergyDo.getId());
                updList.add(stationInfoDo);
            } else {
                addList.add(stationInfoDo);
            }
        }
        if (!CollectionUtils.isEmpty(updList)) {
            chargeStationInfoMapper.updateBatch(updList, updList.size());
        }
        chargeStationInfoMapper.insertBatch(addList);
    }
}
