package com.bobandata.cloud.trade.ml.term.controller.contract.vo;

import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-03-19日 09:27
 * @description
 */
@Schema(description = "中长期管理-电量预测")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BatteryDivinerRespVo {

    @Schema(description = "预测尖峰电量")
    private BigDecimal predictionHighestEnergy;

    @Schema(description = "预测高峰电量")
    private BigDecimal predictionPeakEnergy;

    @Schema(description = "预测平段电量")
    private BigDecimal predictionFlatEnergy;
    
    @Schema(description = "预测谷段电量")
    private BigDecimal predictionValleyEnergy;

    @Schema(description = "预测总计")
    private BigDecimal predictionTotal;

    @Schema(description = "补充尖峰电量")
    private BigDecimal supplementHighestEnergy;

    @Schema(description = "补充高峰电量")
    private BigDecimal supplementPeakEnergy;

    @Schema(description = "补充平段电量")
    private BigDecimal supplementFlatEnergy;

    @Schema(description = "补充谷段电量")
    private BigDecimal supplementValleyEnergy;

    @Schema(description = "补充总计")
    private BigDecimal supplementTotal;


}
