package com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 *内部响应邀约表
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName("m_abandon_inner_response")
public class AbandonInnerResponseDo extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;
    /**
     * 工单编号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 工单名称
     */
    @TableField("trade_name")
    private String tradeName;

    /**
     * 用户户号
     */
    @TableField("cons_no")
    private String consNo;

    /**
     * 用户户名
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 执行日期
     */
    @TableField("run_date")
    private Date runDate;

    /**
     * 调节时段
     */
    @TableField("adjust_time_segment")
    private String adjustTimeSegment;

    /**
     * 调节负荷(万kW)
     */
    @TableField("adjust_load")
    private BigDecimal adjustLoad;

    /**
     * 反馈状态 0待反馈 1已反馈
     */
    @TableField("feed_status")
    private Integer feedStatus;

    /**
     * 反馈时间
     */
    @TableField("feedback_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp feedbackTime;

    /**
     * 合同容量(万kW)
     */
    @TableField("contract_cap")
    private BigDecimal contractCap;


    /**
     * 聚合容量(聚合容量是运行容量的10%)
     */
    @TableField("run_cap")
    private BigDecimal runCap;


}
