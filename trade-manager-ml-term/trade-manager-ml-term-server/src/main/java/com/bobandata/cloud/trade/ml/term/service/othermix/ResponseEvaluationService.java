package com.bobandata.cloud.trade.ml.term.service.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ProxyUserInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ResponseEvaluationDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.UserResponseDetailsDo;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:18
 * @Classname ResponseEvaluationService
 * @Description
 */
public interface ResponseEvaluationService {

    PagingResult<ResponseEvaluationDo> getResponseEvaluationPage(ResponseEvaluationReqVo reqVo);

    PagingResult<UserResponseDetailsDo> getUserResponseDetailsPage(ResponseEvaluationReqVo reqVo);

    PagingResult<ProxyUserInfoDo> getProxyUserInfoPage(ResponseEvaluationReqVo reqVo);
}
