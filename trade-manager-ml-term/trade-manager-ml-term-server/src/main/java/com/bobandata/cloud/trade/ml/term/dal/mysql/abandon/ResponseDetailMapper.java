package com.bobandata.cloud.trade.ml.term.dal.mysql.abandon;

import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.ResponseReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseDetailDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 11:56
 * @Description
 */
public interface ResponseDetailMapper extends BaseCrudMapper<ResponseDetailDo> {

    default PagingResult<ResponseDetailDo> selectPage(ResponseReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<ResponseDetailDo>()
                .likeIfPresent(ResponseDetailDo::getConsNo, reqVO.getConsNo())
                .likeIfPresent(ResponseDetailDo::getConsName, reqVO.getConsName())
                .eqIfPresent(ResponseDetailDo::getBalDate, reqVO.getBalDate())
                .orderByDesc(ResponseDetailDo::getBalDate)
                .orderByAsc(ResponseDetailDo::getConsName));
    }
}
