package com.bobandata.cloud.trade.ml.term.service.othermix;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseDetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.ResponseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.ResponseDetailMapper;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.ResponseInfoMapper;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.google.common.collect.Range;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-11-18日 09:00
 * @description
 */
@Component
public class BalanceComputeService {

    private static final String PEAK_TYPE = "削峰";

    private static final String FILL_TYPE = "填谷";

    @Autowired
    private ResponseDetailMapper detailMapper;

    @Autowired
    private ResponseInfoMapper responseInfoMapper;

    @Transactional
    public void balance(Date balDate,
                        TimeSegment targetSegment,
                        BigDecimal targetPower,
                        int type,
                        List<String> consNos) {
        LambdaQueryWrapperX<ResponseDetailDo> wrapperX = new LambdaQueryWrapperX<ResponseDetailDo>().eqIfPresent(
                ResponseDetailDo::getBalDate, balDate);
        if (CollectionUtil.isNotEmpty(consNos)) {
            wrapperX.in(ResponseDetailDo::getConsNo, consNos);
        }
        List<ResponseDetailDo> detailDos = detailMapper.selectList(wrapperX);
        detailDos.forEach(detailDo -> this.calSegmentScale(targetSegment, detailDo.getTimeSegment(type), detailDo));
        List<ResponseInfoDo> responseInfoDos;
        if (type == 1) {
            responseInfoDos = balancePeak(balDate, targetSegment, targetPower, detailDos);
        } else {
            responseInfoDos = balanceFill(balDate, targetSegment, targetPower, detailDos);
        }
        LambdaQueryWrapper<ResponseInfoDo> query = Wrappers.lambdaQuery(
                ResponseInfoDo.class);
        query.eq(ResponseInfoDo::getBalDate, balDate);
        responseInfoMapper.delete(query);
        responseInfoMapper.insertBatch(responseInfoDos);
    }

    private List<ResponseInfoDo> balancePeak(Date balDate,
                                             TimeSegment targetSegment,
                                             BigDecimal targetPower,
                                             List<ResponseDetailDo> detailDos) {
        detailDos = detailDos.stream()
                             .filter(new Predicate<ResponseDetailDo>() {
                                 @Override
                                 public boolean test(final ResponseDetailDo responseDetailDo) {
                                     return responseDetailDo.getSegmentScale().compareTo(BigDecimal.ZERO) > 0;
                                 }
                             })
                             .filter(ResponseDetailDo::getHaveDown)
                             .sorted(Comparator.comparing(ResponseDetailDo::getSegmentScale, Comparator.reverseOrder())
                                               .thenComparing(
                                                       ResponseDetailDo::getDownRegulation, Comparator.reverseOrder())
                                               .thenComparing(ResponseDetailDo::getShavingLevel)
                                               .thenComparing(ResponseDetailDo::getJustRate)
                                               .thenComparing(ResponseDetailDo::getUserIntention))
                             .collect(Collectors.toList());
        return balanceList(balDate, targetSegment, targetPower, detailDos,
                           responseInfoDo -> responseInfoDo.setResponseType(PEAK_TYPE),
                           ResponseDetailDo::getDownRegulation
        );
    }

    private List<ResponseInfoDo> balanceList(Date balDate,
                                             TimeSegment targetSegment,
                                             BigDecimal targetPower,
                                             List<ResponseDetailDo> detailDos,
                                             Consumer<ResponseInfoDo> setterResponseType,
                                             Function<ResponseDetailDo, BigDecimal> function) {
        List<ResponseInfoDo> responseInfoDos = new ArrayList<>();
        for (final ResponseDetailDo detailDo : detailDos) {
            BigDecimal justLoad = function.apply(detailDo);
            if (targetPower.compareTo(BigDecimal.ZERO) == 0) {
                justLoad = BigDecimal.ZERO;
            } else if (targetPower.compareTo(justLoad) <= 0) {
                justLoad = targetPower;
                targetPower = BigDecimal.ZERO;
            } else {
                targetPower = targetPower.subtract(justLoad);
            }
            ResponseInfoDo responseInfoDo = new ResponseInfoDo()
                    .setBalDate(balDate)
                    .setConsNo(detailDo.getConsNo())
                    .setConsName(detailDo.getConsName())
                    .setCoincidenceRate(detailDo.getSegmentScale())
                    .setAdjustLoad(justLoad)
                    .setJustRate(detailDo.getJustRate())
                    .setMinLoad(detailDo.getMinLoad())
                    .setMaxLoad(detailDo.getMaxLoad())
                    .setUserIntention(detailDo.getUserIntention())
                    .setResponsePeriod(detailDo.getEndPeriod())
                    .setRaisePriority(new BigDecimal(detailDo.getFillLevel()))
                    .setLowerPriority(new BigDecimal(detailDo.getShavingLevel()))
                    .setUpRegulation(detailDo.getUpRegulation())
                    .setDownRegulation(detailDo.getDownRegulation());
            setterResponseType.accept(responseInfoDo);
            responseInfoDos.add(responseInfoDo);
        }
        return responseInfoDos;
    }

    private List<ResponseInfoDo> balanceFill(Date balDate,
                                             TimeSegment targetSegment,
                                             BigDecimal targetPower,
                                             List<ResponseDetailDo> detailDos) {
        detailDos = detailDos.stream()
                             .filter(
                                     responseDetailDo -> responseDetailDo.getSegmentScale()
                                                                         .compareTo(BigDecimal.ZERO) > 0)
                             .filter(ResponseDetailDo::getHaveUp)
                             .sorted(Comparator.comparing(ResponseDetailDo::getSegmentScale, Comparator.reverseOrder())
                                               .thenComparing(
                                                       ResponseDetailDo::getUpRegulation, Comparator.reverseOrder())
                                               .thenComparing(ResponseDetailDo::getFillLevel)
                                               .thenComparing(ResponseDetailDo::getJustRate)
                                               .thenComparing(ResponseDetailDo::getUserIntention))
                             .collect(Collectors.toList());
        return balanceList(balDate, targetSegment, targetPower, detailDos,
                           responseInfoDo -> responseInfoDo.setResponseType(FILL_TYPE),
                           ResponseDetailDo::getUpRegulation
        );
    }

    private void calSegmentScale(TimeSegment denSegment, TimeSegment molSegment, ResponseDetailDo responseDetailDo) {
        BigDecimal bigDecimal = segmentScale(molSegment, denSegment);
        responseDetailDo.setSegmentScale(bigDecimal);
    }

    private BigDecimal segmentScale(TimeSegment molSegment, TimeSegment denSegment) {
        Range<Integer> molRange = molSegment.getPointsRange();
        Range<Integer> denRange = denSegment.getPointsRange();
        Range<Integer> intersection;
        try {
            intersection = molRange.intersection(denRange);
        } catch (IllegalArgumentException e) {
            return BigDecimal.ZERO;
        }
        Integer lowerEndpoint = intersection.lowerEndpoint();
        Integer upperEndpoint = intersection.upperEndpoint();
        int len = denSegment.getEndPoint() - denSegment.getStartPoint();
        double per = (double) (upperEndpoint - lowerEndpoint) / len;
        return new BigDecimal(String.valueOf(per)).setScale(4, RoundingMode.HALF_UP);
    }
}
