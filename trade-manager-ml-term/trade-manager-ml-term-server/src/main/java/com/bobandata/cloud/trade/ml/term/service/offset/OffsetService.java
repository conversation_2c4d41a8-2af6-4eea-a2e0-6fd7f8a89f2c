package com.bobandata.cloud.trade.ml.term.service.offset;

import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetReqVo;
import com.bobandata.cloud.trade.ml.term.controller.offset.vo.OffsetRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.offset.OffsetDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.offset.OffsetMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_settle_batch(中长期交易：批发侧结算单)】的数据库操作Service
 * @createDate 2024-03-19 09:43:56
 */
public interface OffsetService extends BaseService<OffsetMapper, OffsetDo> {

    List<OffsetRespVo> listDlTotal(OffsetReqVo offsetReqVo);
}
