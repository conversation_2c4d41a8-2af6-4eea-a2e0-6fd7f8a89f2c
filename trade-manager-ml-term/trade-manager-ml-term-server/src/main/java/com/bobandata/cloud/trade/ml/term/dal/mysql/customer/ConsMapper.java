package com.bobandata.cloud.trade.ml.term.dal.mysql.customer;

import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.Cons;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 */
public interface ConsMapper extends BaseCrudMapper<Cons> {

    @Select("select distinct trim(VOLT_CODE) level from corp_cons_hn_yxke where VOLT_CODE!= '' and VOLT_CODE is not null")
    List<Map> getLevel();

    @Select("select distinct trim(elec_type_bigcls) elecTypeBigcls from corp_cons_hn_yxke where elec_type_bigcls !='' and elec_type_bigcls is not null")
    List<Map> getYd();
}




