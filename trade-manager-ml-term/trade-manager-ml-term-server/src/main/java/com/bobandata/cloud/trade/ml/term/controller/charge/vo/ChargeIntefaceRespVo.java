package com.bobandata.cloud.trade.ml.term.controller.charge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/4 10:00
 * @Classname ChargeStationsReqVo
 * @Description
 */
@Schema(description = "充电接口RespVo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
public class ChargeIntefaceRespVo {

    private int code;

    private ChargeStationRespVo data;

    private String msg;

}
