package com.bobandata.cloud.trade.ml.term.dal.mysql.customer;

import com.bobandata.cloud.trade.ml.term.controller.customer.vo.PowerPlantReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.PowerPlant;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024-03-12 15:35:24
 * @Entity com.bobandata.cloud.trade.system.domain.MlTermContractEnergy
 */
public interface PowerPlantMapper extends BaseCrudMapper<PowerPlant> {
    default PagingResult<PowerPlant> selectPage(PowerPlantReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<PowerPlant>()
                .likeIfPresent(PowerPlant::getPowerPlantName, reqVO.getPowerPlantName())
                .eqIfPresent(PowerPlant::getPowerPlantType, reqVO.getPowerPlantType())
                .eqIfPresent(PowerPlant::getPowerGenerationGroup, reqVO.getPowerGenerationGroup())
        );
    }

    @Select("select distinct power_plant_type powerPlantType from power_plant_info where power_plant_type!='' and power_plant_type is not null")
    List<Map> getPlantType();

    @Select("select distinct power_generation_group powerGenerationGroup from power_plant_info where power_generation_group!='' and power_generation_group is not null")
    List<Map> getFdjt();
}




