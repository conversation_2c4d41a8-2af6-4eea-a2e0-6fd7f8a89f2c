package com.bobandata.cloud.trade.ml.term.dal.dataobject.charge;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * <p>
 * 充电桩信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charge_piles")
public class ChargePilesDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 充电桩编号
     */
    @JsonProperty(value = "pileNum")
    @TableField("pile_number")
    private String pileNumber;

    /**
     * 充电桩名称
     */
    @JsonProperty(value = "name")
    @TableField("pile_name")
    private String pileName;

    /**
     * 制造商/运营商
     */
    private String manufacturer;

    /**
     * 所属站点
     */
    @TableField("station_name")
    private String stationName;
    /**
     * 所属站点
     */
    @TableField("station_id")
    private Long stationId;

    /**
     * 所在区域
     */
    @TableField("area_name")
    private String areaName;

    /**
     * 经营模式
     */
    @TableField("business_mode")
    private String businessMode;

    /**
     * 桩电流模式
     */
    @TableField("pile_current_mode")
    private String pileCurrentMode;

    /**
     * 设备投运状态
     */
    @TableField("equipment_status")
    private String equipmentStatus;

    /**
     * 桩状态/桩在线状态
     */
    @TableField("pile_status")
    private String pileStatus;

    /**
     * 充电枪数量
     */
    @TableField("gun_count")
    private String gunCount;

    /**
     * 充电枪总功率（kW）
     */
    private BigDecimal power;

    /**
     * 桩品牌
     */
    @TableField("pile_brand")
    private String pileBrand;

    /**
     * 桩型号
     */
    @TableField("pile_model")
    private String pileModel;

    /**
     * 通讯协议
     */
    private Integer protocol;

    /**
     * 软件版本号
     */
    @TableField("software_ver")
    private String softwareVer;

    /**
     * 硬件版本号
     */
    @TableField("handware_ver")
    private String handwareVer;

    /**
     * 创建时间/登记时间
     */
    @TableField("create_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

    /**
     * 未找到--登记人
     */
    @TableField("create_name")
    private String createName;

    /**
     * 未找到-设备产权所属
     */
    @TableField("equipment_ownership")
    private String equipmentOwnership;

    /**
     * 更新时间/最后更新时间
     */
    @TableField("update_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;


}
