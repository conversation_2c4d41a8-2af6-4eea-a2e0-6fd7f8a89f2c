package com.bobandata.cloud.trade.ml.term.service.contract;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.BatteryDivinerRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractEnergyPriceRespVo;
import com.bobandata.cloud.trade.ml.term.controller.contract.vo.ContractInfoReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.contract.MlTermContractInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.contract.MlTermContractInfoMapper;
import com.bobandata.cloud.trade.ml.term.service.contract.dto.ContractDetailDto;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_info_test(中长期交易：交易合同基本信息)】的数据库操作Service
 * @createDate 2024-03-12 15:57:26
 */
public interface MlTermContractInfoService extends BaseService<MlTermContractInfoMapper, MlTermContractInfoDo> {

    List<ContractDetailDto> listDetailByTradeId(String tradeId);

    ContractEnergyPriceRespVo getContractEnergyPrice(ContractInfoReqVo contractInfoReqVo);

    List<MlTermContractInfoDo> listByMonth(ContractInfoReqVo contractInfoReqVo);
    
    ServiceResult<Boolean> declare(String time, String energy);
}
