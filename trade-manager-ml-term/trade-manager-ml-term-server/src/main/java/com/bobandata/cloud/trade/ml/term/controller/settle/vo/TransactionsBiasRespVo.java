package com.bobandata.cloud.trade.ml.term.controller.settle.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:02
 * @description
 */
@Schema(description = "交易情况表格")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TransactionsBiasRespVo {

    @Schema(description = "日期")
    private String dateTime;

    @Schema(description = "交易类型")
    private String contractEnergy;

    @Schema(description = "电厂类型")
    private String greenEnergy;

    @Schema(description = "电厂名称")
    private String settleEnergy;

    @Schema(description = "所属集团")
    private String gdjjc;

    @Schema(description = "电量-尖")
    private String gwdlgdjc;

    @Schema(description = "电量-峰")
    private String scjjc;

    @Schema(description = "电量-平")
    private String pcdl;

    @Schema(description = "电量-谷")
    private String pcdl3;

    @Schema(description = "电量-总")
    private String pcl;

    @Schema(description = "电价-尖")
    private String k1;

    @Schema(description = "电价-峰")
    private String k2;

    @Schema(description = "电价-平")
    private String zpckhdj;

    @Schema(description = "电价-谷")
    private String fpckhdj;

    @Schema(description = "电费-尖")
    private String bkhdf;

    @Schema(description = "售电收入")
    private String sdsr;


}
