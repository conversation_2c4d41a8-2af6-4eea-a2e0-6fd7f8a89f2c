package com.bobandata.cloud.trade.ml.term.convert;

import com.bobandata.cloud.trade.ml.term.controller.settle.vo.SettleBatchRetailRespVo;
import com.bobandata.cloud.trade.ml.term.controller.settle.vo.SettleTotalRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleBatchDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleRetailDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.settle.MlSettleTotalInfoDo;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-19日 11:06
 * @description
 */
public class SettleTotalConvert {

    public static final SettleTotalConvert INSTANCE = new SettleTotalConvert();

    public List<SettleTotalRespVo> convertTotalVo(List<MlSettleTotalInfoDo> totalInfoDos) {
        return BobanBeanUtils.convertList(totalInfoDos, SettleTotalRespVo.class);
    }

    public SettleBatchRetailRespVo convertRespVo(String yearMonth,
                                                 List<MlSettleBatchDo> batchDos,
                                                 List<MlSettleRetailDo> retailDos) {
        List<SettleBatchRetailRespVo.Detail> batchDetails = BobanBeanUtils.convertList(
                batchDos,
                SettleBatchRetailRespVo.Detail.class
        );
        List<SettleBatchRetailRespVo.Detail> retailDetails = BobanBeanUtils.convertList(
                retailDos,
                SettleBatchRetailRespVo.Detail.class
        );
        return new SettleBatchRetailRespVo()
                .setSettleMonth(yearMonth)
                .setBatchInfos(batchDetails)
                .setRetailInfos(retailDetails);
    }
}
