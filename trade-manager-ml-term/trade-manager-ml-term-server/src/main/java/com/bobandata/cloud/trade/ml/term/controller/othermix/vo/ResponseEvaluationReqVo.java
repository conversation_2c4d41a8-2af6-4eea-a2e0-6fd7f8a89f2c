package com.bobandata.cloud.trade.ml.term.controller.othermix.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:10
 * @Classname ResponseEvaluationReq
 * @Description
 */
@Schema(description = "需求响应-用户需求响应评估")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ResponseEvaluationReqVo extends PageParam {

    @Schema(description = "用户编号")
    private String consId;

    @Schema(description = "用户名称")
    private String consName;

    @Schema(description = "执行日期")
    private String dispatchDate;

    @Schema(description = "负荷集成商")
    private String integratorName;

    /*@Schema(description = "用户类型")
    private String userType;*/

}
