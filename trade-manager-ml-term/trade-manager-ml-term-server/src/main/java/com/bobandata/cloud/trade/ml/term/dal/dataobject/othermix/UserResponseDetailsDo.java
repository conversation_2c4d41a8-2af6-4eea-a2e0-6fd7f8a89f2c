package com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * <p>
 * 用户响应补偿明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@TableName("m_user_response_details")
public class UserResponseDetailsDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 市公司名称，如国网永州供电公司
     */
    @TableField("city_name")
    private String cityName;

    /**
     * 用户 ID
     */
    @TableField("cons_id")
    private String consId;

    /**
     * 用户名称，如湖南东安湘江科技股份有限公司
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 县公司名称，如国网东安县供电公司
     */
    @TableField("county_name")
    private String countyName;

    /**
     * 调度日期，如 2024-05-30
     */
    @TableField("dispatch_date")
    private Date dispatchDate;

    /**
     * 结束时间，如20:00
     */
    @TableField("end_period")
    private String endPeriod;

    /**
     * 集成商名称（可为 null）
     */
    @TableField("integrator_name")
    private String integratorName;

    /**
     * 是否反对标识，如 0
     */
    @TableField("is_objection")
    private String isObjection;

    /**
     * 是否反对描述，如否
     */
    @TableField("is_objection_dsc")
    private String isObjectionDsc;

    /**
     * 组织名称，如国网东安县供电公司
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 组织编号
     */
    private String orgno;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 比例（可为 null）
     */
    private BigDecimal proportion;

    /**
     * 重新计算负荷
     */
    @TableField("rcalculate_load")
    private BigDecimal rcalculateLoad;

    /**
     * 响应总量
     */
    @TableField("response_gross")
    private BigDecimal responseGross;

    /**
     * 实际响应负荷值
     */
    @TableField("sj_value")
    private BigDecimal sjValue;

    /**
     * 开始时间，如18:00
     */
    @TableField("start_period")
    private String startPeriod;

    /**
     * 补贴金额
     */
    private BigDecimal subsidy;

    /**
     * 补贴 JSC
     */
    @TableField("subsidy_jsc")
    private BigDecimal subsidyJsc;

    /**
     * 补贴 SJ
     */
    @TableField("subsidy_sj")
    private BigDecimal subsidySj;

    /**
     * 所属组织名称（可为空字符串）
     */
    @TableField("suo_org_name")
    private String suoOrgName;

    /**
     * 用户类型，如直接参与用户
     */
    @TableField("user_type")
    private String userType;

    /**
     * 用户账号，如 13974681600
     */
    @TableField("user_name")
    private String userName;

    /**
     * 响应有效性
     */
    @TableField("response_effectiveness")
    private String responseEffectiveness;
}
