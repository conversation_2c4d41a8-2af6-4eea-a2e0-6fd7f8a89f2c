package com.bobandata.cloud.trade.ml.term.controller.customer.vo;

import com.bobandata.cloud.trade.mvc.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2024-03-18日 10:24
 * @description
 */
@Schema(description = "")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ConsRespVo {

    @Schema(description = "用户户名")
    protected Long id;

    @Schema(description = "用户户名")
    protected String caption;
    

    @Schema(description = "用户户号")
    protected List<Map> children;

}
