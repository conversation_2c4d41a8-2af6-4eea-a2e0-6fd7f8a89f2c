package com.bobandata.cloud.trade.ml.term.service.charge;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.ml.term.controller.charge.vo.ChargeStatRespVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.charge.ChargeRecordsStatDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.charge.ChargeRecordsStatMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import java.sql.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【charge_records_stat(充电桩电量数据汇总)】的数据库操作Service
 * @createDate 2024-11-04 14:09:33
 */
public interface ChargeRecordsStatService extends BaseService<ChargeRecordsStatMapper, ChargeRecordsStatDo> {

    ChargeStatRespVo chargeStat(Date startDate, Date endDate);

    List<KeyValueVo> getStationCurve(Date dataDate);

    void dataEtl(List<ChargeRecordsDo> chargeRecordsDos);
}
