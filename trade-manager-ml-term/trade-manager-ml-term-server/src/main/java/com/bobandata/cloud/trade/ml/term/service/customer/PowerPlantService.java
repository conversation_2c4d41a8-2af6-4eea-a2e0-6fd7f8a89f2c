package com.bobandata.cloud.trade.ml.term.service.customer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.ml.term.controller.customer.vo.PowerPlantReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.customer.PowerPlant;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【ml_term_contract_flow(中长期交易：交易合同流水信息)】的数据库操作Service
 * @createDate 2024-03-18 10:08:06
 */
public interface PowerPlantService extends IService<PowerPlant> {


    PagingResult<PowerPlant> listPowerPlant(PowerPlantReqVo reqVo);


    ServiceResult<Boolean> insert(PowerPlant powerPlant);

    ServiceResult<Boolean> updatePowerPlant(PowerPlant powerPlant);

    ServiceResult<Boolean> delete(Long id);

    List<Map> getPlantType();

    List<Map> getFdjt();
}
