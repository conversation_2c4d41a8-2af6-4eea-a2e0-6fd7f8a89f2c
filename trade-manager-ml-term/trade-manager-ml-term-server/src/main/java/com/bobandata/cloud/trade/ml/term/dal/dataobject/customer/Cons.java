package com.bobandata.cloud.trade.ml.term.dal.dataobject.customer;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用电客户档案信息
 *
 * @TableName corp_cons
 */
@TableName(value = "corp_cons")
@Data
public class Cons extends BaseDo {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 用户户号
     */
    @TableField(value = "cons_no")
    private String consNo;

    /**
     * 用户户名
     */
    @TableField(value = "caption")
    private String caption;

    /**
     * 所属区域
     */
    @TableField(value = "org_no")
    private String orgNo;

    /**
     * 电压等级
     */
    @TableField(value = "VOLT_CODE")
    private String voltCode;

    /**
     * 用电类别 大工业用电 一般工商业用电 居民用电等
     */
    @TableField(value = "elec_type_bigcls")
    private String elecTypeBigcls;

    /**
     * 用电行业
     */
    @TableField(value = "indust_type_dsc")
    private String industTypeDsc;

    /**
     * 用电地址:用电客户的用电地址
     */
    @TableField(value = "ELEC_ADDR")
    private String elecAddr;

    /**
     * 合同容量:合同约定的本用户的容量
     */
    @TableField(value = "CONTRACT_CAP")
    private BigDecimal contractCap;

    /**
     * 运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量
     */
    @TableField(value = "RUN_CAP")
    private BigDecimal runCap;

    /**
     * 高耗能行业类别
     */
    @TableField(value = "HEC_INDUSTRY_CODE")
    private String hecIndustryCode;

    /**
     * 负荷性质:负荷的重要程度分类一类，二类，三类
     */
    @TableField(value = "LODE_ATTR_CODE")
    private String lodeAttrCode;

    /**
     * 联系人姓名
     */
    @TableField(value = "persion")
    private String persion;

    /**
     * 联系方式
     */
    @TableField(value = "phone")
    private String phone;

}