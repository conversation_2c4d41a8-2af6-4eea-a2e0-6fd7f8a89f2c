package com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * <p>
 * 代理用户信息（结算页面）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode(callSuper = true)
@TableName("m_settle_compensate_proxy_user")
public class ProxyUserInfoDo extends Model<ProxyUserInfoDo> {

    @TableId(type = IdType.AUTO, value = "id")
    private Long id;

    /**
     * 用户 ID
     */
    @TableField("cons_id")
    private Long consId;

    /**
     * 用户名称，如武冈市青云环保建材厂普通合伙
     */
    @TableField("cons_name")
    private String consName;

    /**
     * 执行日期，如 2021-10-25
     */
    @TableField("dispatch_date")
    private Date dispatchDate;

    /**
     * 开始时间，如 10:00
     */
    @TableField("start_time")
    private String startTime;

    /**
     * 结束时间，如 12:00
     */
    @TableField("end_time")
    private String endTime;

    /**
     * 实际响应 负荷值(kW)
     */
    @TableField("practical_load")
    private BigDecimal practicalLoad;

    /**
     * 负荷集成商 分成比例 默认15%
     */
    private BigDecimal proportion;

    /**
     * 响应价格(元/kW·次)
     */
    @TableField("quoted_price")
    private BigDecimal quotedPrice;

    /**
     * 应邀负荷(kW)
     */
    @TableField("rcalculate_load")
    private BigDecimal rcalculateLoad;

    /**
     * 有效响应 负荷值(kW)
     */
    @TableField("response_load")
    private BigDecimal responseLoad;

    /**
     * 当月补偿金额(元)
     */
    private BigDecimal subsidydy;

    /**
     * 补偿总金额(元)
     */
    private BigDecimal subsidy;

    /**
     * 负荷集成商 分成金额(元)---代理金额 补偿总金额*15%
     */
    private BigDecimal subsidyjcs;


}
