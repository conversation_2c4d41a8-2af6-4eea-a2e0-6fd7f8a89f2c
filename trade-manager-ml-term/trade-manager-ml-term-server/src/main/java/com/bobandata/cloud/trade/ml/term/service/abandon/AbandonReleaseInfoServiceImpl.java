package com.bobandata.cloud.trade.ml.term.service.abandon;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoReqVo;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.AbandonReleaseInfoRespVo;
import com.bobandata.cloud.trade.ml.term.controller.abandon.vo.SegmentNodeVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.abandon.AbandonReleaseInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.mysql.abandon.AbandonReleaseInfoMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseServiceImpl;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/10/28 14:02
 * @Classname AbandonReleaseInfoServiceImpl
 * @Description
 */
@Service
public class AbandonReleaseInfoServiceImpl extends BaseServiceImpl<AbandonReleaseInfoMapper, AbandonReleaseInfoDo> implements AbandonReleaseInfoService {

    @Override
    public PagingResult<AbandonReleaseInfoRespVo> getAbandonReleaseInfoPage(AbandonReleaseInfoReqVo reqVo) {
        PagingResult<AbandonReleaseInfoDo> pagingResult = this.getBaseMapper().selectPage(reqVo);
        List<AbandonReleaseInfoDo> releaseInfoDoList = pagingResult.getData();
        if (releaseInfoDoList.isEmpty()) {
            return PagingResult.empty();
        }
        Map<Date, List<AbandonReleaseInfoDo>> valuesMap = releaseInfoDoList.stream()
            .collect(Collectors.groupingBy(
                AbandonReleaseInfoDo::getRunDate));
        List<AbandonReleaseInfoRespVo> dayCurveVos = valuesMap.entrySet()
            .stream()
            .map(entry -> {
                Date key = entry.getKey();
                List<AbandonReleaseInfoDo> value = entry.getValue();
                AbandonReleaseInfoDo infoDo = value.get(0);
                List<SegmentNodeVo> segmentNodeVos = convertNode(value);
                return AbandonReleaseInfoRespVo.builder()
                    .tradeNo(infoDo.getTradeNo())
                    .runDate(key)
                    .tradeName(
                        infoDo.getTradeName())
                    .targetArea(infoDo.getTargetArea())
                    .endFeedbackTime(infoDo.getEndFeedbackTime())
                    .segmentNodeVoList(segmentNodeVos)
                    .build();
            })
            .sorted(Comparator.comparing(AbandonReleaseInfoRespVo::getRunDate)
                .reversed())
            .collect(Collectors.toList());
        PagingResult<AbandonReleaseInfoRespVo> voPagingResult = new PagingResult<AbandonReleaseInfoRespVo>();
        voPagingResult.setData(dayCurveVos);
        voPagingResult.setMsg(pagingResult.getMsg());
        voPagingResult.setCode(pagingResult.getCode());
        voPagingResult.setTotal(Long.valueOf(valuesMap.size()));
        voPagingResult.setPageNo(pagingResult.getPageNo());
        voPagingResult.setPageSize(pagingResult.getPageSize());
        return voPagingResult;
    }

    private List<SegmentNodeVo> convertNode(List<AbandonReleaseInfoDo> value) {
        return value.stream()
            .map(marketAbandonInfo -> {
                return SegmentNodeVo.builder()
                    .timeDesc(marketAbandonInfo.getTimeDesc())
                    .startTimeSegment(marketAbandonInfo.getStartTimeSegment())
                    .endTimeSegment(marketAbandonInfo.getEndTimeSegment())
                    .forecastLoadGap(marketAbandonInfo.getForecastLoadGap())
                    .targetValue(marketAbandonInfo.getTargetValue())
                    .build();
            }).collect(Collectors.toList());
    }
}
