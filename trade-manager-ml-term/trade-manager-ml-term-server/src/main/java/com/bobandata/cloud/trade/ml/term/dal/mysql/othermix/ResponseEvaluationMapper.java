package com.bobandata.cloud.trade.ml.term.dal.mysql.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ResponseEvaluationDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:02
 * @Classname ResponseEvaluationMapper
 * @Description
 */
public interface ResponseEvaluationMapper extends BaseCrudMapper<ResponseEvaluationDo> {

    default PagingResult<ResponseEvaluationDo> selectPage(ResponseEvaluationReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<ResponseEvaluationDo>()
            .likeIfPresent(ResponseEvaluationDo::getConsId, reqVO.getConsId())
            .likeIfPresent(ResponseEvaluationDo::getConsName, reqVO.getConsName())
            .eqIfPresent(ResponseEvaluationDo::getDispatchDate, reqVO.getDispatchDate())
            .orderByDesc(ResponseEvaluationDo::getDispatchDate));
    }
}
