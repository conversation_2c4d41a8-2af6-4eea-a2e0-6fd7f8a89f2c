package com.bobandata.cloud.trade.ml.term.dal.mysql.othermix;

import com.bobandata.cloud.trade.ml.term.controller.othermix.vo.ResponseEvaluationReqVo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ProxyUserInfoDo;
import com.bobandata.cloud.trade.ml.term.dal.dataobject.othermix.ProxyUserInfoDo;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;

/**
 * <AUTHOR>
 * @Date 2024/10/28 17:01
 * @Classname ProxyUserInfoMapper
 * @Description
 */
public interface ProxyUserInfoMapper extends BaseCrudMapper<ProxyUserInfoDo> {

    default PagingResult<ProxyUserInfoDo> selectPage(ResponseEvaluationReqVo reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<ProxyUserInfoDo>()
            .likeIfPresent(ProxyUserInfoDo::getConsId, reqVO.getConsId())
            .likeIfPresent(ProxyUserInfoDo::getConsName, reqVO.getConsName())
            .eqIfPresent(ProxyUserInfoDo::getDispatchDate, reqVO.getDispatchDate())
            .orderByDesc(ProxyUserInfoDo::getDispatchDate));
    }
}
