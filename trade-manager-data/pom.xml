<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trade-manager-develop</artifactId>
        <groupId>com.bobandata.cloud</groupId>
        <version>${re.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trade-manager-data</artifactId>
    <name>trade : manager :: data</name>

    <dependencies>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-framework-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-mvc</artifactId>
        </dependency>


        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-mybatisplus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!-- 配置gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
            <version>2.19.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.14</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.14</version>
        </dependency>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-flink</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-curve</artifactId>
        </dependency>
        <dependency>
            <groupId>net.lightbody.bmp</groupId>
            <artifactId>browsermob-core</artifactId>
            <version>2.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.0.1-jre</version>
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>3.141.59</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.opencv</groupId>-->
<!--            <artifactId>opencv</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.opencv</groupId>
            <artifactId>opencv</artifactId>
            <version>3.4.15</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.3</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
<!--                        <configuration>-->
<!--                            <attach>false</attach>-->
<!--                        </configuration>-->
                    </execution>
                </executions>
                <configuration>
                    <addResources>true</addResources>
                    <mainClass>
                        com.bobandata.cloud.trade.data.TradeDataApplication
                    </mainClass>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.tld</include>
                    <include>**/*.ttf</include>
                    <include>**/*.yaml</include>
                    <include>**/*.yml</include>
                    <include>**/*.exe</include>
                    <include>**/*.png</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

</project>