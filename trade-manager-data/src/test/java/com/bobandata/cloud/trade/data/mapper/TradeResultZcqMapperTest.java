package com.bobandata.cloud.trade.data.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.cookieInfo.LoginForSelenium;
import com.bobandata.cloud.trade.data.dto.TradeNoticeListHnMapper;
import com.bobandata.cloud.trade.data.entity.TradeNoticeListHnDo;
import com.bobandata.cloud.trade.data.service.impl.*;
import com.bobandata.cloud.trade.data.task.TradeDataTask;
import com.bobandata.cloud.trade.data.util.FlinkBaseUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;

@SpringBootTest
@RunWith(SpringRunner.class)

public class TradeResultZcqMapperTest {


    @Autowired
    private TradeSysServiceImp tradeSysServiceImp;

    @Autowired
    private TradeNoticeListHnMapper tradeNoticeListHnMapper;

    @Autowired
    LoginForSelenium loginForSelenium;

    @Autowired
    private TradeDataTask tradeDataTask;

    @Test
    public void testLogin() throws Exception {

        boolean login = loginForSelenium.login("HN_JY_XHGD01", "hngd@4321", "hunan");
        System.out.println("login = " + login);
    }

    @Test
    public void testTradeNotice() {
        ServiceResult hn = tradeSysServiceImp.getTradeNotice("hn", "", "");
        System.out.println("hn = " + hn);
    }

    @Test
    public void testZCQ(){

        tradeDataTask.task();

    }

    @Test
    public void tesFDNLSB() {
        FlinkBaseUtil flinkBaseUtil = new FlinkBaseUtil();
        flinkBaseUtil.writeData("**********************************************************************************************************************", "root", "root");

    }

    @Test
    public void tesN() {

        String list = "[\n" +
                "            {\n" +
                "                \"tradeseqId\": \"PHZHN20240527A0004\",\n" +
                "                \"tradeseqCaption\": \"2024年5月月内市场合同转让双边交易（售电公司）\",\n" +
                "                \"beginDate\": \"2024-05-01T00:00:00.000+0800\",\n" +
                "                \"endDate\": \"2024-05-31T00:00:00.000+0800\",\n" +
                "                \"pubTime\": null,\n" +
                "                \"priority\": null,\n" +
                "                \"tradeChannel\": \"\",\n" +
                "                \"tradeComments\": \"无\",\n" +
                "                \"isPub\": \"1\",\n" +
                "                \"tradetypeId\": \"7913dbae-f16f-4a69-b469-d3bf36601863\",\n" +
                "                \"tradeGgId\": \"PHZHN-2024-05-0033\",\n" +
                "                \"tradeRole\": \"2\",\n" +
                "                \"targetEnergy\": null,\n" +
                "                \"tradetypeMode\": \"1\",\n" +
                "                \"isThreePart\": 0,\n" +
                "                \"subTradetypeCode\": \"130100\",\n" +
                "                \"yearMonth\": null,\n" +
                "                \"createTime\": null,\n" +
                "                \"tradeseqName\": \"2024年5月月内市场合同转让双边交易（售电公司）\",\n" +
                "                \"tradeTypeModo\": \"1\",\n" +
                "                \"declareState\": \"申报已结束\",\n" +
                "                \"tradeState\": \"已发布\",\n" +
                "                \"taradeTypeId\": \"7913dbae-f16f-4a69-b469-d3bf36601863\",\n" +
                "                \"declareRole\": \"2\",\n" +
                "                \"tradeTyppeName\": null,\n" +
                "                \"declareBeginDate\": \"2024-05-28T08:30:00.000+0800\",\n" +
                "                \"declareEndDate\": \"2024-05-28T14:45:00.000+0800\",\n" +
                "                \"restrainPubState\": null,\n" +
                "                \"norestrainPubState\": null,\n" +
                "                \"jyConfigs\": null,\n" +
                "                \"trFiles\": null,\n" +
                "                \"pubStatus\": null,\n" +
                "                \"declareType\": \"DAYPARTING_CURVE\",\n" +
                "                \"intentionTradeseq\": false,\n" +
                "                \"subTradetypeName\": null,\n" +
                "                \"dailyPart\": null,\n" +
                "                \"bandNum\": \"3\",\n" +
                "                \"pledgetemplateId\": \"n5b0eb6fa23ff49eca6f1d4f2d36fd9c4\",\n" +
                "                \"protocolId\": \"\",\n" +
                "                \"independent\": 2,\n" +
                "                \"bandBibidTradeseq\": null,\n" +
                "                \"isNewEnergy\": null,\n" +
                "                \"isSelectContract\": false,\n" +
                "                \"isCrossPlatform\": false,\n" +
                "                \"isCommitment\": true,\n" +
                "                \"parentTradetypeCode\": null,\n" +
                "                \"tradetypeModel\": null\n" +
                "            },\n" +
                "        ]";
        List<TradeNoticeListHnDo> tradeNoticeListHnDos = JSONObject.parseArray(list, TradeNoticeListHnDo.class);
        System.out.println("tradeNoticeListHnDos = " + tradeNoticeListHnDos);
        for (TradeNoticeListHnDo tradeNoticeListHnDo : tradeNoticeListHnDos) {
            String tradeseqId = tradeNoticeListHnDo.getTradeseqId();
            QueryWrapper<TradeNoticeListHnDo> noticeListWrapper = new QueryWrapper<>();
            noticeListWrapper.eq("tradeseq_id", tradeseqId);
            Long count = tradeNoticeListHnMapper.selectCount(noticeListWrapper);
            if (count <= 0) {
                tradeNoticeListHnMapper.insert(tradeNoticeListHnDo);
            }
        }

    }
}