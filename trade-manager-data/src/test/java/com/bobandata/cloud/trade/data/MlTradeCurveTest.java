package com.bobandata.cloud.trade.data;

import com.alibaba.fastjson.JSONObject;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.service.impl.TradeSysServiceImp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 多边交易曲线数据测试类
 * <AUTHOR>
 * @date 2024-11-05
 */
@Slf4j
@SpringBootTest
public class MlTradeCurveTest {

    @Autowired
    private TradeSysServiceImp tradeSysServiceImp;

    @Test
    public void testSaveMlTradeCurveData() {
        // 构造测试数据
        String testJsonData = "{\n" +
                "    \"code\": 0,\n" +
                "    \"msg\": \"操作成功\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"energyDecode\": \"157.098\",\n" +
                "            \"guid\": \"3eff76eb-3277-44e6-9d59-585bf605ada3\",\n" +
                "            \"updatetime\": \"2025-06-10T01:07:39.000+0000\",\n" +
                "            \"bidId\": \"b381d838-54a6-4cdd-9c63-2c093bc3da1e\",\n" +
                "            \"seq\": 1,\n" +
                "            \"energy\": \"9b5b53725b8b06f911842d7a745f80c56ea99b99cc1433315f153a95afead25b1f269911dedd607999664a2e70a4d8d4bfe57172cdbf178744fc4169d44585b9c4d8a04ea0fc1a1b556c57ebebf2a78f\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"energyDecode\": \"158.123\",\n" +
                "            \"guid\": \"da6fb824-8498-410f-ab52-60b70c1363d8\",\n" +
                "            \"updatetime\": \"2025-06-10T01:07:39.000+0000\",\n" +
                "            \"bidId\": \"b381d838-54a6-4cdd-9c63-2c093bc3da1e\",\n" +
                "            \"seq\": 2,\n" +
                "            \"energy\": \"9b5b53725b8b06f911842d7a745f80c56ea99b99cc1433315f153a95afead25b1f269911dedd607999664a2e70a4d8d4bfe57172cdbf178744fc4169d44585b9c4d8a04ea0fc1a1b556c57ebebf2a78f\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"energyDecode\": \"159.456\",\n" +
                "            \"guid\": \"test-guid-3\",\n" +
                "            \"updatetime\": \"2025-06-10T01:07:39.000+0000\",\n" +
                "            \"bidId\": \"b381d838-54a6-4cdd-9c63-2c093bc3da1e\",\n" +
                "            \"seq\": 3,\n" +
                "            \"energy\": \"test-energy-3\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        // 测试参数
        String unitId = "TEST_UNIT_001";
        String caseType = "1";
        String dataTime = "2024-11-05";
        String dataType = "SPOT_TRADE";

        // 调用服务方法
        ServiceResult result = tradeSysServiceImp.saveMlTradeCurveData(
                testJsonData, unitId, caseType, dataTime, dataType);

        // 验证结果
        log.info("测试结果: {}", JSONObject.toJSONString(result));
        
        // 断言
        assert result.isSuccess() : "保存数据应该成功";
        log.info("多边交易曲线数据保存测试通过");
    }

    @Test
    public void testSaveMlTradeCurveDataWithInvalidData() {
        // 测试无效数据
        String invalidJsonData = "{\n" +
                "    \"code\": 0,\n" +
                "    \"msg\": \"操作成功\",\n" +
                "    \"data\": []\n" +
                "}";

        String unitId = "TEST_UNIT_002";
        String caseType = "2";
        String dataTime = "2024-11-05";
        String dataType = "INVALID_TEST";

        ServiceResult result = tradeSysServiceImp.saveMlTradeCurveData(
                invalidJsonData, unitId, caseType, dataTime, dataType);

        log.info("无效数据测试结果: {}", JSONObject.toJSONString(result));
        
        // 验证应该返回错误
        assert !result.isSuccess() : "空数据应该返回失败";
        log.info("无效数据测试通过");
    }
}
