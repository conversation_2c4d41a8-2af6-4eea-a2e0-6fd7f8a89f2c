server:
  port: 20020
  servlet:
    context-path: /trade/data
spring:
  application:
    name: trade-manager-data
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://${DATABASE_HOST:*************}:${DATABASE_PORT:32372}/${DATABASE_DB:trade_sms}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PASSWORD:root}
    hikari:
      maximumPoolSize: 50
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
      driver-class-name: com.mysql.cj.jdbc.Driver

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

logging:
  config: classpath:logback.xml

mybatis-plus:
  mapper-locations:
    - classpath*:mapper/*.xml
  typeAliasesPackage: com.bobandata.cloud.**.dataobject
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
      sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
      meta-object-handler: com.bobandata.acquarium.core.server.handler.BaseEntityHandler
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

ribbon:
  ReadTimeout: 120000 # 请求处理的超时时间

dbUrl: **********************************************************************************************************************
dbUserName: root
dbPassWord: root




chromeUrl: ${CHROME_URL:http://192.168.2.123:31444}
#win64
chromeDriverUrl: ${CHROMEDRIVER_URL:D:\chromedriver-win32\chromedriver-win32\chromedriver.exe}

#linux64
#chromeDriverUrl: ${CHROMEDRIVER_URL:/test/chromedriverFile/chromedriver}

#win
tuPianUrl: ${TUPIAN_URL:D:\wxpc\WeChat Files\wxid_w0f2g6iaohag31\FileStorage\File\2025-07\jTessBoxEditorFX-2.6.0\jTessBoxEditorFX}

#linux
#tuPianUrl: ${TUPIAN_URL:/png/}

cron: ${CRON:0 0 21 * * ?}
#checkCron: 0/30 * * * * ?

rqspotcron: ${RQSPOTCRON:0 19 14 * * ?}

# 下载目录配置
downloadPath: ${DOWNLOAD_PATH:C:\Users\<USER>\Downloads}

