package com.bobandata.cloud.trade.data.util;

import okhttp3.*;
import okhttp3.Request.Builder;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: BYC
 * @Date: 2022/4/29 15:08:25
 * @description: http请求工具类
 **/
public class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    public static String httpPostUtil(String url, String bodyStr, Map<String, String> headersMap, int connectTimeout, int readTimeout, boolean isSSL) {
        OkHttpClient client = null;
        if (isSSL) {
            client = buildOKHttpClient().connectTimeout((long) connectTimeout, TimeUnit.SECONDS).readTimeout((long) readTimeout, TimeUnit.SECONDS).build();
        } else {
            client = (new OkHttpClient()).newBuilder().connectTimeout((long) connectTimeout, TimeUnit.SECONDS).readTimeout((long) readTimeout, TimeUnit.SECONDS).build();
        }

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, bodyStr);
        Request request = null;
        if (MapUtils.isNotEmpty(headersMap)) {
            Headers headers = setHeaders(headersMap);
            request = (new Builder()).url(url).method("POST", body).headers(headers).build();
        } else {
            request = (new Builder()).url(url).method("POST", body).build();
        }

        Response response = null;

        String var12;
        try {
//            log.info("resqustUrl:" + request.url().toString());
            response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.error("Method failed:" + response.code());
            }
//            log.info("Method success:" + response.code());
            String responseStr = response.body().string();
            var12 = responseStr;
        } catch (IOException var16) {
            log.error("get response failed: {}", var16);
            throw new RuntimeException(var16);
        } finally {
            if (response != null) {
                response.close();
            }

        }

        return var12;
    }

    public static String httpGetUtil(String url, Map<String, String> headersMap, int connectTimeout, int readTimeout, boolean isSSL) {
        OkHttpClient client = null;
        if (isSSL) {
            client = buildOKHttpClient().connectTimeout((long) connectTimeout, TimeUnit.SECONDS).readTimeout((long) readTimeout, TimeUnit.SECONDS).build();
        } else {
            client = (new OkHttpClient()).newBuilder().connectTimeout((long) connectTimeout, TimeUnit.SECONDS).readTimeout((long) readTimeout, TimeUnit.SECONDS).build();
        }

        Response response = null;
        Request request = null;
        if (headersMap != null && !headersMap.isEmpty()) {
            Headers headers = setHeaders(headersMap);
            request = (new Builder()).url(url).get().headers(headers).build();
        } else {
            request = (new Builder()).url(url).get().build();
        }

        String var9;
        try {
//            log.info("resqustUrl:" + request.url().toString());
            response = client.newCall(request).execute();

            String responseStr = response.body().string();
            var9 = responseStr;
        } catch (IOException var13) {
            log.error("get response failed: {}", var13);
            throw new RuntimeException(var13);
        } finally {
            if (response != null) {
                response.close();
            }

        }

        return var9;
    }

    public static OkHttpClient.Builder buildOKHttpClient() {
        try {
            TrustManager[] trustAllCerts = buildTrustManagers();
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init((KeyManager[]) null, trustAllCerts, new SecureRandom());
            SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier((hostname, session) -> {
                return true;
            });
            return builder;
        } catch (KeyManagementException | NoSuchAlgorithmException var4) {
            var4.printStackTrace();
            return new OkHttpClient.Builder();
        }
    }

    private static TrustManager[] buildTrustManagers() {
        return new TrustManager[]{new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }

            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        }};
    }

    private static Headers setHeaders(Map<String, String> headersParams) {
        Headers headers = null;
        Headers.Builder headersbuilder = new Headers.Builder();
        if (headersParams != null && !headersParams.isEmpty()) {
            Iterator<String> iterator = headersParams.keySet().iterator();
            String key = "";

            while (iterator.hasNext()) {
                key = ((String) iterator.next()).toString();
                headersbuilder.add(key, (String) headersParams.get(key));
            }
        }

        headers = headersbuilder.build();
        return headers;
    }
}
