package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-05-31 
 */
@Data
@TableName("corp_cons_hn")
public class CorpConsDo implements Serializable {

	private static final long serialVersionUID =  52288844225275817L;

	private String id;

	/**
	 * 用户编号
	 */
	private String consNo;

	/**
	 * 旧的用户编号
	 */
	private String oldAbbr;

	/**
	 * 交易单元id
	 */
	private String elementId;

	/**
	 * 用户侧此域指向可能的代理售电公司
	 */
	private String parentElementId;

	/**
	 * 交易单元名称
	 */
	private String elementName;

	/**
	 * 用户名称
	 */
	private String caption;

	/**
	 * 所属区域
	 */
	private String orgNo;

	/**
	 * 用户类型，现货还是非现货
	 */
	private String userType;

	/**
	 * 电压等级
	 */
	private String level;

	/**
	 * 所属行业
	 */
	private String industTypeDsc;

	/**
	 * 用电类别 大工业用电 一般工商业用电 居民用电等
	 */
	private String elecTypeBigcls;

	/**
	 * 产品类别
	 */
	private String productType;

	/**
	 * 所属地市
	 */
	private String city;

	/**
	 * 所属区县
	 */
	private String county;

	/**
	 * 用电地址:用电客户的用电地址
	 */
	private String elecAddr;

	/**
	 * 合同容量:合同约定的本用户的容量
	 */
	private BigDecimal contractCap;

	/**
	 * 运行容量:用电客户正在使用的合同容量，如暂停客户，在暂停期间其运行容量等于合同容量减去已暂停的容量
	 */
	private BigDecimal runCap;

	/**
	 * 高耗能行业类别
	 */
	private String hecIndustryCode;

	/**
	 * 负荷性质:负荷的重要程度分类一类，二类，三类
	 */
	private String lodeAttrCode;

	/**
	 * 联系人
	 */
	private String persion;

	/**
	 * 联系电话
	 */
	private String phone;

	/**
	 * 交易平台账号
	 */
	private String account;

	/**
	 * 交易平台密码
	 */
	private String pwd;

	/**
	 * 代理起始日期
	 */
	private LocalDateTime startTime;

	/**
	 * 代理结束日期
	 */
	private LocalDateTime endTime;

	/**
	 * 合同分成比例
	 */
	private Integer proportion;

	/**
	 * 交易平台账号
	 */
	private String creatorId;

	/**
	 * 代理起始日期
	 */
	private String createTime;

	/**
	 * 最后修改人
	 */
	private Integer lastModifierId;

	/**
	 * 刷新时间
	 */
	private LocalDateTime lastRefreshTime;

}
