package com.bobandata.cloud.trade.data.util;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class ExcelUtil {

//    public static void main(String[] args){
//        ExcelUtil excelUtil = new ExcelUtil();
//        //读取excel数据
//        List<Map<String,String>> result = excelUtil.readExcelToObj("C:\\Users\\<USER>\\Desktop\\新华\\价差\\价差\\2024年5月代理购电价格公告.xlsx");
//        for(Map<String,String> map:result){
//            System.out.println(map);
//        }
//
//    }


    /**
     * 读取excel数据
     *
     * @param
     */

    public static List<Map<String, String>> readExcel(InputStream file, int sheetIndex, int startReadLine, int tailLine) {
        Workbook wb = null;
        try {
            wb = WorkbookFactory.create(file);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Sheet sheet = wb.getSheetAt(sheetIndex);
        Row row = null;
        List<Map<String, String>> result = new ArrayList<>();
        for (int i = startReadLine; i <= tailLine; i++) {

            row = sheet.getRow(i);
            Map<String, String> map = new HashMap<>();
            for (Cell c : row) {
                String returnStr = "";

                boolean isMerge = isMergedRegion(sheet, i, c.getColumnIndex());
                //判断是否具有合并单元格
                if (isMerge) {
                    String rs = getMergedRegionValue(sheet, row.getRowNum(), c.getColumnIndex());
                    returnStr = rs;
                } else {
                    //设置单元格类型
                    c.setCellType(Cell.CELL_TYPE_STRING);
                    returnStr = c.getStringCellValue();
                }
                if (c.getColumnIndex() == 1) {
                    map.put("powerType", returnStr);
                } else if (c.getColumnIndex() == 2) {
                    map.put("voltageGrade", returnStr);
                } else if (c.getColumnIndex() == 3) {
                    map.put("dianduPrice", returnStr);
                } else if (c.getColumnIndex() == 4) {
                    map.put("onlinePrice", returnStr);
                } else if (c.getColumnIndex() == 5) {
                    map.put("lineLossPrice", returnStr);
                } else if (c.getColumnIndex() == 6) {
                    map.put("transportMatchPrice", returnStr);
                } else if (c.getColumnIndex() == 7) {
                    map.put("systemWorkPrice", returnStr);
                } else if (c.getColumnIndex() == 8) {
                    map.put("governmentFund", returnStr);
                } else if (c.getColumnIndex() == 9) {
                    map.put("peakPrice", returnStr);
                } else if (c.getColumnIndex() == 10) {
                    map.put("highestPrice", returnStr);
                } else if (c.getColumnIndex() == 11) {
                    map.put("flatPrice", returnStr);
                } else if (c.getColumnIndex() == 12) {
                    map.put("valleyPrice", returnStr);
                } else if (c.getColumnIndex() == 13) {
                    map.put("maxNeedCapacity", returnStr);
                } else if (c.getColumnIndex() == 14) {
                    map.put("transformerCapacity", returnStr);
                }

            }
            result.add(map);
        }
        return result;

    }

    public static String getMergedRegionValue(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();

        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress ca = sheet.getMergedRegion(i);
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();

            if (row >= firstRow && row <= lastRow) {

                if (column >= firstColumn && column <= lastColumn) {
                    Row fRow = sheet.getRow(firstRow);
                    Cell fCell = fRow.getCell(firstColumn);
                    fCell.setCellType(Cell.CELL_TYPE_STRING);
                    return fCell.getStringCellValue();
//                    return getCellValue(fCell);
                }
            }
        }

        return null;
    }

    /**
     * 判断合并了行
     *
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    private boolean isMergedRow(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (row == firstRow && row == lastRow) {
                if (column >= firstColumn && column <= lastColumn) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断指定的单元格是否是合并单元格
     *
     * @param sheet
     * @param row    行下标
     * @param column 列下标
     * @return
     */
    private static boolean isMergedRegion(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (row >= firstRow && row <= lastRow) {
                if (column >= firstColumn && column <= lastColumn) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断sheet页中是否含有合并单元格
     *
     * @param sheet
     * @return
     */
    private boolean hasMerged(Sheet sheet) {
        return sheet.getNumMergedRegions() > 0 ? true : false;
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param firstRow 开始行
     * @param lastRow  结束行
     * @param firstCol 开始列
     * @param lastCol  结束列
     */
    private void mergeRegion(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    /**
     * 获取单元格的值
     *
     * @param cell
     * @return
     */
    public static String getCellValue(Cell cell) {

        if (cell == null) return "";

        if (cell.getCellType() == Cell.CELL_TYPE_STRING) {

            return cell.getStringCellValue();

        } else if (cell.getCellType() == Cell.CELL_TYPE_BOOLEAN) {

            return String.valueOf(cell.getBooleanCellValue());

        } else if (cell.getCellType() == Cell.CELL_TYPE_FORMULA) {

            return cell.getCellFormula();

        } else if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {

            return String.valueOf(cell.getNumericCellValue());

        }
        return "";
    }

    /**
     * 从excel读取内容
     */
    public static void readContent(String fileName) {
        boolean isE2007 = false;    //判断是否是excel2007格式
        if (fileName.endsWith("xlsx"))
            isE2007 = true;
        try {
            InputStream input = new FileInputStream(fileName);  //建立输入流
            Workbook wb = null;
            //根据文件格式(2003或者2007)来初始化
            if (isE2007)
                wb = new XSSFWorkbook(input);
            else
                wb = new HSSFWorkbook(input);
            Sheet sheet = wb.getSheetAt(0);     //获得第一个表单
            Iterator<Row> rows = sheet.rowIterator(); //获得第一个表单的迭代器
            while (rows.hasNext()) {
                Row row = rows.next();  //获得行数据
                System.out.println("Row #" + row.getRowNum());  //获得行号从0开始
                Iterator<Cell> cells = row.cellIterator();    //获得第一行的迭代器
                while (cells.hasNext()) {
                    Cell cell = cells.next();
                    System.out.println("Cell #" + cell.getColumnIndex());
                    switch (cell.getCellType()) {   //根据cell中的类型来输出数据
                        case HSSFCell.CELL_TYPE_NUMERIC:
                            System.out.println(cell.getNumericCellValue());
                            break;
                        case HSSFCell.CELL_TYPE_STRING:
                            System.out.println(cell.getStringCellValue());
                            break;
                        case HSSFCell.CELL_TYPE_BOOLEAN:
                            System.out.println(cell.getBooleanCellValue());
                            break;
                        case HSSFCell.CELL_TYPE_FORMULA:
                            System.out.println(cell.getCellFormula());
                            break;
                        default:
                            System.out.println("unsuported sell type=======" + cell.getCellType());
                            break;
                    }
                }
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }


    public static String getNumber(String str) {
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);

        Matcher m = p.matcher(str);

        String string = m.replaceAll(" ").trim();

        String[] strArr = string.split(" ");

        String date = "";
        //遍历数组转换数据类型输出
        for (String s : strArr) {
            if (s.length() < 2) {
                date += "0" + s + "-";
            } else {
                date += s + "-";
            }

        }
        return date.substring(0, date.length() - 1) + " 00:00:00";
    }


    public static <T> List<T> readExcelToObjects(String filePath, int sheetIndex, int startRow,
                                                 Function<Row, T> rowMapper) {
        List<T> objects = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = null;

            // 根据文件扩展名选择合适的工作簿类型
            if (filePath.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (filePath.endsWith(".xls")) {
                workbook = new HSSFWorkbook(fis);
            } else {
                throw new IllegalArgumentException("不支持的文件格式");
            }

            Sheet sheet = workbook.getSheetAt(sheetIndex);

            // 遍历行
            for (int i = startRow; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 使用传入的映射器转换行数据
                T object = rowMapper.apply(row);
                if (object != null) {
                    objects.add(object);
                }
            }

            workbook.close();

        } catch (IOException e) {
            e.printStackTrace();
        }

        return objects;
    }


    /**
     * 读取Excel文件并转换为List<Map>格式，日期以字符串形式返回
     *
     * @param filePath   Excel文件路径
     * @param sheetIndex 工作表索引（从0开始）
     * @param hasHeader  是否有标题行（true表示第一行是标题）
     * @param dateFormat 日期格式字符串，如"yyyy-MM-dd"
     * @return List<Map < String, Object>> 每行数据转换为Map
     */
    public static List<Map<String, Object>> readExcelToListMapWithDateFormat(String filePath, int sheetIndex, boolean hasHeader, String dateFormat) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = null;

            // 根据文件扩展名选择合适的工作簿类型
            if (filePath.endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (filePath.endsWith(".xls")) {
                workbook = new HSSFWorkbook(fis);
            } else {
                throw new IllegalArgumentException("不支持的文件格式");
            }

            Sheet sheet = workbook.getSheetAt(sheetIndex);

            // 获取标题行
            List<String> headers = new ArrayList<>();
            int startRow = 0;

            if (hasHeader && sheet.getLastRowNum() >= 0) {
                Row headerRow = sheet.getRow(0);
                if (headerRow != null) {
                    for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                        Cell cell = headerRow.getCell(i);
                        String header = (cell != null) ? getCellValueAsString(cell) : "Column" + i;
                        headers.add(header.trim().isEmpty() ? "Column" + i : header);
                    }
                }
                startRow = 1;
            }

            // 如果没有标题行，创建默认标题
            if (!hasHeader || headers.isEmpty()) {
                Row firstRow = sheet.getRow(0);
                if (firstRow != null) {
                    for (int i = 0; i < firstRow.getLastCellNum(); i++) {
                        headers.add("Column" + i);
                    }
                }
                startRow = 0;
            }

            // 创建日期格式化器
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(dateFormat);

            // 遍历数据行
            for (int i = startRow; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, Object> rowData = new HashMap<>();
                boolean hasData = false;

                // 遍历每个单元格
                for (int j = 0; j < headers.size(); j++) {
                    Cell cell = row.getCell(j);
                    Object value = getCellValueWithDateFormat(cell, sdf);

                    if (value != null && !value.toString().trim().isEmpty()) {
                        hasData = true;
                    }

                    rowData.put(headers.get(j), value);
                }

                // 只添加有数据的行
                if (hasData) {
                    dataList.add(rowData);
                }
            }

            workbook.close();

        } catch (IOException e) {
            e.printStackTrace();
        }

        return dataList;
    }

    /**
     * 获取单元格值作为字符串 - POI 3.14版本
     */
    public static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case Cell.CELL_TYPE_BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case Cell.CELL_TYPE_FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 获取单元格值，日期以指定格式返回字符串
     */
    private static Object getCellValueWithDateFormat(Cell cell, java.text.SimpleDateFormat dateFormatter) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue();
            case Cell.CELL_TYPE_NUMERIC:
                if (isDateCell(cell)) {
                    return dateFormatter.format(cell.getDateCellValue());
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，返回Long，否则返回Double
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case Cell.CELL_TYPE_BOOLEAN:
                return cell.getBooleanCellValue();
            case Cell.CELL_TYPE_FORMULA:
                // 对于公式，尝试获取计算后的值
                try {
                    if (isDateCell(cell)) {
                        return dateFormatter.format(cell.getDateCellValue());
                    } else {
                        return cell.getNumericCellValue();
                    }
                } catch (Exception e) {
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception e2) {
                        return cell.getCellFormula();
                    }
                }
            case Cell.CELL_TYPE_BLANK:
                return null;
            default:
                return null;
        }
    }

    /**
     * 改进的日期检测方法
     * 除了使用POI的DateUtil.isCellDateFormatted，还检查数值范围和格式代码
     */
    private static boolean isDateCell(Cell cell) {
        if (cell == null || cell.getCellType() != Cell.CELL_TYPE_NUMERIC) {
            return false;
        }

        // 首先使用POI的标准日期检测
        if (DateUtil.isCellDateFormatted(cell)) {
            return true;
        }
        //获取列名
        String columnName = cell.getSheet().getRow(0).getCell(cell.getColumnIndex()).getStringCellValue();
        if (columnName.contains("日期")) {
            return true;
        } else {
            return false;
        }
    }
}
