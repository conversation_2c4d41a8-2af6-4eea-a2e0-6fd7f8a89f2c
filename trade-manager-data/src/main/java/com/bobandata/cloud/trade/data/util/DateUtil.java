package com.bobandata.cloud.trade.data.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * @Author: BYC
 * @Date: 2023/4/29 15:18:27
 * @description: 时间工具类
 **/
public class DateUtil extends org.apache.commons.lang3.time.DateUtils {
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    public static final String FULL_FORMAT = "yyyy/M/dd H:mm:ss.SSS";

    public static final String NORMAL_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String MILLS_FORMAT = "HH:mm:ss.SSS";

    public static final String sFULL_FORMAT_WITH_ZONE = "yyyy-MM-dd HH:mm:ss Z";

    public static final String FULL_FORMAT_WITH_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    private static String[] parsePatterns = new String[]{
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss",
            "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyy-MM-dd_HH:mm:ss", "yyyy-MM-dd HH:mm:ss Z"};

    public static String getDate(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    public static List<LocalTime> splitIntoHours(String start, String end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        List<LocalTime> result = new ArrayList<>();
        if (start != ("23:00") || end != ("24:00")) {
            LocalTime startParse = LocalTime.parse(start);
            LocalTime endParse = LocalTime.parse(end);
            while (!endParse.isBefore(startParse)) {

                if (!result.contains(start)) {
                    result.add(startParse);
                }

                startParse = startParse.plusMinutes(60);
            }
            return result;
        }
        return null;
    }

    public static String formatDate(Date date, Object... pattern) {
        String formatDate;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    public static String formatDateTime(Date date) {
        return formatDate(date, new Object[]{"yyyy-MM-dd HH:mm:ss"});
    }

    public static String formatDBDate(Date date) {
        return formatDate(date, new Object[]{"yyyy-MM-dd"});
    }

    public static String subtractMinuteForDate(Date date, Integer minute) {
        Calendar nowTime = Calendar.getInstance();
        nowTime.setTime(date);
        nowTime.add(12, -minute.intValue());
        return formatDate(nowTime.getTime(), new Object[]{"yyyy-MM-dd HH:mm:ss"});
    }

    public static Date getFirstDayOfMonth(Date time) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(time);
        cale.set(5, 1);
        cale.set(11, 0);
        cale.set(12, 0);
        cale.set(13, 0);
        cale.set(14, 0);
        return cale.getTime();
    }

    public static Date getYearLast(Date year) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(year);
        cal.set(cal.get(1), 11, 31, 23, 59, 59);
        cal.set(14, 999);
        return cal.getTime();
    }

    public static Date getYearFirst(Date year) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(year);
        cal.set(cal.get(1), 0, 1, 0, 0, 0);
        cal.set(14, 0);
        return cal.getTime();
    }

    public static int getMaxDayForMonth(Date time) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(time);
        return cale.getActualMaximum(5);
    }

    public static Date getLastDayOfMonth(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date parseDate(Object str) {
        if (str == null)
            return null;
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Date parseFormatDate(String date, String format) {
        try {
            return parseDate(date, new String[]{format});
        } catch (ParseException e) {
            log.error("parse date error : ", e);
            return null;
        }
    }

    public static String formatDBTimeByZone(Date date, String zone) {
        TimeZone timeZone = TimeZone.getTimeZone(zone);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(date);
    }

    public static String formatDBDateByZone(Date date, String zone) {
        TimeZone timeZone = TimeZone.getTimeZone(zone);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(date);
    }

    public static String formatByZone(Date date, String zone, String format) {
        TimeZone timeZone = TimeZone.getTimeZone(zone);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(date);
    }

    public static Date getOffsetDay(Date date, int count) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(6, count);
        return cale.getTime();
    }

    public static Date getOffsetMinute(Date date, int count) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(12, count);
        return cale.getTime();
    }

    public static Date getOffsetByField(Date date, int field, int count) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(field, count);
        return cale.getTime();
    }

    public static Date getEndTimeOfDay(Date date) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.set(11, 23);
        cale.set(12, 59);
        cale.set(13, 59);
        cale.set(14, 999);
        return cale.getTime();
    }

    public static Date getBeginTimeOfDay(Date date) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.set(11, 0);
        cale.set(12, 0);
        cale.set(13, 0);
        cale.set(14, 0);
        return cale.getTime();
    }

    public static Date getBeginTimeOfHour(Date date) {
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.set(12, 0);
        cale.set(13, 0);
        cale.set(14, 0);
        return cale.getTime();
    }

    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(11);
    }

    public static String getTimeOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        return simpleDateFormat.format(calendar.getTime());
    }

    public static int getFieldCount(Date date, int field) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(field);
    }

    public static String getNextMonthDate(String currentMonthStr) {
        currentMonthStr = currentMonthStr + "-01";
        LocalDate currentMonth = LocalDate.parse(currentMonthStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate lastMonth = currentMonth.minusMonths(-1L);
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
        return formatters.format(lastMonth);
    }

    public static String getBeijingDate(Date sourceDate) {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(sourceDate);
    }

    public static String formatStartTime(String yearAndMonth) {
        return yearAndMonth + "-01 00:00:00";
    }

    public static String formatEndTime(String yearAndMonth) {
        yearAndMonth = yearAndMonth + "-01";
        LocalDate monthOfLastDate = LocalDate.parse(yearAndMonth, DateTimeFormatter.ofPattern("yyyy-MM-dd")).with(TemporalAdjusters.lastDayOfMonth());
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String lastDate = formatters.format(monthOfLastDate);
        return lastDate;
    }

    public static String getLastDateByYearAndMonth(String yearAndMonth) {
        if (yearAndMonth.length() < 10)
            yearAndMonth = yearAndMonth + "-01";
        LocalDate monthOfLastDate = LocalDate.parse(yearAndMonth, DateTimeFormatter.ofPattern("yyyy-MM-dd")).with(TemporalAdjusters.lastDayOfMonth());
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return formatters.format(monthOfLastDate);
    }

    public static String formatBeijingZeroTime(String dateStr) {
        return dateStr + "T08:00:00Z";
    }

    public static String getBeforeDateByCurrent() {
        Date beforeDate = getOffsetDay(new Date(), -1);
        return getBeijingDate(beforeDate);
    }

    public static String getBeforeFiveDateByCurrent() {
        Date beforeDate = getOffsetDay(new Date(), -5);
        return getBeijingDate(beforeDate);
    }

    public static Long startToEndByDay(String startDateStr, String endDateStr) {
        if (StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr))
            return null;
        LocalDate startDate = LocalDate.parse(startDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(endDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return Long.valueOf(startDate.until(endDate, ChronoUnit.DAYS) + 1L);
    }

    public static String getOffsetDay(String dateStr, int count) {
        Date date = parseFormatDate(dateStr, "yyyy-MM-dd");
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(6, -count);
        return formatDBDate(cale.getTime());
    }

    public static String getOffsetDayByAdd(String dateStr, int count) {
        Date date = parseFormatDate(dateStr, "yyyy-MM-dd");
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(6, count);
        return formatDBDate(cale.getTime());
    }

    public static String getCurrentBeijingTime() {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(new Date());
    }

    public static String getCurrentBeijingDate() {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+8:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(new Date());
    }

    public static String getCurrentUTCTime() {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+0:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        simpleDateFormat.setTimeZone(timeZone);
        return simpleDateFormat.format(new Date());
    }

    public static String getCurrentDayZeroUtcTime() {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+0:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        String day = simpleDateFormat.format(new Date());
        return day + " 00:00:00";
    }

    public static String getNextDayZeroUtcTime() {
        TimeZone timeZone = TimeZone.getTimeZone("GMT+0:00");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(timeZone);
        String day = simpleDateFormat.format(new Date());
        String nextDay = getOffsetDay(day, 1);
        return nextDay + " 00:00:00";
    }

    public static String bigDecimalToDateStr(BigDecimal mills, String format) {
        String millStr = mills.toPlainString();
        long time = Long.parseLong(millStr);
        Date date = new Date(time);
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
        return dateFormat.format(date);
    }

    public static String DoubleToDateStr(Double mills, String format, String timeZone) {
        long time = mills.longValue();
        Date date = new Date(time);
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));
        return dateFormat.format(date);
    }

    public static String dateToStr(Date date, String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(date);
    }

    public static long getOffsetHoursByNowTime(long time) {
        Date now = new Date();
        long diff = now.getTime() - time;
        return diff / 3600000L;
    }

    public static long getOffsetMinutesByNowTime(String time, String timeZone) {
        String now = formatDBTimeByZone(new Date(), timeZone);
        Date nowTime = parseFormatDate(now, "yyyy-MM-dd HH:mm:ss");
        Date beginTime = parseFormatDate(time, "yyyy-MM-dd HH:mm:ss");
        long diff = nowTime.getTime() - beginTime.getTime();
        return diff / 60000L;
    }

    public static Integer getOffsetMinutes(Date startTime, Date endTime) {
        if (startTime == null || endTime == null)
            return null;
        Long diff = Long.valueOf((endTime.getTime() - startTime.getTime()) / 60000L);
        return Integer.valueOf(diff.intValue());
    }

    public static String getFormatTime(String date, int hour) {
        if (hour < 10)
            return date + " 0" + hour + ":00:00";
        return date + " " + hour + ":00:00";
    }

    public static String getFormatTime(String date, String hourAndMinute) {
        return date + " " + hourAndMinute + ":00";
    }

    public static List<Integer> getHourList() {
        List<Integer> list = new ArrayList<>();
        for (int i = 0; i < 24; i++)
            list.add(Integer.valueOf(i));
        return list;
    }

    public static String getFormatTimeRange(Date startTime, Date endTime) {
        if (startTime == null || endTime == null)
            return null;
        String start = formatDateTime(startTime);
        String end = formatDateTime(endTime);
        return start.substring(11, 16) + "-" + end.substring(11, 16);
    }

    public static Date parseFormatDate(String date) {
        try {
            return parseDate(date, new String[]{"yyyy-MM-dd HH:mm:ss"});
        } catch (ParseException e) {
            log.error("parse date error : ", e);
            return null;
        }
    }

    public static String getZeroTimeByCurrentTime(String currentTime) {
        return currentTime.substring(0, 10) + " 00:00:00";
    }

    public static int compareTimes(String time1, String time2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date dt1 = df.parse(time1);
            Date dt2 = df.parse(time2);
            if (dt1.getTime() > dt2.getTime())
                return -1;
            if (dt1.getTime() < dt2.getTime())
                return 1;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static Long getOffsetSeconds(Date startTime, Date endTime) {
        if (startTime == null || endTime == null)
            return null;
        Long diff = Long.valueOf((endTime.getTime() - startTime.getTime()) / 1000L);
        return diff;
    }

    public static String getLastTimeByDay(String day) {
        return day + " 23:59:59";
    }

    public static boolean isMixed(Date leftStartDate, Date leftEndDate, Date rightStartDate, Date rightEndDate) {
        if ((leftStartDate.getTime() >= rightStartDate.getTime() && leftStartDate
                .getTime() < rightEndDate.getTime()) || (leftStartDate
                .getTime() > rightStartDate.getTime() && leftStartDate
                .getTime() <= rightEndDate.getTime()) || (rightStartDate
                .getTime() >= leftStartDate.getTime() && rightStartDate
                .getTime() < leftEndDate.getTime()) || (rightStartDate
                .getTime() > leftStartDate.getTime() && rightStartDate
                .getTime() <= leftEndDate.getTime()))
            return true;
        return false;
    }

    public static boolean isMonthlyTime(String time) {
        return (time.length() == 7);
    }

    public static String getLastDayOfMonth(String yearMonth) {
        int year = Integer.parseInt(yearMonth.split("-")[0]);
        int month = Integer.parseInt(yearMonth.split("-")[1]);
        Calendar cal = Calendar.getInstance();
        cal.set(1, year);
        cal.set(2, month);
        int lastDay = cal.getMinimum(5);
        cal.set(5, lastDay - 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    public static List<String> getHoursAndMinutesByDay(String date) {
        List<String> list = new ArrayList<>();
        int a = 0;
        String value = "";
        int i;
        for (i = 0; i < 96; i += 4) {
            String flag = "00";
            for (int j = 0; j < 4; j++) {
                if (i < 40) {
                    value = "0" + a + ":" + flag;
                    list.add(date + " " + value + ":00");
                } else if (i >= 40) {
                    value = a + ":" + flag;
                    list.add(date + " " + value + ":00");
                }
                flag = (Integer.parseInt(flag) + 15) + "";
            }
            a++;
        }
        list.add(date + " 24:00:00");
        list.remove(0);
        return list;
    }

    public static List<String> getHourGapString(String time, Integer gap) {
        return getGapTimeString(time, gap, "hour");
    }

    public static List<String> getMinuteGapString(String time, Integer gap) {
        return getGapTimeString(time, gap, "minute");
    }

    public static List<String> getSecondGapString(String time, Integer gap) {
        return getGapTimeString(time, gap, "second");
    }

    public static List<String> getGapTimeString(String time, Integer gap, String type) {
        int count;
        if (time == null || time.length() != 10)
            throw new IllegalArgumentException();
        List<String> result = new ArrayList<>();
        boolean isHour = "hour".equals(type);
        boolean isMinute = "minute".equals(type);
        boolean isSecond = "second".equals(type);
        if (isHour) {
            count = 24;
        } else {
            count = 60;
        }
        if (!Integer.valueOf(0).equals(Integer.valueOf(count % gap.intValue())))
            throw new IllegalArgumentException("count must can be divided by gap exactly");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String baseTime = time + " 00:00:00";
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(simpleDateFormat.parse(baseTime));
        } catch (ParseException e) {
            log.error("DateUtils.getGapTimeString() time parse failed");
            throw new IllegalArgumentException("time parse failed");
        }
        if (isHour) {
            for (int i = 0; i < count / gap.intValue(); i++) {
                calendar.add(10, gap.intValue());
                result.add(simpleDateFormat.format(calendar.getTime()));
            }
        } else {
            for (int i = 0; i < 24; i++) {
                if (isMinute)
                    for (int j = 0; j < count / gap.intValue(); j++) {
                        calendar.add(12, gap.intValue());
                        result.add(simpleDateFormat.format(calendar.getTime()));
                    }
                if (isSecond)
                    for (int j = 0; j < count; j++) {
                        for (int k = 0; k < count / gap.intValue(); k++) {
                            calendar.add(13, gap.intValue());
                            result.add(simpleDateFormat.format(calendar.getTime()));
                        }
                    }
            }
        }
        result.remove(result.size() - 1);
        result.add(time + " 24:00:00");
        return result;
    }

    public static List<String> getMonthString(String time) {
        if (time == null || time.length() < 7 || time.length() > 10)
            throw new IllegalArgumentException();
        List<String> result = new ArrayList<>();
        String baseTime = "";
        if (time.length() == 7) {
            baseTime = time + "-01";
        } else if (time.length() == 10) {
            baseTime = time.substring(0, 7) + "-01";
        }
        result.add(baseTime);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(simpleDateFormat.parse(baseTime));
        } catch (ParseException e) {
            log.error("DateUtils.getGapTimeString() time parse failed, time:{}", time);
            throw new IllegalArgumentException("time parse failed");
        }
        int maximum = calendar.getActualMaximum(5);
        for (int i = 1; i < maximum; i++) {
            calendar.add(5, 1);
            String format = simpleDateFormat.format(calendar.getTime());
            result.add(format);
        }
        return result;
    }

    public static Date getTime(String timeStr, String timeZone, String formatter) {
        if (StringUtils.isEmpty(timeStr) || StringUtils.isEmpty(formatter))
            return null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatter);
        Date date = null;
        try {
            date = simpleDateFormat.parse(timeStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
        TimeZone localTimeZone = getLocalTimeZone();
        TimeZone srcTimeZone = TimeZone.getTimeZone(timeZone);
        Long targetTime = Long.valueOf(date.getTime() + localTimeZone.getRawOffset() - srcTimeZone.getRawOffset());
        return new Date(targetTime.longValue());
    }

    public static TimeZone getLocalTimeZone() {
        DateFormat dateFormat = new SimpleDateFormat("Z");
        String localTimeZone = dateFormat.format(new Date());
        return TimeZone.getTimeZone("GMT" + localTimeZone);
    }

    public static List<String> getAllMonthByYear(String year) {
        List<String> list = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            String month;
            if (i < 10) {
                month = year + "-0" + i;
            } else {
                month = year + "-" + i;
            }
            list.add(month);
        }
        return list;
    }

    public static boolean isYearTime(String time) {
        return (time.length() == 4);
    }

    public static Date getBeijingTimeByUtc(String utcTime, String formatPattern) {
        if (StringUtils.isBlank(utcTime))
            return null;
        ZonedDateTime zonedDateTime = ZonedDateTime.of(LocalDateTime.parse(utcTime, DateTimeFormatter.ofPattern(formatPattern)), ZoneId.of("Etc/UTC")).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        if (zonedDateTime != null)
            return Date.from(zonedDateTime.toInstant());
        return null;
    }

    public static String getOffsetTimeByDay(String timeStr, int offsetDay) {
        Date date = parseFormatDate(timeStr, "yyyy-MM-dd HH:mm:ss");
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        cale.add(6, offsetDay);
        String dateStr = formatDBDate(cale.getTime());
        return dateStr + " " + timeStr.substring(11, 19);
    }

    public static boolean isHistoryDay(String day) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date dt1 = df.parse(getCurrentBeijingDate());
            Date dt2 = df.parse(day);
            if (dt1.getTime() > dt2.getTime())
                return true;
            if (dt1.getTime() < dt2.getTime())
                return false;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static Integer getOffsetDayWithMonthLastDay(String time) {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar ca = Calendar.getInstance();
        ca.setTime(parseFormatDate(time, "yyyy-MM-dd"));
        ca.set(5, ca.getActualMaximum(5));
        String lastDay = format.format(ca.getTime());
        Date startTime = null;
        Date endTime = null;
        try {
            startTime = format.parse(time);
            endTime = format.parse(lastDay);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (startTime != null && endTime != null) {
            Long diff = Long.valueOf((endTime.getTime() - startTime.getTime()) / 86400000L);
            if (diff != null)
                return Integer.valueOf(diff.intValue());
        }
        return null;
    }

    public static boolean isInTimeByStartAndEnd(String time, String startTime, String endTime) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date start = df.parse(startTime);
            Date end = df.parse(endTime);
            Date timeDate = df.parse(time);
            if (start.getTime() <= timeDate.getTime() && end.getTime() > timeDate.getTime())
                return true;
            return false;
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static int getDaysFromTodayToMonthEnd(String day) {
        LocalDate today = LocalDate.parse(day);
        LocalDate lastDay = today.with(TemporalAdjusters.lastDayOfMonth());
        return lastDay.getDayOfMonth() - today.getDayOfMonth() + 1;
    }

    public static boolean isFirstDayOfMonth(String day) {
        LocalDate today = LocalDate.parse(day);
        if (today.getDayOfMonth() == 1)
            return true;
        return false;
    }

    public static boolean isLastDayOfMonth(String day) {
        LocalDate today = LocalDate.parse(day);
        LocalDate lastDay = today.with(TemporalAdjusters.lastDayOfMonth());
        if (today.getDayOfMonth() == lastDay.getDayOfMonth())
            return true;
        return false;
    }

    public static int compareDay(String day1, String day2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date dt1 = df.parse(day1);
            Date dt2 = df.parse(day2);
            if (dt1.getTime() > dt2.getTime())
                return -1;
            if (dt1.getTime() < dt2.getTime())
                return 1;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static int getDaysOfMonth(String day) {
        LocalDate today = LocalDate.parse(day);
        LocalDate lastDay = today.with(TemporalAdjusters.lastDayOfMonth());
        return lastDay.getDayOfMonth();
    }

    public static String getFullDate(String day, int dayNumber) {
        String fullDay = day.substring(0, 7);
        if (dayNumber < 10)
            return fullDay + "-0" + dayNumber;
        return fullDay + "-" + dayNumber;
    }

    public static String getZeroTimeOfCurrentDay() {
        return getCurrentBeijingDate() + " 00:00:00";
    }

    public static List<String> getIntervalTimeOfDay() {
        List<String> list = getGapTimeString("2021-01-01", Integer.valueOf(15), "minute");
        List<String> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i++)
            result.add(((String) list.get(i)).substring(11, 16));
        return result;
    }

    public static List<String> getDaysBetweenStartAndEnd(String startTime, String endTime) {
        List<String> days = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date start = dateFormat.parse(startTime);
            Date end = dateFormat.parse(endTime);
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(start);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(end);
            tempEnd.add(5, 1);
            while (tempStart.before(tempEnd)) {
                days.add(dateFormat.format(tempStart.getTime()));
                tempStart.add(6, 1);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return days;
    }
}
