package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;
import lombok.Data;
import java.time.LocalDateTime;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-05-29 
 */
@Data
public class TradeConfigDo implements Serializable {

	private static final long serialVersionUID =  1532174924971882535L;

	private String guid;

	private String tradeseqId;

	private String flowId;

	private String xh;

	private String jdId;

	private String jdName;

	private String isControl;

	private LocalDateTime sdate;

	private LocalDateTime edate;

	private String conBegDate;

	private String conEndDate;

	private String updateTime;

	private String createPerson;

	private String marketId;

}
