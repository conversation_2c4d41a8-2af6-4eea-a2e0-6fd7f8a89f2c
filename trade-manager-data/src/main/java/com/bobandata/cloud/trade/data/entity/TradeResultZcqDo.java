package com.bobandata.cloud.trade.data.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("trade_result_zcq_do_hn")
public class TradeResultZcqDo {

    private String tradeGgId;
    private String guid;
    private String tradeseqId;
    private String seRouteId;
    private Integer intervalIndex;
    private Integer tradeTimepart;
    private Integer bandNo;
    private String saleUnitsCode;
    private String saleUnitsName;
    private String vendeeUnitsCode;
    private String vendeeUnitsName;
    private String saleParticipantId;
    private String saleParticipantName;
    private String vendeeParticipantId;
    private String vendeeParticipantName;
    private Double vendeeEnergy;
    private Double saleEnergy;
    private Double vendeePrice;
    private Double salePrice;
    private String vendeeEnergyPeriod1;
    private String saleEnergyPeriod1;
    private String vendeePricePeriod1;
    private String salePricePeriod1;
    private String vendeeEnergyPeriod2;
    private String saleEnergyPeriod2;
    private String vendeePricePeriod2;
    private String salePricePeriod2;
    private String vendeeEnergyPeriod3;
    private String saleEnergyPeriod3;
    private String vendeePricePeriod3;
    private String salePricePeriod3;
    private String vendeeEnergyPeriod4;
    private String saleEnergyPeriod4;
    private String vendeePricePeriod4;
    private String salePricePeriod4;
    private String vendeeEnergyPeriod5;
    private String saleEnergyPeriod5;
    private String vendeePricePeriod5;
    private String salePricePeriod5;
    private String vendeeGrosseng;
    private String saleGrosseng;
    private String vendeeGrossengPeriod1;
    private String saleGrossengPeriod1;
    private String vendeeGrossengPeriod2;
    private String saleGrossengPeriod2;
    private String vendeeGrossengPeriod3;
    private String coCompensationPrice;
    private String saleGrossengPeriod3;
    private String vendeeGrossengPeriod4;
    private String saleGrossengPeriod4;
    private String vendeeGrossengPeriod5;
    private String saleGrossengPeriod5;
    private Double vendeeGenRate;
    private String saleGenRate;
    private String powerCurveId;
    private String netDiscountLoss;
    private String tradePriceMargin;
    private String approvalResult;
    private String releaseStatus;
    private String saleBidinfoId;
    private String vendeeBidinfoId;
    private String bindId;
    private String bindName;
    private String elecContractId;
    private String isMarginalPrice;
    private String createTime;
    private String jjtDataFlag;
    private String isInOut;
    private String vendeeMarketId;
    private String saleMarketId;
    private String vendeePowerGridId;
    private String vendeePowerGridName;
    private String salePowerGridId;
    private String salePowerGridName;
    private String querenTime;
    private Double clearingEnergy;
    private String clearingEnergyPeriod1;
    private String clearingEnergyPeriod2;
    private String clearingEnergyPeriod3;
    private String clearingEnergyPeriod4;
    private String clearingEnergyPeriod5;
    private Double clearingPrice;
    private String clearingPricePeriod1;
    private String clearingPricePeriod2;
    private String clearingPricePeriod3;
    private String clearingPricePeriod4;
    private String clearingPricePeriod5;
    private String coTransStartDate;
    private String coTransEndDate;
    private String coTransCostsRole;
    private String coTransCompensationPrice;
    private String coTransCompensationSelfprice;
    private String timeDivisionCode;
    private String timeDivisionRange;
    private String timeDivisionName;
    private String saleMarketName;
    private String vendeeMarketName;
    private String salePowerType;
    private String vendeePowerType;
    private String saleParticipantType;
    private String vendeeParticipantType;
    private String vendeeDecDate;
    private String saleDecDate;
    private String remark;
    private String parentGuId;
    private String bak1;
    private String bak2;
    private String bak3;
    private String bak4;
    private String preTradeseqId;
    private String preTradeseqCaption;
    private String preResultId;
    private String trBeginTime;
    private String trEndTime;
    private String saleAdjustFactor;
    private String vendeeAdjustFactor;
    private String unitId;
    private String unitName;
    private String unitMonthEnergy;
    private String startTime;
    private String endTime;
    private String countSale;
    private String countVendee;
    private String sumVendeeEnergy;
    private String sumSaleEnergy;
    private String avgVendeePrice;
    private String avgsalePrice;
    private String tradeTypeEnergy;
    private String monthEnergySum;
    private String beforEnergy;
    private String comparedSame;
    private String tradeTypeMode;
    private String timePart;
    private String timeDesc;
    private String tradeseqCaption;
    private String idList;
    private String strings;
    private String tradeseqIds;
    private String tradetypeId;
    private String guids;
    private String tradeCaption;
    private String timePartName;
    private String timePartContent;
    private Integer vendeeEnergySum;
    private Integer saleEnergySum;
    private String powerGenerationType;
    private String sumEnergy;
    private String energyFirst;
    private String energySecond;
    private String energyTotal;
    private String newIntervalIndex;
    private String unitEnergy;

}
