package com.bobandata.cloud.trade.data.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.enums.ServiceStatus;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.config.TradeInterfaceUrlConfig;
import com.bobandata.cloud.trade.data.dto.*;
import com.bobandata.cloud.trade.data.entity.*;
import com.bobandata.cloud.trade.data.service.TradeSysService;
import com.bobandata.cloud.trade.data.util.ExcelUtil;
import com.bobandata.cloud.trade.data.util.FlinkBaseUtil;
import com.bobandata.cloud.trade.data.util.HttpUtil;
import com.bobandata.cloud.trade.data.vo.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * @Author: BYC
 * @Date: 2024/1/29 15:42:51
 * @description:
 **/
@Slf4j
@Service
public class TradeSysServiceImp implements TradeSysService {


    private final static String green_power_url = "https://pmos.hn.sgcc.cn/px-settlement-extnetupload/hunan/seDuDeclareEnergy/query";

    private final static String menu_url = "https://pmos.hn.sgcc.com.cn/px-trade-extranet/trTradeType/getMenu";
    private final static String queryTradeCaptionBytype = "https://pmos.hn.sgcc.com.cn/px-trade-extranet/trTradeseqInfo/queryTradeCaptionBytype";
    private final static String queryForResultCalcu = "https://pmos.hn.sgcc.com.cn/px-trade-extranet/TrResultMainController/queryResultByTypeOuter";
    private final static String querySpotTrade = "https://pmos.hn.sgcc.com.cn/px-spotgoods-province/trade/queryUserDeclareCurve";

    private final static String currentRoute = "/pxf-trade-extranet/myTransaction/TradeQuery?date=";
    @Autowired
    private TradeInterfaceUrlConfig tradeInterfaceUrlConfig;

    @Autowired
    private TradeResultZcqMapper tradeResultZcqMapper;

    @Autowired
    private TradeNoticeListHnMapper tradeNoticeListHnMapper;

    @Autowired
    private TradeNoticeDetailMapper tradeNoticeDetailMapper;

    @Autowired
    private TradeElectricityPriceMapper tradeElectricityPriceMapper;

    @Autowired
    private TradeGreenPowerHnMapper tradeGreenPowerHnMapper;

    @Autowired
    private MlTradeCurveMapper mlTradeCurveMapper;

    @Autowired
    private MlTradeCqdlMapper mlTradeCqdlMapper;


    @Value("${dbUrl}")
    private String dbUrl;
    @Value("${dbUserName}")
    private String dbUserName;
    @Value("${dbPassWord}")
    private String dbPassWord;
    @Value("${downloadPath}")
    private String downloadPath;

    @Override
    public ServiceResult getAllZCQResult(Map<String, String> headerMap) {

        headerMap.put("CurrentRoute", currentRoute + System.currentTimeMillis());

        //获取交易类型
        HashMap<String, String> stringMap = new HashMap<>();
        String getMenuResult = HttpUtil.httpPostUtil(menu_url, stringMap.toString(), headerMap, 20, 20, true);

        String data = JSONObject.parseObject(getMenuResult).getString("data");
        List<MenuDataVo> menuDataVos = JSONObject.parseArray(data, MenuDataVo.class);

        if (!menuDataVos.isEmpty()) {
            for (MenuDataVo menuDataVo : menuDataVos) {
                List<MenuDataVo> children = menuDataVo.getChildren();
                if (!children.isEmpty()) {
                    for (MenuDataVo child : children) {
                        Map<String, String> tradetypeIdmap = new HashMap<>();
                        tradetypeIdmap.put("tradetypeId", child.getTradeTypeCode());
                        //获取交易序列
                        String queryTradeCaptionBytypeRes = HttpUtil.httpPostUtil(queryTradeCaptionBytype, JSONObject.toJSONString(tradetypeIdmap), headerMap, 40, 40, true);
//                        String queryTradeCaptionBytypeRes = OkHttpUtil.postFormParams(queryTradeCaptionBytype, tradetypeIdmap, tokenMap);

                        String typeData = JSONObject.parseObject(queryTradeCaptionBytypeRes).getString("data");
                        List<TradeTypeVo> tradeTypeVos = JSONObject.parseArray(typeData, TradeTypeVo.class);
                        if (tradeTypeVos.size() > 0) {
                            for (TradeTypeVo tradeTypeVo : tradeTypeVos) {
                                String tradeseqId = tradeTypeVo.getTradeseqId();
                                log.info("第一次请求：tradeseqId, getId(),getTradeTypeCode():" + tradeseqId + "--" + child.getId() + "--" + child.getTradeTypeCode());

                                //1代表取第一页数据
                                String param = getParam(tradeseqId, child.getId(), child.getTradeTypeCode(), 1);
                                String FirstResult = HttpUtil.httpPostUtil(queryForResultCalcu, param, headerMap, 60, 60, true);
                                //获取当前交易序列数据总量
                                Integer total = (Integer) JSONObject.parseObject(FirstResult).getJSONObject("data").get("total");
                                log.info("total:" + total);
                                if (total == 0) {
                                    continue;
                                }
                                int i = total / 5000;
                                if (i > 0) {
                                    List<TradeResultZcqDo> tradeResult = getTradeResult(FirstResult, child.getTradeTypeCode());

                                    //j=2表示从第二页开始循环取数
                                    for (int j = 2; j < i + 2; j++) {
                                        log.info("第" + j + "次请求！");
                                        log.info("tradeseqId, getId(),getTradeTypeCode():" + tradeseqId + "--" + child.getId() + "--" + child.getTradeTypeCode());
                                        String paramByPage = getParam(tradeseqId, child.getId(), child.getTradeTypeCode(), j);
                                        String resultByPage = HttpUtil.httpPostUtil(queryForResultCalcu, paramByPage, headerMap, 60, 60, true);
                                        List<TradeResultZcqDo> tradeResultByPage = getTradeResult(resultByPage, child.getTradeTypeCode());
                                        tradeResult.addAll(tradeResultByPage);
                                    }
                                    log.info("tradeResult:" + tradeResult.size());
                                    saveTradeResult(tradeResult);
                                } else {
                                    List<TradeResultZcqDo> tradeResult = getTradeResult(FirstResult, child.getTradeTypeCode());
                                    saveTradeResult(tradeResult);
                                }
                            }
                        }
                    }
                }
            }
            FlinkBaseUtil flinkBaseUtil = new FlinkBaseUtil();
            flinkBaseUtil.writeData(dbUrl, dbUserName, dbPassWord);
            return ServiceResult.success("中长期合同数据导入成功！");
        }
        return ServiceResult.error(500, "中长期合同数据导入失败！");
    }

    @Override
    public ServiceResult getTradeType(String area, String Cookie, String Currentroute) {
        Map<String, String> areaInfo = getAreaInfo(area);
        if (!area.equals("hn") && !area.equals("gs")) {
            return new ServiceResult(ServiceStatus.FAIL_NOT_FOUND, "无此地区数据!");
        }
        String getMenu = areaInfo.get("getMenu");
        Boolean isSSL = Boolean.parseBoolean(areaInfo.get("isSSL"));

        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("Cookie", Cookie);
        tokenMap.put("Currentroute", Currentroute);
        HashMap<String, String> stringMap = new HashMap<>();
        String getMenuResult = HttpUtil.httpPostUtil(getMenu, stringMap.toString(), tokenMap, 20, 20, isSSL);
        Object menuRes = JSONObject.parse(getMenuResult);
        return ServiceResult.success(menuRes);
    }

    @Override
    public ServiceResult getTradeCaptionBytype(String area, String Cookie, String Currentroute, String tradetypeId) {
        Map<String, String> areaInfo = getAreaInfo(area);
        if (!area.equals("hn") && !area.equals("gs")) {
            return new ServiceResult(ServiceStatus.FAIL_NOT_FOUND, "无此地区数据!");
        }
        String queryTradeCaptionBytype = areaInfo.get("queryTradeCaptionBytype");
        Boolean isSSL = Boolean.parseBoolean(areaInfo.get("isSSL"));

        Map<String, String> tradetypeIdMap = new HashMap<>();
        tradetypeIdMap.put("tradetypeId", tradetypeId);

        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("Cookie", Cookie);
        tokenMap.put("Currentroute", Currentroute);

        String queryTradeCaptionBytypeRes = HttpUtil.httpPostUtil(queryTradeCaptionBytype, JSONObject.toJSONString(tradetypeIdMap), tokenMap, 20, 20, isSSL);
        Object tradeCaptionBytypeRes = JSONObject.parse(queryTradeCaptionBytypeRes);
        return ServiceResult.success(tradeCaptionBytypeRes);
    }

    @Override
    public ServiceResult getResultCalcu(String area, String Cookie, String Currentroute, String tradeseqId, String tradeTypeId, String subTradetypeCode, Integer pageNum, String isSave) {
        Map<String, String> areaInfo = getAreaInfo(area);
        if (!area.equals("hn") && !area.equals("gs")) {
            return new ServiceResult(ServiceStatus.FAIL_INVALID_PARAM, "无此地区数据!");
        }
        String queryForResultCalcu = areaInfo.get("queryForResultCalcu");
        Boolean isSSL = Boolean.parseBoolean(areaInfo.get("isSSL"));

        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("Cookie", Cookie);
        tokenMap.put("Currentroute", Currentroute);

        String param = getParam(tradeseqId, tradeTypeId, subTradetypeCode, pageNum);
        log.info("tradeseqId:" + tradeseqId + ";tradeTypeId:" + tradeTypeId + ";subTradetypeCode:" + subTradetypeCode);
        String firstResult = HttpUtil.httpPostUtil(queryForResultCalcu, param, tokenMap, 30, 30, isSSL);
        if (isSave.equals("y")) {
            log.info("第一次取数据");
            Integer total = (Integer) JSONObject.parseObject(firstResult).getJSONObject("data").get("total");
            if (total == 0 || ObjectUtils.isEmpty(total)) {
                return new ServiceResult(ServiceStatus.FAIL_NOT_FOUND, "无数据!");
            }
            List<TradeResultZcqDo> tradeResult = getTradeResult(firstResult, subTradetypeCode);
            log.info("total = " + total);
            int i = total / 5000;
            if (i > 0) {
                //j=2表示从第二页开始循环取数
                for (int j = 2; j < i + 2; j++) {
                    log.info("第" + j + "次取数据");
                    String paramByPage = getParam(tradeseqId, tradeTypeId, subTradetypeCode, j);
                    String resultByPage = HttpUtil.httpPostUtil(queryForResultCalcu, paramByPage, tokenMap, 30, 30, isSSL);
                    log.info("total = " + total);
                    List<TradeResultZcqDo> tradeResultByPage = getTradeResult(resultByPage, subTradetypeCode);
                    tradeResult.addAll(tradeResultByPage);
                }
                saveTradeResult(tradeResult);
                return new ServiceResult(true);
            } else {
                List<TradeResultZcqDo> tradeResultFirst = getTradeResult(firstResult, subTradetypeCode);
                saveTradeResult(tradeResultFirst);
                return new ServiceResult(true);
            }
        }
        Object zcqResultCalcu = JSONObject.parse(firstResult);
        return ServiceResult.success(zcqResultCalcu);
    }

    @Override
    public ServiceResult getTradeNotice(String Area, String Cookie, String Currentroute) {
        Map<String, String> areaInfo = getAreaInfo(Area);
        if (!Area.equals("hn") && !Area.equals("gs")) {
            return new ServiceResult(ServiceStatus.FAIL_NOT_FOUND, "无此地区数据!");
        }
        String tradeNotice = areaInfo.get("tradeNotice");
        String getAnnoAllInfo = areaInfo.get("getAnnoAllInfo");
        Boolean isSSL = Boolean.parseBoolean(areaInfo.get("isSSL"));

        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("Cookie", Cookie);
        tokenMap.put("Currentroute", Currentroute);

        Map<String, Map> paramMap = new HashMap<>();

        Map<String, String> data = new HashMap<>();
        Map<String, Integer> pageInfo = new HashMap<>();

        data.put("tradetypeId", "");
        data.put("tradeseqId", "");
        data.put("subTradetypeCode", "");

        pageInfo.put("pageNum", 1);
        pageInfo.put("pageSize", 50);

        paramMap.put("data", JSON.parseObject(JSON.toJSONString(data), Map.class));
        paramMap.put("pageInfo", JSON.parseObject(JSON.toJSONString(pageInfo), Map.class));
        String paramStr = JSON.toJSONString(paramMap, SerializerFeature.WriteMapNullValue);
        try {
            //获取交易公告数据
            String tradeNoticeRes = HttpUtil.httpPostUtil(tradeNotice, paramStr, tokenMap, 60, 60, isSSL);
            String list = JSONObject.parseObject(tradeNoticeRes).getJSONObject("data").getString("list");
            List<TradeNoticeListHnDo> tradeNoticeListHnDos = JSONObject.parseArray(list, TradeNoticeListHnDo.class);

            for (TradeNoticeListHnDo tradeNoticeListHnDo : tradeNoticeListHnDos) {
                String tradeseqId = tradeNoticeListHnDo.getTradeseqId();
                QueryWrapper<TradeNoticeListHnDo> noticeListWrapper = new QueryWrapper<>();
                noticeListWrapper.eq("tradeseq_id", tradeseqId);
                noticeListWrapper.eq("trade_role", tradeNoticeListHnDo.getTradeRole());
                Long count = tradeNoticeListHnMapper.selectCount(noticeListWrapper);
                if (count <= 0) {
                    tradeNoticeListHnMapper.insert(tradeNoticeListHnDo);
                    Map<String, String> infoParamMap = new HashMap<>();
                    infoParamMap.put("tradeseqId", tradeseqId);
                    String infoParamStr = JSONObject.toJSONString(infoParamMap);
                    //获取交易公告详情
                    String noticeDetailRes = HttpUtil.httpPostUtil(getAnnoAllInfo, infoParamStr, tokenMap, 60, 60, isSSL);
                    JSONObject object = JSONObject.parseObject(noticeDetailRes).getJSONObject("data");
                    TradeNoticeDetailDo tradeNoticeDetailDo = JSONObject.parseObject(object.toJSONString(), TradeNoticeDetailDo.class);
                    QueryWrapper<TradeNoticeDetailDo> tradeNoticeDetailWrapper = new QueryWrapper<>();
                    tradeNoticeDetailWrapper.eq("tradeseq_id", tradeseqId);
                    Long detailTotal = tradeNoticeDetailMapper.selectCount(tradeNoticeDetailWrapper);
                    if (detailTotal <= 0) {
                        tradeNoticeDetailDo.setTradeseqId(tradeseqId);
                        tradeNoticeDetailMapper.insert(tradeNoticeDetailDo);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ServiceResult.error(500, "交易公告数据导入失败！");
        }
        return ServiceResult.success("交易公告数据导入成功！");
    }


    public ServiceResult uploadProxyPriceFile(@RequestParam("file") MultipartFile file) {

        try {
            //获得日期
            List<Map<String, String>> dateList = ExcelUtil.readExcel(file.getInputStream(), 0, 3, 3);

            List<Map<String, String>> list = ExcelUtil.readExcel(file.getInputStream(), 0, 7, 14);
            List<TradeElectricityPriceDo> tradeElectricityPriceDos = JSONObject.parseArray(JSONObject.toJSONString(list), TradeElectricityPriceDo.class);

            String date = dateList.get(0).get("powerType");
            String[] split = date.split("-");
            String executeStartDate = ExcelUtil.getNumber(split[0]);
            String executeEneDate = ExcelUtil.getNumber(split[1]);
            QueryWrapper<TradeElectricityPriceDo> electricityPriceDoWrapper = new QueryWrapper<>();
            electricityPriceDoWrapper.eq("execute_start_date", executeStartDate);
            electricityPriceDoWrapper.eq("execute_end_date", executeEneDate);
            Long count = tradeElectricityPriceMapper.selectCount(electricityPriceDoWrapper);
            if (count <= 0) {
                int i = 0;
                for (TradeElectricityPriceDo tradeElectricityPriceDo : tradeElectricityPriceDos) {
                    tradeElectricityPriceDo.setExecuteStartDate(Timestamp.valueOf(executeStartDate));
                    tradeElectricityPriceDo.setExecuteEndDate(Timestamp.valueOf(executeEneDate));
                    tradeElectricityPriceDo.setNumber(i++);
                    tradeElectricityPriceMapper.insert(tradeElectricityPriceDo);
                }
                return ServiceResult.success(executeStartDate + "~" + executeEneDate + "工商业代理差价文件导入成功！");
            } else {
                return ServiceResult.success(executeStartDate + "~" + executeEneDate + "工商业代理差价数据已存在！");
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return ServiceResult.error(500, "工商业代理差价文件导入失败！");
        }
    }


    public ServiceResult greenPower(String month, String Cookie, String Currentroute) {
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("Cookie", Cookie);
        tokenMap.put("Currentroute", Currentroute);
        try {
            //第一次取数据
            String greenPowerParam = getGreenPowerParam(month, 1);
            String FirstResult = HttpUtil.httpPostUtil(green_power_url, greenPowerParam, tokenMap, 60, 60, true);
            Integer total = (Integer) JSONObject.parseObject(FirstResult).getJSONObject("data").get("total");
            log.info(month + "绿电分解合同总数：" + total);
            int i = total / 5000;
            if (i > 0) {
                List<TradeGreenPowerHnDo> greemPowerResult = getGreemPowerResult(FirstResult);

                //j=2表示从第二页开始循环取数
                for (int j = 2; j < i + 2; j++) {
                    log.info("第" + j + "次请求！");
                    String greenPowerParamByPage = getGreenPowerParam(month, j);
                    String resultByPage = HttpUtil.httpPostUtil(green_power_url, greenPowerParamByPage, tokenMap, 60, 60, true);
                    List<TradeGreenPowerHnDo> greemPowerResultByPage = getGreemPowerResult(resultByPage);
                    greemPowerResult.addAll(greemPowerResultByPage);
                }
                log.info("greemPowerResult:" + greemPowerResult.size());
                saveGreenPowerResult(greemPowerResult);
                return ServiceResult.success(month + "绿电分解合同导入成功！");
            } else {
                List<TradeGreenPowerHnDo> greemPowerResult = getGreemPowerResult(FirstResult);
                saveGreenPowerResult(greemPowerResult);
                return ServiceResult.success(month + "绿电分解合同导入成功！");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ServiceResult.error(500, month + "绿电分解合同导入失败！");

        }

    }

    private static String getGreenPowerParam(String month, Integer pageNum) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mktMonth", month);
        paramMap.put("consname", "");
        paramMap.put("consno", "");
        paramMap.put("contractName", "");
        paramMap.put("confirmStatus", "");
        GreenPowerPage greenPowerPage = new GreenPowerPage();
        greenPowerPage.setPageNum(pageNum);

        paramMap.put("page", JSON.parseObject(JSON.toJSONString(greenPowerPage), Map.class));
        String paramJsonString = JSON.toJSONString(paramMap, SerializerFeature.WriteMapNullValue);
        return paramJsonString;
    }

    private static String getParam(String tradeseqId, String tradetypeId, String subTradetypeCode, Integer pageNum) {
        Map<String, Map> paramMap = new HashMap<>();
        DataParamVo dataParamVo = new DataParamVo();
        dataParamVo.setTradeseqId(tradeseqId);
        dataParamVo.setTradetypeId(tradetypeId);
        dataParamVo.setSubTradetypeCode(subTradetypeCode);

        PageInfoVo pageInfoVo = new PageInfoVo();
        pageInfoVo.setPageNum(pageNum);
        paramMap.put("data", JSON.parseObject(JSON.toJSONString(dataParamVo), Map.class));
        paramMap.put("pageInfo", JSON.parseObject(JSON.toJSONString(pageInfoVo), Map.class));
        String paramJsonString = JSON.toJSONString(paramMap, SerializerFeature.WriteMapNullValue);
        return paramJsonString;
    }

    private List<TradeResultZcqDo> getTradeResult(String result, String tradeTypeCode) {
        List<TradeResultZcqDo> tradeResultZcqDos = null;
        String data;
        try {
            data = JSONObject.parseObject(result).getJSONObject("data").getString("list");
            tradeResultZcqDos = JSONObject.parseArray(data, TradeResultZcqDo.class);
            tradeResultZcqDos.stream().forEach((TradeResultZcqDo) -> TradeResultZcqDo.setTradetypeId(tradeTypeCode));
        } catch (NullPointerException e) {
            log.error(e.getMessage(), e);
        }
        return tradeResultZcqDos;
    }


    private List<TradeGreenPowerHnDo> getGreemPowerResult(String greenPowerResult) {
        String data = JSONObject.parseObject(greenPowerResult).getJSONObject("data").getString("list");
        List<TradeGreenPowerHnDo> tradeGreenPowerHnDos = JSONObject.parseArray(data, TradeGreenPowerHnDo.class);
        return tradeGreenPowerHnDos;
    }

    private void saveTradeResult(List<TradeResultZcqDo> resultList) {
        QueryWrapper<TradeResultZcqDo> resultZcqDoQueryWrapper = new QueryWrapper<>();
        resultZcqDoQueryWrapper.eq("tradeseq_id", resultList.get(0).getTradeseqId());
        tradeResultZcqMapper.delete(resultZcqDoQueryWrapper);
        List<List<TradeResultZcqDo>> partition = Lists.partition(resultList, 5000);
        log.info("partition = " + partition.size());
        int i = 1;
        for (List<TradeResultZcqDo> tradeResultZcqDos : partition) {
            log.info("第 " + i++ + "次插入数据！");
            tradeResultZcqMapper.insertBatch(tradeResultZcqDos);
        }
    }


    private void saveGreenPowerResult(List<TradeGreenPowerHnDo> greemPowerResult) {
        QueryWrapper<TradeGreenPowerHnDo> greenPowerHnDoQueryWrapper = new QueryWrapper<>();
        greenPowerHnDoQueryWrapper.eq("mkt_month", greemPowerResult.get(0).getMktMonth());
        tradeGreenPowerHnMapper.delete(greenPowerHnDoQueryWrapper);
        List<List<TradeGreenPowerHnDo>> partition = Lists.partition(greemPowerResult, 5000);
        log.info("partition = " + partition.size());
        int i = 1;
        for (List<TradeGreenPowerHnDo> tradeGreenPowerHnDos : partition) {
            log.info("第 " + i++ + "次插入数据！");
            tradeGreenPowerHnMapper.insertBatch(tradeGreenPowerHnDos);
        }
    }

    private Map<String, String> getAreaInfo(String area) {

        Map<String, String> areaInfoPathMaps = null;
        if (area.equals("gs")) {
            areaInfoPathMaps = tradeInterfaceUrlConfig.getGsPathMaps();
        } else if (area.equals("hn")) {
            areaInfoPathMaps = tradeInterfaceUrlConfig.getHnPathMaps();
        }
        return areaInfoPathMaps;
    }


    public ServiceResult getSpotTradeByDateRange(String startDate, String endDate, String cookie, String currentroute, String membersType, String unitId) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);

            Map<String, String> headMap = new HashMap<>();
            headMap.put("Cookie", cookie);
            headMap.put("Currentroute", currentroute);

            List<Map> allData = new java.util.ArrayList<>();
            int successCount = 0;

            // 循环处理每一天
            for (Date currentDate = start; !currentDate.after(end); ) {
                String dateStr = sdf.format(currentDate);

                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("dayStr", dateStr);
                paramMap.put("membersType", membersType);
                paramMap.put("unitId", unitId);

                try {
                    String result = HttpUtil.httpPostUtil(querySpotTrade, JSONObject.toJSONString(paramMap), headMap, 60, 60, true);
                    String data = JSONObject.parseObject(result).getJSONObject("data").getString("spotPointCurveData");
                    List<Map> dayData = JSONObject.parseArray(data, Map.class);

                    if (dayData != null && !dayData.isEmpty()) {
                        saveMlTradeCurveData(dayData, unitId, "7", dateStr, "电能量申报数据");
                        allData.addAll(dayData);
                        successCount++;
                    }

                    log.info("处理日期 {} 的数据成功", dateStr);
                } catch (Exception e) {
                    log.error("处理日期 {} 的数据失败: {}", dateStr, e.getMessage());
                }

                // 移动到下一天
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(currentDate);
                cal.add(java.util.Calendar.DAY_OF_MONTH, 1);
                currentDate = cal.getTime();
            }

            return ServiceResult.success("成功处理 " + successCount + " 天的数据，共 " + allData.size() + " 条记录");

        } catch (ParseException e) {
            log.error("日期解析失败: {}", e.getMessage());
            return ServiceResult.error(400, "日期格式错误，请使用 yyyy-MM-dd 格式");
        } catch (Exception e) {
            log.error("批量处理数据失败: {}", e.getMessage());
            return ServiceResult.error(500, "处理失败: " + e.getMessage());
        }
    }

    public ServiceResult saveMlTradeCurveData(List<Map> dataList, String unitId, String caseType, String dataTime, String dataType) {
        try {

            if (dataList == null || dataList.isEmpty()) {
                return ServiceResult.error(400, "数据为空");
            }

            // 创建实体对象
            MlTradeCurveEntity entity = new MlTradeCurveEntity();
            entity.setUnitId(unitId);
            entity.setCaseType(new BigDecimal(caseType));
            entity.setDataType(dataType);

            // 解析日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date parsedDate = sdf.parse(dataTime);
            entity.setDataTime(parsedDate);

            // 计算总和
            BigDecimal totalSum = BigDecimal.ZERO;

            // 遍历数据，根据seq设置对应的value字段
            for (Map<String, Object> item : dataList) {
                Integer seq = (Integer) item.get("seq");
                String energyDecodeStr = (String) item.get("energyDecode");

                if (seq != null && energyDecodeStr != null && seq >= 1 && seq <= 96) {
                    BigDecimal energyDecode = new BigDecimal(energyDecodeStr);
                    setValueBySeq(entity, seq, energyDecode);
                    totalSum = totalSum.add(energyDecode);
                }
            }

            // 设置总和
            entity.setValuesum(totalSum);
            entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));

            QueryWrapper<MlTradeCurveEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("unit_id", unitId).eq("case_type", caseType).eq("data_time", dataTime).eq("data_type", dataType);
            if (mlTradeCurveMapper.selectCount(queryWrapper) > 0) {
                mlTradeCurveMapper.update(entity, queryWrapper);
            } else {
                mlTradeCurveMapper.insert(entity);
            }
            log.info("多边交易曲线数据保存成功，unitId: {}, caseType: {}, dataTime: {}, dataType: {}",
                    unitId, caseType, dataTime, dataType);

            return ServiceResult.success("多边交易曲线数据保存成功");

        } catch (ParseException e) {
            log.error("日期解析失败: {}", e.getMessage(), e);
            return ServiceResult.error(400, "日期格式错误");
        } catch (Exception e) {
            log.error("保存多边交易曲线数据失败: {}", e.getMessage(), e);
            return ServiceResult.error(500, "保存数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据seq设置对应的value字段
     */
    private void setValueBySeq(MlTradeCurveEntity entity, Integer seq, BigDecimal value) {
        switch (seq) {
            case 1:
                entity.setValue1(value);
                break;
            case 2:
                entity.setValue2(value);
                break;
            case 3:
                entity.setValue3(value);
                break;
            case 4:
                entity.setValue4(value);
                break;
            case 5:
                entity.setValue5(value);
                break;
            case 6:
                entity.setValue6(value);
                break;
            case 7:
                entity.setValue7(value);
                break;
            case 8:
                entity.setValue8(value);
                break;
            case 9:
                entity.setValue9(value);
                break;
            case 10:
                entity.setValue10(value);
                break;
            case 11:
                entity.setValue11(value);
                break;
            case 12:
                entity.setValue12(value);
                break;
            case 13:
                entity.setValue13(value);
                break;
            case 14:
                entity.setValue14(value);
                break;
            case 15:
                entity.setValue15(value);
                break;
            case 16:
                entity.setValue16(value);
                break;
            case 17:
                entity.setValue17(value);
                break;
            case 18:
                entity.setValue18(value);
                break;
            case 19:
                entity.setValue19(value);
                break;
            case 20:
                entity.setValue20(value);
                break;
            case 21:
                entity.setValue21(value);
                break;
            case 22:
                entity.setValue22(value);
                break;
            case 23:
                entity.setValue23(value);
                break;
            case 24:
                entity.setValue24(value);
                break;
            case 25:
                entity.setValue25(value);
                break;
            case 26:
                entity.setValue26(value);
                break;
            case 27:
                entity.setValue27(value);
                break;
            case 28:
                entity.setValue28(value);
                break;
            case 29:
                entity.setValue29(value);
                break;
            case 30:
                entity.setValue30(value);
                break;
            case 31:
                entity.setValue31(value);
                break;
            case 32:
                entity.setValue32(value);
                break;
            case 33:
                entity.setValue33(value);
                break;
            case 34:
                entity.setValue34(value);
                break;
            case 35:
                entity.setValue35(value);
                break;
            case 36:
                entity.setValue36(value);
                break;
            case 37:
                entity.setValue37(value);
                break;
            case 38:
                entity.setValue38(value);
                break;
            case 39:
                entity.setValue39(value);
                break;
            case 40:
                entity.setValue40(value);
                break;
            case 41:
                entity.setValue41(value);
                break;
            case 42:
                entity.setValue42(value);
                break;
            case 43:
                entity.setValue43(value);
                break;
            case 44:
                entity.setValue44(value);
                break;
            case 45:
                entity.setValue45(value);
                break;
            case 46:
                entity.setValue46(value);
                break;
            case 47:
                entity.setValue47(value);
                break;
            case 48:
                entity.setValue48(value);
                break;
            case 49:
                entity.setValue49(value);
                break;
            case 50:
                entity.setValue50(value);
                break;
            case 51:
                entity.setValue51(value);
                break;
            case 52:
                entity.setValue52(value);
                break;
            case 53:
                entity.setValue53(value);
                break;
            case 54:
                entity.setValue54(value);
                break;
            case 55:
                entity.setValue55(value);
                break;
            case 56:
                entity.setValue56(value);
                break;
            case 57:
                entity.setValue57(value);
                break;
            case 58:
                entity.setValue58(value);
                break;
            case 59:
                entity.setValue59(value);
                break;
            case 60:
                entity.setValue60(value);
                break;
            case 61:
                entity.setValue61(value);
                break;
            case 62:
                entity.setValue62(value);
                break;
            case 63:
                entity.setValue63(value);
                break;
            case 64:
                entity.setValue64(value);
                break;
            case 65:
                entity.setValue65(value);
                break;
            case 66:
                entity.setValue66(value);
                break;
            case 67:
                entity.setValue67(value);
                break;
            case 68:
                entity.setValue68(value);
                break;
            case 69:
                entity.setValue69(value);
                break;
            case 70:
                entity.setValue70(value);
                break;
            case 71:
                entity.setValue71(value);
                break;
            case 72:
                entity.setValue72(value);
                break;
            case 73:
                entity.setValue73(value);
                break;
            case 74:
                entity.setValue74(value);
                break;
            case 75:
                entity.setValue75(value);
                break;
            case 76:
                entity.setValue76(value);
                break;
            case 77:
                entity.setValue77(value);
                break;
            case 78:
                entity.setValue78(value);
                break;
            case 79:
                entity.setValue79(value);
                break;
            case 80:
                entity.setValue80(value);
                break;
            case 81:
                entity.setValue81(value);
                break;
            case 82:
                entity.setValue82(value);
                break;
            case 83:
                entity.setValue83(value);
                break;
            case 84:
                entity.setValue84(value);
                break;
            case 85:
                entity.setValue85(value);
                break;
            case 86:
                entity.setValue86(value);
                break;
            case 87:
                entity.setValue87(value);
                break;
            case 88:
                entity.setValue88(value);
                break;
            case 89:
                entity.setValue89(value);
                break;
            case 90:
                entity.setValue90(value);
                break;
            case 91:
                entity.setValue91(value);
                break;
            case 92:
                entity.setValue92(value);
                break;
            case 93:
                entity.setValue93(value);
                break;
            case 94:
                entity.setValue94(value);
                break;
            case 95:
                entity.setValue95(value);
                break;
            case 96:
                entity.setValue96(value);
                break;
            default:
                log.warn("无效的seq值: {}", seq);
                break;
        }
    }

    /**
     * 获取sessionId
     */
    public static String getSessionId(String text) {
        String pattern = "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(text);
        if (m.find()) {
            return m.group();
        } else {
            throw new RuntimeException("未找到sessionId");
        }
    }

    public ServiceResult autoDownload(WebDriver driver, String date, String type) {
        //谷歌浏览器自动化下载
        try {
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));

            Thread.sleep(2000);
            WebElement element1 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[3]/div/div[3]/div[1]/div/div/div[1]/div/div/div[2]/div[3]/div/div[8]/div[1]/span[2]/span")));
            element1.click();
            Thread.sleep(2000);
            WebElement element2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[3]/div/div[3]/div[1]/div/div/div[1]/div[1]/div/div[2]/div[3]/div/div[8]/div[2]/div[3]/div/span[2]/span")));
            element2.click();
            String windowHandle1 = driver.getWindowHandle();
            Thread.sleep(2000);
            for (String windowHandle : driver.getWindowHandles()) {
                System.out.println("windowHandle = " + windowHandle);
                if (!windowHandle.equals(windowHandle1)) {
                    driver.switchTo().window(windowHandle);
                }
            }
            Thread.sleep(3000);
            WebElement element3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/section/section[1]/main/div[3]/div[1]/div/span[2]/i")));
            element3.click();
            WebElement element4 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/section/section[1]/main/div[3]/div[1]/div/span[2]/i")));
            element4.click();
            Thread.sleep(3000);
            WebElement element5 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div/section/section[1]/main/div[3]/div[1]/div/div/div/div/ul/li[9]")));
            element5.click();
            Thread.sleep(3000);
            WebElement element6 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[1]/div[1]/span[2]/span[1]/span")));
            element6.click();
            Thread.sleep(3000);
            WebElement element7 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div/span[2]")));
            element7.click();
            Thread.sleep(3000);
            WebElement element8 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div/span[2]/span[1]/span")));
            element8.click();
            Thread.sleep(3000);

            if (type.equals("日前")) {
                //日前用户侧24点出清电量
                WebElement targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[12]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[3]")));
                queryButton.click();
                Thread.sleep(2000); // 等待查询结果加载

                //导出
                WebElement exportButton = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                exportButton.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton.click();
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton.click();
                log.info("日前用户侧24点出清电量，等待下载...");
                Thread.sleep(3000); // 等待下载开始

                //日前节点边际电价 开始
                //切回主页面
                driver.switchTo().defaultContent();
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[3]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[5]/div/span")));
                queryButton2.click();
                Thread.sleep(5000); // 等待查询结果加载

                //导出
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton2.click();
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton2.click();
                log.info("日前节点边际电价，等待下载...");
                Thread.sleep(3000); // 等待下载开始
                //日前节点边际电价 结束

                //日前各类型电源出清电量 开始
                driver.switchTo().defaultContent();
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[8]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[3]/div/span")));
                queryButton3.click();
                Thread.sleep(5000); // 等待查询结果加载

                //导出
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton3.click();
                log.info("日前各类型电源出清电量，等待下载...");
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton3.click();
                //日前各类型电源出清电量 结束

                //日前用户侧统一结算价格 开始
                driver.switchTo().defaultContent();
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[10]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton4 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[3]/div/span")));
                queryButton4.click();
                Thread.sleep(5000); // 等待查询结果加载

                //导出
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton4 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton4.click();
                log.info("日前用户侧统一结算价格，等待下载...");
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton4 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton4.click();
                //日前用户侧统一结算价格 结束
            } else {
                //实时节点边际电价 开始
                //切回主页面
                driver.switchTo().defaultContent();
                WebElement targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[4]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[5]/div/span")));
                queryButton2.click();
                Thread.sleep(5000); // 等待查询结果加载

                //导出
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton2.click();
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton2 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton2.click();
                log.info("日前节点边际电价，等待下载...");
                Thread.sleep(3000); // 等待下载开始
                //实时节点边际电价 结束

                //实时各类型电源出清电量 开始
                driver.switchTo().defaultContent();
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div/section/section[2]/aside/section/main/div[2]/div[1]/div[2]/div[2]/div[2]/div[5]/div[2]/div[8]/div/span[2]/span/span")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(3000);
                // 自动化填充日期
                fillDateInput(driver, date);
                // 点击查询按钮
                WebElement queryButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[3]/div/span")));
                queryButton3.click();
                Thread.sleep(5000); // 等待查询结果加载

                //导出
                targetElement = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("/html/body/div[2]/table/tbody/tr/td[11]/div/div")));
                ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", targetElement);
                wait.until(ExpectedConditions.elementToBeClickable(targetElement));
                targetElement.click();
                Thread.sleep(1000);
                WebElement exportConfirmButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[4]/div[2]")));
                exportConfirmButton3.click();
                log.info("日前各类型电源出清电量，等待下载...");
                Thread.sleep(3000);
                //原样导出
                WebElement exportOriginalButton3 = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[5]/div")));
                exportOriginalButton3.click();
                //实时各类型电源出清电量 结束
            }

            // 等待下载完成，然后尝试从临时目录读取文件
            log.info("开始等待下载完成...");
            //判断driver浏览器里没有正在下载的文件
            waitForDownloadsToComplete();
            log.info("所有下载已完成");

            getExcelData(downloadPath);
//            driver.quit();

        } catch (Exception e) {
//            driver.quit();
            log.error("自动化下载过程中发生错误: " + e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            driver.quit();
        }
        return ServiceResult.success("自动化下载完成");
    }

    /**
     * 等待所有下载完成（通过检查.crdownload临时文件）
     */
    private void waitForDownloadsToComplete() {
        try {
            Thread.sleep(2000);
            int maxWaitTime = 60; // 最大等待时间（秒）
            int checkInterval = 1; // 检查间隔（秒）
            int waitedTime = 0;

            log.info("开始检查下载目录: {}", downloadPath);

            while (waitedTime < maxWaitTime) {
                File downloadDirectory = new File(downloadPath);
                if (!downloadDirectory.exists()) {
                    log.warn("下载目录不存在: {}", downloadPath);
                    Thread.sleep(checkInterval * 1000);
                    waitedTime += checkInterval;
                    continue;
                }

                // 检查是否有.crdownload临时文件
                File[] crdownloadFiles = downloadDirectory.listFiles((dir, name) ->
                        name.toLowerCase().endsWith(".crdownload"));

                if (crdownloadFiles == null || crdownloadFiles.length == 0) {
                    log.info("没有发现正在下载的文件，下载已完成");
                    break;
                }

                log.info("发现 {} 个正在下载的文件，继续等待...", crdownloadFiles.length);
                Thread.sleep(checkInterval * 1000);
                waitedTime += checkInterval;
            }

            if (waitedTime >= maxWaitTime) {
                log.warn("等待下载完成超时，可能仍有文件在下载");
            }

        } catch (Exception e) {
            log.error("检查下载状态时发生错误: " + e.getMessage(), e);
        }
    }

    private void getExcelData(String fload) {
        try {
            File file = new File(fload);
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    if (f.isFile() && f.getName().endsWith(".xlsx") && (f.getName().contains("日前用户侧24点出清电量") || f.getName().contains("日前节点边际电价") || f.getName().contains("日前各类型电源出清电量及台数")
                            || f.getName().contains("日前用户侧统一结算价格")|| f.getName().contains("实时节点边际电价")|| f.getName().contains("实时各类型电源出清电量"))) {
                        log.info("找到Excel文件: {}", f.getName());
                        processExcelFile(f);
                        f.delete();
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理Excel文件夹失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理Excel文件并转换为对象
     *
     * @param excelFile Excel文件
     * @return 是否处理成功
     */
    private boolean processExcelFile(File excelFile) {
        try {
            log.info("开始处理Excel文件: {}", excelFile.getName());

            // 使用Apache POI读取Excel文件
            Workbook workbook = null;
            try (FileInputStream fis = new FileInputStream(excelFile)) {
                if (excelFile.getName().endsWith(".xlsx")) {
                    workbook = new XSSFWorkbook(fis);
                } else if (excelFile.getName().endsWith(".xls")) {
                    workbook = new HSSFWorkbook(fis);
                } else {
                    log.error("不支持的文件格式: {}", excelFile.getName());
                    return false;
                }
                List<Map<String, Object>> list = ExcelUtil.readExcelToListMapWithDateFormat(excelFile.getAbsolutePath(), 0, true, "yyyy-MM-dd");
                //转成对象 需要转换
                if (excelFile.getName().contains("日前用户侧24点出清电量")) {
                    for (Map<String, Object> map : list) {
                        map.put("dataTime", map.get("日期"));
                        map.put("dataType", map.get("类型"));
                        excelColumnParse24(map);
                    }
                    List<MlTradeCqdlEntity> list1 = JSONObject.parseArray(JSONObject.toJSONString(list), MlTradeCqdlEntity.class);
                    //list1 里的dataType不一样,要挨个判断数据库有没有同日期类型的
                    for (MlTradeCqdlEntity entity : list1) {
                        QueryWrapper<MlTradeCqdlEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("data_time", entity.getDataTime()).eq("data_type", entity.getDataType());
                        if (mlTradeCqdlMapper.selectCount(queryWrapper) > 0) {
                            log.info("数据库已有数据，无需重复插入");
                            continue;
                        }
                        mlTradeCqdlMapper.insert(entity);
                    }
                }

                if (excelFile.getName().contains("日前节点边际电价")) {
                    for (Map<String, Object> map : list) {
                        map.put("dataTime", map.get("日期"));
                        map.put("dataType", map.get("数据类型"));
                        map.put("caseType", "250");
                        map.put("unitName", map.get("单元名称"));
                        excelColumnParse96(map);
                    }
                    //判断数据库日期类型有无数据
                    QueryWrapper<MlTradeCurveEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("data_time", list.get(0).get("dataTime")).eq("case_type", list.get(0).get("caseType"));
                    if (mlTradeCurveMapper.selectCount(queryWrapper) > 0) {
                        log.info("数据库已有数据，无需重复插入");
                        return true;
                    }
                    List<MlTradeCurveEntity> list1 = JSONObject.parseArray(JSONObject.toJSONString(list), MlTradeCurveEntity.class);
                    mlTradeCurveMapper.insertBatch(list1);
                }

                if (excelFile.getName().contains("日前各类型电源出清电量及台数")) {
                    for (Map<String, Object> map : list) {
                        map.put("dataTime", map.get("日期"));
                        map.put("dataType", map.get("数据类型"));
                        map.put("caseType", "251");
                        map.put("unitName", map.get("开机台数"));
                        excelColumnParse96(map);
                    }
                    //判断数据库日期类型有无数据
                    QueryWrapper<MlTradeCurveEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("data_time", list.get(0).get("dataTime")).eq("case_type", list.get(0).get("caseType"));
                    if (mlTradeCurveMapper.selectCount(queryWrapper) > 0) {
                        log.info("数据库已有数据，无需重复插入");
                        return true;
                    }
                    List<MlTradeCurveEntity> list1 = JSONObject.parseArray(JSONObject.toJSONString(list), MlTradeCurveEntity.class);
                    mlTradeCurveMapper.insertBatch(list1);
                }
                if (excelFile.getName().contains("日前用户侧统一结算价格")) {
                    Map<String, Object> result = convertToSingleRecord(list);
                    MlTradeCqdlEntity list1 = JSONObject.parseObject(JSONObject.toJSONString(result), MlTradeCqdlEntity.class);
                    //根据日期，类型判断数据库有没有相同数据
                    QueryWrapper<MlTradeCqdlEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("data_time", list1.getDataTime()).eq("data_type", list1.getDataType());
                    if (mlTradeCqdlMapper.selectCount(queryWrapper) > 0) {
                        log.info("数据库已有数据，无需重复插入");
                        return true;
                    }
                    mlTradeCqdlMapper.insert(list1);
                }
                if (excelFile.getName().contains("实时节点边际电价")) {
                    for (Map<String, Object> map : list) {
                        map.put("dataTime", map.get("日期"));
                        map.put("dataType", map.get("数据类型"));
                        map.put("caseType", "252");
                        map.put("unitName", map.get("单元名称"));
                        excelColumnParse96(map);
                    }
                    List<MlTradeCurveEntity> list1 = JSONObject.parseArray(JSONObject.toJSONString(list), MlTradeCurveEntity.class);
                    //判断数据库日期类型有无数据 没有就新增，有就修改
                    QueryWrapper<MlTradeCurveEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("data_time", list.get(0).get("dataTime")).eq("case_type", list.get(0).get("caseType"));
                    if (mlTradeCurveMapper.selectCount(queryWrapper) > 0) {
                        mlTradeCurveMapper.updateBatch(list1,2);
                    }
                    mlTradeCurveMapper.insertBatch(list1);

                }
                excelFile.delete();
                return true;

            } finally {
                if (workbook != null) {
                    workbook.close();
                }
            }
        } catch (Exception e) {
            excelFile.delete();
            log.error("处理Excel文件失败: {}", e.getMessage(), e);
            return false;
        }
    }


    /**
     * 填充日期输入框
     *
     * @param driver WebDriver实例
     * @param date   要填充的日期字符串，格式如：2025-07-18
     */
    private void fillDateInput(WebDriver driver, String date) {
        try {
            log.info("开始填充日期: " + date);

            // 等待iframe加载并切换到iframe
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));

            // 尝试多种方式定位iframe
            WebElement iframe = null;

            // 方式1：通过id定位iframe
            iframe = wait.until(ExpectedConditions.presenceOfElementLocated(By.tagName("iframe")));
            log.info("找到iframe元素");


            if (iframe != null) {
                // 切换到iframe
                driver.switchTo().frame(iframe);
                log.info("已切换到iframe");

                // 在iframe中查找日期输入框
                WebElement dateInput = null;

                // 尝试多种方式定位日期输入框
                dateInput = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[1]/div[1]/div[2]/div/div[2]/div[1]/input")));
                log.info("在iframe中找到日期输入框");
                if (dateInput != null) {
                    // 填充日期
                    dateInput.click();
                    Thread.sleep(500);
                    dateInput.sendKeys(Keys.CONTROL + "a");
                    Thread.sleep(200);
                    dateInput.sendKeys(Keys.DELETE);
                    Thread.sleep(300);

                    String currentValue = dateInput.getAttribute("value");
                    if (currentValue != null && !currentValue.isEmpty()) {
                        // 使用JavaScript直接清空
                        ((JavascriptExecutor) driver).executeScript("arguments[0].value = '';", dateInput);
                        Thread.sleep(200);

                        // 或者使用多次退格键
                        for (int i = 0; i < currentValue.length(); i++) {
                            dateInput.sendKeys(Keys.BACK_SPACE);
                            Thread.sleep(50);
                        }
                    }

                    // 方式3：再次确认清空
                    dateInput.clear();
                    Thread.sleep(300);


                    for (char c : date.toCharArray()) {
                        dateInput.sendKeys(String.valueOf(c));
                        Thread.sleep(50);
                    }

                    dateInput.sendKeys(Keys.TAB);
                    Thread.sleep(500);
                    log.info("日期填充完成: " + date);
                } else {
                    throw new RuntimeException("在iframe中无法找到日期输入框");
                }

                // 切换回主页面
//                driver.switchTo().defaultContent();

            } else {
                throw new RuntimeException("无法找到iframe");
            }

        } catch (Exception e) {
            // 确保切换回主页面
            try {
//                driver.switchTo().defaultContent();
            } catch (Exception ex) {
                // 忽略
            }
            log.error("填充日期时发生错误: " + e.getMessage(), e);
            throw new RuntimeException("填充日期失败: " + e.getMessage(), e);
        }
    }


    //这里的:也有可能是：
    //96点数据转换
    void excelColumnParse96(Map<String, Object> map) {
        for (int i = 1; i <= 96; i++) {
            int hour = (i - 1) / 4;
            int minute = (i - 1) % 4 * 15;
            String colonKey = String.format("%02d:%02d", hour, minute);
            String fullWidthKey = String.format("%02d：%02d", hour, minute);
            if (map.get(colonKey) == null) {
                map.put(colonKey, map.get(fullWidthKey));
            }
            map.put("value" + i, map.get(colonKey));
        }
    }


    //96点数据转换
    void excelColumnParse24(Map<String, Object> map) {
        map.put("value1", map.get("1:00"));
        map.put("value2", map.get("2:00"));
        map.put("value3", map.get("3:00"));
        map.put("value4", map.get("4:00"));
        map.put("value5", map.get("5:00"));
        map.put("value6", map.get("6:00"));
        map.put("value7", map.get("7:00"));
        map.put("value8", map.get("8:00"));
        map.put("value9", map.get("9:00"));
        map.put("value10", map.get("10:00"));
        map.put("value11", map.get("11:00"));
        map.put("value12", map.get("12:00"));
        map.put("value13", map.get("13:00"));
        map.put("value14", map.get("14:00"));
        map.put("value15", map.get("15:00"));
        map.put("value16", map.get("16:00"));
        map.put("value17", map.get("17:00"));
        map.put("value18", map.get("18:00"));
        map.put("value19", map.get("19:00"));
        map.put("value20", map.get("20:00"));
        map.put("value21", map.get("21:00"));
        map.put("value22", map.get("22:00"));
        map.put("value23", map.get("23:00"));
        map.put("value24", map.get("24:00"));
    }


    /**
     * 将24小时电价数据从行转列，转换为包含value1到value24的单条记录
     *
     * @param priceList 原始电价数据列表
     * @return 转换后的Map，包含日期、名称和value1-value24字段
     */
    public static Map<String, Object> convertToSingleRecord(List<Map<String, Object>> priceList) {
        if (priceList == null || priceList.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, Object> result = new HashMap<>();

        // 获取基础信息（从第一条记录）
        Map<String, Object> firstRecord = priceList.get(0);
        result.put("dataTime", firstRecord.get("日期"));
        result.put("dataType", firstRecord.get("名称"));

        // 按时刻排序并转换为value1-value24
        List<Map<String, Object>> sortedList = priceList.stream()
                .sorted((a, b) -> {
                    String timeA = (String) a.get("时刻");
                    String timeB = (String) b.get("时刻");
                    return timeA.compareTo(timeB);
                })
                .collect(Collectors.toList());

        // 填充value1到value24
        for (int i = 0; i < sortedList.size() && i < 24; i++) {
            String valueKey = "value" + (i + 1);
            Object price = sortedList.get(i).get("价格");
            result.put(valueKey, price);
        }

        return result;
    }
}