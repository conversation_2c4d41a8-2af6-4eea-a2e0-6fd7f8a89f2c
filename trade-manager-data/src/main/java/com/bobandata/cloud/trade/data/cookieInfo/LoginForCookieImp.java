package com.bobandata.cloud.trade.data.cookieInfo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.dto.CookieInfoMapper;
import com.bobandata.cloud.trade.data.entity.CookieInfoEntity;
import com.bobandata.cloud.trade.data.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.Cookie;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author： BYC
 * @create： 2024/7/19 14:14
 * @description：TODO
 */

@Slf4j
@Service
@EnableScheduling
public class LoginForCookieImp {


    private final static String huNan_getMenu_url = "https://pmos.hn.sgcc.cn/px-trade-extranet/trTradeType/getMenu";
    private final static String beiJing_getMenu_url = "https://pmos.sgcc.com.cn/px-trade-extranet/trTradeType/getMenu";

    private final static String huNan_dashboard_url = "https://pmos.hn.sgcc.cn/#/dashboard";
    private final static String beiJijng_dashboard_url = "https://pmos.sgcc.com.cn/#/dashboard";

    private final static String CurrentRoute = "/pxf-trade-extranet/myTransaction/TradeQuery?date=";

    @Autowired
    private CookieInfoMapper cookieInfoMapper;

    @Autowired
    private LoginForSelenium loginForSelenium;

    @Value("${chromeDriverUrl}")
    private String chromeDriverUrl;

    @Value("${chromeUrl}")
    private String chromeUrl;

    public ServiceResult loginForCookie(String userName, String pwd, String area) {

        if (area.equals("hunan")) {
            ServiceResult loginForInfo = loginForInfo(userName, pwd, area);
            return loginForInfo;
        }
        if (area.equals("beijing")) {
            ServiceResult loginForInfo = loginForInfo(userName, pwd, area);
            return loginForInfo;
        }
        return ServiceResult.error(1, userName + "无此地区数据" + area);
    }


    private ServiceResult loginForInfo(String userName, String pwd, String area) {


        QueryWrapper<CookieInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_Name", userName);
        queryWrapper.eq("user_pwd", pwd);
        queryWrapper.eq("area", area);

        CookieInfoEntity cookieInfoEntity = cookieInfoMapper.selectOne(queryWrapper);
        if (cookieInfoEntity == null) {
            return ServiceResult.error(500, userName + "无此地区数据:" + area);
        }
        String xTicket = cookieInfoEntity.getXTicket();
        String adminToken = cookieInfoEntity.getAdminToken();
        String currentRoute = cookieInfoEntity.getCurrentRoute();
        String grayTag = cookieInfoEntity.getGrayTag();
        String clientTag = cookieInfoEntity.getClientTag();
        String sidebarStatus = cookieInfoEntity.getSidebarStatus();
        String xToken = cookieInfoEntity.getXToken();

        Map<String, String> paramMap = new HashMap<>();
        HashMap<String, String> haedMap = new HashMap<>();
        haedMap.put("X-Ticket", xTicket);
        haedMap.put("Admin-Token", adminToken);
        haedMap.put("CurrentRoute", currentRoute);
        haedMap.put("Gray-Tag", grayTag);
        haedMap.put("ClientTag", clientTag);
        haedMap.put("sidebarStatus", sidebarStatus);
        haedMap.put("X-Token", xToken);
        haedMap.put("CurrentRoute", CurrentRoute + System.currentTimeMillis());

        String message = null;
        String get_url = null;
        if (area.equals("hunan")) {
            String menuRes = HttpUtil.httpPostUtil(huNan_getMenu_url, paramMap.toString(), haedMap, 60, 60, true);
            message = JSONObject.parseObject(menuRes).getString("message");
            get_url = huNan_dashboard_url;
        } else if (area.equals("beijing")) {
            String menuRes = HttpUtil.httpPostUtil(beiJing_getMenu_url, paramMap.toString(), haedMap, 60, 60, true);
            message = JSONObject.parseObject(menuRes).getString("message");
            get_url = beiJijng_dashboard_url;
        } else {
            return ServiceResult.error(500, userName + "无此地区数据:" + area);
        }
        boolean isLogin = false;
        if (message.equals("Success")) {
            WebDriver driver = getCookie(xTicket, adminToken, currentRoute, grayTag, clientTag, sidebarStatus, xToken, area);
            driver.get(get_url);
            return ServiceResult.success(driver,userName + "登录" + area + "交易系统成功！");
        } else {
            try {
                isLogin = loginForSelenium.login(userName, pwd, area);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (isLogin == true) {
                return ServiceResult.success("登录" + area + "交易系统成功！");
            }
            return ServiceResult.error(500, "登录" + area + "交易系统失败！");
        }
    }

    private WebDriver getCookie(String xTicket, String adminToken, String currentRoute, String grayTag, String clientTag, String sidebarStatus, String xToken, String area) {
        Cookie cookie = new Cookie("X-Ticket", xTicket);
        Cookie cookie1 = new Cookie("Admin-Token", adminToken);
        Cookie cookie2 = new Cookie("CurrentRoute", currentRoute);
        Cookie cookie3 = new Cookie("Gray-Tag", grayTag);
        Cookie cookie4 = new Cookie("ClientTag", clientTag);
        Cookie cookie5 = new Cookie("sidebarStatus", sidebarStatus);
        Cookie cookie6 = new Cookie("X-Token", xToken);

        System.setProperty("webdriver.chrome.driver", chromeDriverUrl);
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--remote-allow-origins=*");

        // 配置下载目录
        Map<String, Object> prefs = new HashMap<>();
        prefs.put("download.default_directory", "/home/<USER>/Downloads");
        prefs.put("download.prompt_for_download", false);
        prefs.put("download.directory_upgrade", true);
        prefs.put("safebrowsing.enabled", true);
        prefs.put("profile.default_content_settings.popups", 0);
        prefs.put("profile.default_content_setting_values.automatic_downloads", 1);
        options.setExperimentalOption("prefs", prefs);

        // 添加更多 Chrome 参数
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--window-size=1920,1080");

        log.info("Chrome 下载配置: 目录=/home/<USER>/Downloads, 自动下载=启用");
                WebDriver driver = new ChromeDriver(options);
//        WebDriver driver = null;
//        try {
//            driver = new RemoteWebDriver(new URL(chromeUrl), options);
//        } catch (MalformedURLException e) {
//            if (driver != null) {
//                driver.quit();
//                log.info("driver is close!");
//            }
//            log.error(e.getMessage(), e);
//        }
//        finally {
//            if (driver != null) {
//                driver.quit();
//                log.info("driver is close!");
//            }
//        }
        if (area.equals("hunan")) {
            driver.get("https://pmos.hn.sgcc.cn/#/outNet");
        }
        if (area.equals("beijing")) {
            driver.get("https://pmos.sgcc.com.cn/#/outNet");
        }

        driver.manage().addCookie(cookie);
        driver.manage().addCookie(cookie1);
        driver.manage().addCookie(cookie2);
        driver.manage().addCookie(cookie3);
        driver.manage().addCookie(cookie4);
        driver.manage().addCookie(cookie5);
        driver.manage().addCookie(cookie6);
        return driver;
    }


    //    @Scheduled(cron = "${checkCron}")
    public void checkCookie() {
        List<CookieInfoEntity> cookieInfoEntities = cookieInfoMapper.selectList();
        for (CookieInfoEntity cookieInfoEntity : cookieInfoEntities) {
            String userName = cookieInfoEntity.getUserName();
            String userPwd = cookieInfoEntity.getUserPwd();

            String xTicket = cookieInfoEntity.getXTicket();
            String adminToken = cookieInfoEntity.getAdminToken();
            String currentRoute = cookieInfoEntity.getCurrentRoute();
            String grayTag = cookieInfoEntity.getGrayTag();
            String clientTag = cookieInfoEntity.getClientTag();
            String sidebarStatus = cookieInfoEntity.getSidebarStatus();
            String xToken = cookieInfoEntity.getXToken();
            String area = cookieInfoEntity.getArea();

            Map<String, String> paramMap = new HashMap<>();
            HashMap<String, String> haedMap = new HashMap<>();
            haedMap.put("X-Ticket", xTicket);
            haedMap.put("Admin-Token", adminToken);
            haedMap.put("CurrentRoute", currentRoute);
            haedMap.put("Gray-Tag", grayTag);
            haedMap.put("ClientTag", clientTag);
            haedMap.put("sidebarStatus", sidebarStatus);
            haedMap.put("X-Token", xToken);
            haedMap.put("CurrentRoute", CurrentRoute + System.currentTimeMillis());
            if (area.equals("gansu")) {
                String menuRes = HttpUtil.httpPostUtil(huNan_getMenu_url, paramMap.toString(), haedMap, 60, 60, true);
                String message = JSONObject.parseObject(menuRes).getString("message");

                boolean isLogin = false;
                if (message.equals("Success")) {
                    log.info("用户:" + userName + ";地区:" + area + "连接状态...在线");
                } else {
                    try {
                        isLogin = loginForSelenium.login(userName, userPwd, area);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    if (isLogin == true) {
                        log.info(userName + "登录" + area + "交易系统成功！");
                    }
                    log.info(userName + "登录" + area + "交易系统失败！");
                }
            } else if (area.equals("beijing")) {
                String menuRes = HttpUtil.httpPostUtil(beiJing_getMenu_url, paramMap.toString(), haedMap, 60, 60, true);
                String message = JSONObject.parseObject(menuRes).getString("message");


                boolean isLogin = false;
                if (message.equals("Success")) {

                    log.info("用户:" + userName + ";地区:" + area + "连接状态...在线");
                } else {
                    try {
                        isLogin = loginForSelenium.login(userName, userPwd, area);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    if (isLogin == true) {
                        log.info(userName + "登录" + area + "交易系统成功！");
                    }
                }
            }
        }
    }
}
