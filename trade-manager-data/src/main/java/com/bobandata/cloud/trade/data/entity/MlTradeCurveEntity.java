package com.bobandata.cloud.trade.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;


@Data
@TableName("mos_settle_mltrade_curve")
public class MlTradeCurveEntity implements Serializable {

	private static final long serialVersionUID =  9034312114797142884L;

	/**
	 * 单元ID
	 */
	private String unitId;

    /**
     * 单元ID
     */
    private String unitName;

	/**
	 * 案例类型（主键）
	 */
	private BigDecimal caseType;

	/**
	 * 数据时间（主键）
	 */
	private Date dataTime;

	/**
	 * 数据类型（主键）
	 */
	private String dataType;

	/**
	 * 数值1
	 */
	private BigDecimal value1;

	/**
	 * 数值2
	 */
	private BigDecimal value2;

	/**
	 * 数值3
	 */
	private BigDecimal value3;

	/**
	 * 数值4
	 */
	private BigDecimal value4;

	/**
	 * 数值5
	 */
	private BigDecimal value5;

	/**
	 * 数值6
	 */
	private BigDecimal value6;

	/**
	 * 数值7
	 */
	private BigDecimal value7;

	/**
	 * 数值8
	 */
	private BigDecimal value8;

	/**
	 * 数值9
	 */
	private BigDecimal value9;

	/**
	 * 数值10
	 */
	private BigDecimal value10;

	/**
	 * 数值11
	 */
	private BigDecimal value11;

	/**
	 * 数值12
	 */
	private BigDecimal value12;

	/**
	 * 数值13
	 */
	private BigDecimal value13;

	/**
	 * 数值14
	 */
	private BigDecimal value14;

	/**
	 * 数值15
	 */
	private BigDecimal value15;

	/**
	 * 数值16
	 */
	private BigDecimal value16;

	/**
	 * 数值17
	 */
	private BigDecimal value17;

	/**
	 * 数值18
	 */
	private BigDecimal value18;

	/**
	 * 数值19
	 */
	private BigDecimal value19;

	/**
	 * 数值20
	 */
	private BigDecimal value20;

	/**
	 * 数值21
	 */
	private BigDecimal value21;

	/**
	 * 数值22
	 */
	private BigDecimal value22;

	/**
	 * 数值23
	 */
	private BigDecimal value23;

	/**
	 * 数值24
	 */
	private BigDecimal value24;

	/**
	 * 数值25
	 */
	private BigDecimal value25;

	/**
	 * 数值26
	 */
	private BigDecimal value26;

	/**
	 * 数值27
	 */
	private BigDecimal value27;

	/**
	 * 数值28
	 */
	private BigDecimal value28;

	/**
	 * 数值29
	 */
	private BigDecimal value29;

	/**
	 * 数值30
	 */
	private BigDecimal value30;

	/**
	 * 数值31
	 */
	private BigDecimal value31;

	/**
	 * 数值32
	 */
	private BigDecimal value32;

	/**
	 * 数值33
	 */
	private BigDecimal value33;

	/**
	 * 数值34
	 */
	private BigDecimal value34;

	/**
	 * 数值35
	 */
	private BigDecimal value35;

	/**
	 * 数值36
	 */
	private BigDecimal value36;

	/**
	 * 数值37
	 */
	private BigDecimal value37;

	/**
	 * 数值38
	 */
	private BigDecimal value38;

	/**
	 * 数值39
	 */
	private BigDecimal value39;

	/**
	 * 数值40
	 */
	private BigDecimal value40;

	/**
	 * 数值41
	 */
	private BigDecimal value41;

	/**
	 * 数值42
	 */
	private BigDecimal value42;

	/**
	 * 数值43
	 */
	private BigDecimal value43;

	/**
	 * 数值44
	 */
	private BigDecimal value44;

	/**
	 * 数值45
	 */
	private BigDecimal value45;

	/**
	 * 数值46
	 */
	private BigDecimal value46;

	/**
	 * 数值47
	 */
	private BigDecimal value47;

	/**
	 * 数值48
	 */
	private BigDecimal value48;
	/**
	 * 数值49
	 */
	private BigDecimal value49;

	/**
	 * 数值50
	 */
	private BigDecimal value50;

	/**
	 * 数值51
	 */
	private BigDecimal value51;

	/**
	 * 数值52
	 */
	private BigDecimal value52;

	/**
	 * 数值53
	 */
	private BigDecimal value53;

	/**
	 * 数值54
	 */
	private BigDecimal value54;

	/**
	 * 数值55
	 */
	private BigDecimal value55;

	/**
	 * 数值56
	 */
	private BigDecimal value56;

	/**
	 * 数值57
	 */
	private BigDecimal value57;

	/**
	 * 数值58
	 */
	private BigDecimal value58;

	/**
	 * 数值59
	 */
	private BigDecimal value59;

	/**
	 * 数值60
	 */
	private BigDecimal value60;

	/**
	 * 数值61
	 */
	private BigDecimal value61;

	/**
	 * 数值62
	 */
	private BigDecimal value62;

	/**
	 * 数值63
	 */
	private BigDecimal value63;

	/**
	 * 数值64
	 */
	private BigDecimal value64;

	/**
	 * 数值65
	 */
	private BigDecimal value65;

	/**
	 * 数值66
	 */
	private BigDecimal value66;

	/**
	 * 数值67
	 */
	private BigDecimal value67;

	/**
	 * 数值68
	 */
	private BigDecimal value68;

	/**
	 * 数值69
	 */
	private BigDecimal value69;

	/**
	 * 数值70
	 */
	private BigDecimal value70;

	/**
	 * 数值71
	 */
	private BigDecimal value71;

	/**
	 * 数值72
	 */
	private BigDecimal value72;

	/**
	 * 数值73
	 */
	private BigDecimal value73;

	/**
	 * 数值74
	 */
	private BigDecimal value74;

	/**
	 * 数值75
	 */
	private BigDecimal value75;

	/**
	 * 数值76
	 */
	private BigDecimal value76;

	/**
	 * 数值77
	 */
	private BigDecimal value77;

	/**
	 * 数值78
	 */
	private BigDecimal value78;

	/**
	 * 数值79
	 */
	private BigDecimal value79;

	/**
	 * 数值80
	 */
	private BigDecimal value80;

	/**
	 * 数值81
	 */
	private BigDecimal value81;

	/**
	 * 数值82
	 */
	private BigDecimal value82;

	/**
	 * 数值83
	 */
	private BigDecimal value83;

	/**
	 * 数值84
	 */
	private BigDecimal value84;

	/**
	 * 数值85
	 */
	private BigDecimal value85;

	/**
	 * 数值86
	 */
	private BigDecimal value86;

	/**
	 * 数值87
	 */
	private BigDecimal value87;

	/**
	 * 数值88
	 */
	private BigDecimal value88;

	/**
	 * 数值89
	 */
	private BigDecimal value89;

	/**
	 * 数值90
	 */
	private BigDecimal value90;

	/**
	 * 数值91
	 */
	private BigDecimal value91;

	/**
	 * 数值92
	 */
	private BigDecimal value92;

	/**
	 * 数值93
	 */
	private BigDecimal value93;

	/**
	 * 数值94
	 */
	private BigDecimal value94;

	/**
	 * 数值95
	 */
	private BigDecimal value95;

	/**
	 * 数值96
	 */
	private BigDecimal value96;

	/**
	 * 数值总和
	 */
	private BigDecimal valuesum;

	/**
	 * 更新时间
	 */
	private Timestamp updateTime;

}
