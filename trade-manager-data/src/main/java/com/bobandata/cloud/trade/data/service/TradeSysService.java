package com.bobandata.cloud.trade.data.service;

import com.bobandata.cloud.common.pojo.ServiceResult;

import java.util.Map;

/**
 * @Author: BYC
 * @Date: 2024/1/29 15:39:08
 * @description:
 **/
public interface TradeSysService {

    ServiceResult getAllZCQResult( Map<String, String> headerMap) ;

    ServiceResult getTradeType(String area, String Cookie, String Currentroute);

    ServiceResult getTradeCaptionBytype(String area, String Cookie, String Currentroute, String tradetypeId);

    ServiceResult getResultCalcu(String area, String Cookie, String Currentroute, String tradeseqId, String tradeTypeId, String subTradetypeCode, Integer pageNum, String isSave);


    ServiceResult getTradeNotice(String Area, String Cookie, String Currentroute) ;

}
