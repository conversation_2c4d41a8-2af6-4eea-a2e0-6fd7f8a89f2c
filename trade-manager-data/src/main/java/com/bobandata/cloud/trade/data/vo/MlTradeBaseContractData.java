package com.bobandata.cloud.trade.data.vo;

import cn.hutool.core.util.StrUtil;
import lombok.*;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-03-06日 14:34
 * @description
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MlTradeBaseContractData implements Serializable {

    private String tradeSeqId;
    private String elecContractId;
    private String saleUnitsCode;
    private String saleUnitsName;
    private String vendeeUnitsCode;
    private String vendeeUnitsName;
    private String startTime;
    private String endTime;
    private String timeDivisionCode;
    private BigDecimal vendeeEnergy;
    private BigDecimal vendeePrice;
    private String arrName;

    public String getKey() {
        if (StrUtil.isEmpty(elecContractId)) {
            return String.join("-", this.tradeSeqId, saleUnitsCode, vendeeUnitsCode, startTime, endTime,
                               elecContractId
            );
        }
        return String.join("-", this.tradeSeqId, saleUnitsCode, vendeeUnitsCode, startTime, endTime);
    }

    public static MlTradeBaseContractData fromRow(Row row) {
        return MlTradeBaseContractData.builder()
                                      .tradeSeqId(row.getFieldAs(0))
                                      .elecContractId(row.getFieldAs(1))
                                      .vendeeUnitsCode(row.getFieldAs(2))
                                      .vendeeUnitsName(row.getFieldAs(3))
                                      .saleUnitsName(row.getFieldAs(4))
                                      .saleUnitsCode(row.getFieldAs(5))
                                      .startTime(row.getFieldAs(6))
                                      .endTime(row.getFieldAs(7))
                                      .timeDivisionCode(row.getFieldAs(8))
                                      .vendeeEnergy(row.getFieldAs(9))
                                      .vendeePrice(row.getFieldAs(10))
                                      .arrName(row.getFieldAs(11))
                                      .build();
    }
}
