package com.bobandata.cloud.trade.data.cookieInfo;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.bobandata.cloud.trade.data.dto.CookieInfoMapper;
import com.bobandata.cloud.trade.data.entity.CookieInfoEntity;
import com.bobandata.cloud.trade.data.util.HttpUtil;
import com.bobandata.cloud.trade.data.util.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import net.lightbody.bmp.BrowserMobProxy;
import net.lightbody.bmp.BrowserMobProxyServer;
import net.lightbody.bmp.client.ClientUtil;
import net.lightbody.bmp.core.har.Har;
import net.lightbody.bmp.core.har.HarEntry;
import net.lightbody.bmp.core.har.HarRequest;
import net.lightbody.bmp.core.har.HarResponse;
import net.lightbody.bmp.proxy.CaptureType;
//import org.bytedeco.javacpp.Loader;
//import org.bytedeco.opencv.opencv_java;
import org.opencv.core.Core;
import org.opencv.core.Mat;
import org.opencv.core.Point;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.openqa.selenium.*;
import org.openqa.selenium.Proxy;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.*;
import java.sql.Timestamp;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.opencv.imgcodecs.Imgcodecs.imread;


/**
 * @Author: BYC
 * @Date: 2024/2/1 22:52:45
 * @description:
 **/
@Slf4j
@Component
public class LoginForSelenium {


    @Value("${chromeDriverUrl}")
    private String chromeDriverUrl;

    @Value("${tuPianUrl}")
    private String tuPianUrl;

    @Value("${chromeUrl}")
    private String chromeUrl;


    @Autowired
    private CookieInfoMapper cookieInfoMapper;
    private final static String hunan_url = "https://pmos.hn.sgcc.cn/#/outNet";

    private final static String beijing_url = "https://pmos.sgcc.com.cn/#/outNet";

    private final static String verify_beijing_url = "https://pmos.sgcc.com.cn/px-common-authcenter/auth/v2/verify";

    private final static String verify_hunan_url = "https://pmos.hn.sgcc.cn/px-common-authcenter/auth/v2/verify";


    public boolean login(String userName, String userPwd, String area) {

        QueryWrapper<CookieInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", userName);
        queryWrapper.eq("user_pwd", userPwd);
        queryWrapper.eq("area", area);

        CookieInfoEntity cookieInfoEntity = cookieInfoMapper.selectOne(queryWrapper);
        String secretKey = cookieInfoEntity.getSecretKey();
        System.setProperty("webdriver.chrome.driver", chromeDriverUrl);
//        System.loadLibrary(Core.NATIVE_LIBRARY_NAME);
        System.load("D:\\wxpc\\WeChat Files\\wxid_w0f2g6iaohag31\\FileStorage\\File\\2025-07\\opencv\\build\\java\\x64\\opencv_java3415.dll");
        ChromeOptions options = new ChromeOptions();
        //   options.addArguments("--headless");
        options.addArguments("--start-maximized");
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("ignore-certificate-errors");
//        options.setBinary(chromeUrl);

        WebDriver driver = null;
        BrowserMobProxy browserMobProxy = null;
        try {

            //设置chrome代理
            browserMobProxy = new BrowserMobProxyServer();
            browserMobProxy.setTrustAllServers(true);
            browserMobProxy.setHarCaptureTypes(CaptureType.REQUEST_CONTENT, CaptureType.RESPONSE_CONTENT);
            browserMobProxy.start();
            browserMobProxy.newHar("kk");

//            InetAddress realIP = getRealIP();
//            System.out.println("realIP = " + realIP);

//            String str = "*************";
//            String[] ipStr = str.split("\\.");
//            byte[] ipBuf = new byte[4];
//            for (int i = 0; i < 4; i++) {
//                ipBuf[i] = (byte) (Integer.parseInt(ipStr[i]));
//            }
//            InetAddress localHost = InetAddress.getByAddress(ipBuf);
//            System.out.println("localHost = " + localHost);

//            Proxy seleniumProxy = ClientUtil.createSeleniumProxy(browserMobProxy, realIP);

            Proxy seleniumProxy = ClientUtil.createSeleniumProxy(browserMobProxy);

            // 设置浏览器参数
            options.setProxy(seleniumProxy);
            // 创建驱动对象
            driver = new ChromeDriver(options);
//            driver = new RemoteWebDriver(new URL(chromeUrl), options);


            Thread.sleep(5000);
            // 获取返回的请求内容
            Har har = browserMobProxy.getHar();

            List<HarEntry> entries = har.getLog().getEntries();

            if (area.equals("hunan")) {
                driver.get(hunan_url);
                //loginHK(driver, userName, userPwd);
                //解决机器打开验证不成功【脚本打开和人为打开浏览器会有一个标识，通过这个代码进行修改】
                ((JavascriptExecutor) driver).executeScript("Object.defineProperties(navigator,{ webdriver:{ get: () => false } })");

                Thread.sleep(4000);
                //获取到账号和密码的输入框并填写内容
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[1]/form/div[1]/div/div/input")).sendKeys(userName);
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[1]/form/div[2]/div/div/input")).sendKeys(userPwd);
                //找到登录按钮模拟点击
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/button[1]")).click();

                Thread.sleep(3000);
                //这个时候可能会弹出来滑块
                int i = 1;
                while (true) {
                    log.info("第" + i++ + "次进行验证");
                    //这时候可能有滑块出现yidun_slider
                    WebElement sliderBJ = null;
                    WebElement sliderHK = null;
                    try {
                        //9、如果没拿到，说明直接登录了，没有触发出来滑块
                        sliderBJ = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[2]/div/div[2]/div/div[1]/div[1]/img"));
                        sliderHK = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[2]/div/div[2]/div/div[2]/div/div/div/img"));
                    } catch (Exception e) {
                        log.error(userName + "未获取图片，登录失败！");
                    }
                    //解决图片下载不了的问题
                    TimeUnit.SECONDS.sleep(1);

                    //如果不为空需要处理滑块
                    if (sliderBJ != null && sliderHK != null) {
                        //先得到距离，这里需要opencv  先搭建一下
                        //获取到背景图的地址和滑块的地址
                        String srcBJ = sliderBJ.getAttribute("src");
                        String srcHK = sliderHK.getAttribute("src");
                        //拿到图片
                        String houZhui = srcBJ.substring(srcBJ.lastIndexOf(",") + 1);
                        String houZhuiHK = srcHK.substring(srcBJ.lastIndexOf(",") + 1);
                        //将图片下载到本地
                        File tuPianDir = new File(tuPianUrl);
                        if (!tuPianDir.exists()) {
                            tuPianDir.mkdir();
                        }
                        ImageUtils.GenerateImage(houZhui, tuPianUrl + "beijing.png");
                        ImageUtils.GenerateImage(houZhuiHK, tuPianUrl + "huakuai.png");
                        //从本地读取背景原图
                        Mat src = imread(tuPianUrl + "beijing.png", Imgcodecs.IMREAD_GRAYSCALE);

                        Mat srcBenDiHK = imread(tuPianUrl + "huakuai.png", Imgcodecs.IMREAD_GRAYSCALE);
                        //创建一个新的背景图，方便做标记
//                Mat clone = src.clone();
                        Mat result = new Mat();


                        //匹配小图在大图中的位置  用标准模式去比较 然后把返回结果给result
                        Imgproc.matchTemplate(src, srcBenDiHK, result, Imgproc.TM_CCORR_NORMED);
                        Core.normalize(result, result, 1, 0, Core.NORM_MINMAX, -1, new Mat());

                        //获取匹配结果坐标
                        Core.MinMaxLocResult minMaxLocResult = Core.minMaxLoc(result);
                        Point maxLoc = minMaxLocResult.maxLoc;

                        double distance = maxLoc.x;
                        log.info("distance = " + distance);
                        //模拟移动
                        move(driver, sliderHK, (int) distance);

                        //移动后看看是否还存在不
                        try {
                            Thread.sleep(2000);
                            String text = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[3]/div[1]/div/div[2]/div/div[1]/div[1]/div/div/div/div[3]")).getText();
                            log.info("text = " + text);
                            if (text.equals("短信验证")) {
                                log.info("验证成功");
                                break;
                            }
                        } catch (Exception e) {
                            log.error("第" + i + "验证失败");
                        }
                    }
                }
                log.info("循环结束，验证成功");
                //选择短信登录方式
//            driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[3]/div[1]/div/div[2]/div/div[1]/div[1]/div/div/div/div[3]")).click();
//            //点击短信发送按钮
//            //driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[3]/div[1]/div/div[2]/div/div[2]/div/form/div[2]/div/div[1]/div/button")).click();
//            //等待收取短信验证码
//            String phoneCode = getPhoneCode();
//            //输入短信验证码
//            driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[3]/div[1]/div/div[2]/div/div[2]/div/form/div[2]/div/div[1]/input")).sendKeys(phoneCode);
//            //验证登录
//            driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[3]/div[3]/div[1]/div/div[2]/div/div[2]/div/form/div[2]/div/div[2]/button")).click();
                Thread.sleep(4000);
                for (HarEntry harEntry : entries) {
                    HarResponse response = harEntry.getResponse();
                    HarRequest request = harEntry.getRequest();
                    if (request.getUrl().equals("https://pmos.hn.sgcc.cn/px-common-authcenter/auth/v2/login")) {
                        String jwtResponse = response.getContent().getText();
                        JSONObject jsonObject = JSONObject.parseObject(jwtResponse);
                        String jwt = jsonObject.getJSONObject("data").getString("jwt");
                        Map<String, Object> verifyPamMap = new HashMap<>();
                        verifyPamMap.put("secretKey", secretKey);
                        verifyPamMap.put("jwt", jwt);
                        verifyPamMap.put("loginType", "CF_CA");
                        verifyPamMap.put("cookieTicketKey", "Admin-Token");
                        verifyPamMap.put("skipRealNameAuth", false);
                        String jsonString = JSONObject.toJSONString(verifyPamMap);
                        String verifyRes = HttpUtil.httpPostUtil(verify_hunan_url, jsonString, null, 30, 30, true);
                        JSONObject verifyJsonObject = JSONObject.parseObject(verifyRes);
                        String tokenByVerify = verifyJsonObject.getJSONObject("data").getString("token");
                        cookieInfoEntity.setXTicket(tokenByVerify);
                        cookieInfoEntity.setAdminToken(tokenByVerify);
                        cookieInfoEntity.setLoginTime(new Timestamp(System.currentTimeMillis()));
                        saveCookieInfo(cookieInfoEntity, userName, area);
                    }
                }
                log.info(userName + "登录" + area + "交易平台成功！");
                return true;
            } else if (area.equals("beijing")) {
                //解决机器打开验证不成功【脚本打开和人为打开浏览器会有一个标识，通过这个代码进行修改】
                ((JavascriptExecutor) driver).executeScript("Object.defineProperties(navigator,{ webdriver:{ get: () => false } })");

                driver.get(beijing_url);

                Thread.sleep(4000);
                //获取到账号和密码的输入框并填写内容
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/div[1]/form/div[1]/div/div/input")).sendKeys(userName);
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/div[1]/form/div[2]/div/div[1]/input")).sendKeys(userPwd);
                //找到登录按钮模拟点击
                driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/button[1]")).click();

                Thread.sleep(3000);
                //这个时候可能会弹出来滑块
                int i = 1;
                while (true) {
                    log.info("第" + i++ + "次进行验证");
                    //这时候可能有滑块出现yidun_slider
                    WebElement sliderBJ = null;
                    WebElement sliderHK = null;
                    try {
                        //9、如果没拿到，说明直接登录了，没有触发出来滑块
                        sliderBJ = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/div[2]/div/div[2]/div/div[1]/div/img"));
                        sliderHK = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/div[2]/div/div[2]/div/div[2]/div/div/div/img"));
                    } catch (Exception e) {
                        log.error(userName + "未获取图片，登录失败！");
                    }
                    //解决图片下载不了的问题
                    TimeUnit.SECONDS.sleep(1);

                    //如果不为空需要处理滑块
                    if (sliderBJ != null && sliderHK != null) {
                        //先得到距离，这里需要opencv  先搭建一下
                        //获取到背景图的地址和滑块的地址
                        String srcBJ = sliderBJ.getAttribute("src");
                        String srcHK = sliderHK.getAttribute("src");
                        //拿到图片
                        String houZhui = srcBJ.substring(srcBJ.lastIndexOf(",") + 1);
                        String houZhuiHK = srcHK.substring(srcBJ.lastIndexOf(",") + 1);
                        //将图片下载到本地

                        File tuPianDir = new File(tuPianUrl);
                        if (!tuPianDir.exists()) {
                            tuPianDir.mkdir();
                        }
                        ImageUtils.GenerateImage(houZhui, tuPianUrl + "beijing.png");
                        ImageUtils.GenerateImage(houZhuiHK, tuPianUrl + "huakuai.png");
                        //从本地读取背景原图
                        Mat src = imread(tuPianUrl + "beijing.png", Imgcodecs.IMREAD_GRAYSCALE);

                        Mat srcBenDiHK = imread(tuPianUrl + "huakuai.png", Imgcodecs.IMREAD_GRAYSCALE);
                        //创建一个新的背景图，方便做标记
//                Mat clone = src.clone();
                        Mat result = new Mat();


                        //匹配小图在大图中的位置  用标准模式去比较 然后把返回结果给result
                        Imgproc.matchTemplate(src, srcBenDiHK, result, Imgproc.TM_CCORR_NORMED);
                        Core.normalize(result, result, 1, 0, Core.NORM_MINMAX, -1, new Mat());

                        //获取匹配结果坐标
                        Core.MinMaxLocResult minMaxLocResult = Core.minMaxLoc(result);
                        Point maxLoc = minMaxLocResult.maxLoc;

                        double distance = maxLoc.x;
                        log.info("distance = " + distance);
                        //模拟移动
                        move(driver, sliderHK, (int) distance);
                        Thread.sleep(4000);
                        //移动后看看是否还存在不
                        try {
                            String text = driver.findElement(By.xpath("/html/body/div/div/div/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/div[4]/div[3]/div[1]/div/div[2]/div/div[1]/div[1]/div/div/div/div[2]")).getText();
                            log.info("text = " + text);
                            if (text.trim().equals("证书验证")) {
                                log.info("验证成功！");
                                break;
                            }
                        } catch (Exception e) {
                            try {
                                String oder = driver.findElement(By.xpath("/html/body/div[3]/div/div[3]/div[1]/div/div/div[1]/div[1]/div/div[1]/div[1]/span")).getText();
                                log.info("oder = " + oder);
                                if (oder.trim().equals("常规菜单")) {
                                    log.info("验证成功！");
                                    break;
                                }
                            } catch (Exception exception) {
                                log.error("第" + i + "验证失败");
                            }
                        }
                    }
                }
                log.info("循环结束，验证成功");

                Thread.sleep(4000);
                for (HarEntry harEntry : entries) {
                    HarResponse response = harEntry.getResponse();
                    HarRequest request = harEntry.getRequest();
                    if (request.getUrl().equals("https://pmos.sgcc.com.cn/px-common-authcenter/auth/v2/login")) {
                        String jwtResponse = response.getContent().getText();
                        JSONObject jsonObject = JSONObject.parseObject(jwtResponse);
                        String tokenByLogin = jsonObject.getJSONObject("data").getString("token");
                        if (tokenByLogin != null) {
                            cookieInfoEntity.setXTicket(tokenByLogin);
                            cookieInfoEntity.setAdminToken(tokenByLogin);
                            cookieInfoEntity.setLoginTime(new Timestamp(System.currentTimeMillis()));
                            saveCookieInfo(cookieInfoEntity, userName, area);
                        } else {
                            String jwt = jsonObject.getJSONObject("data").getString("jwt");
                            Map<String, Object> verifyPamMap = new HashMap<>();
                            verifyPamMap.put("secretKey", secretKey);
                            verifyPamMap.put("jwt", jwt);
                            verifyPamMap.put("loginType", "CF_CA");
                            verifyPamMap.put("cookieTicketKey", "Admin-Token");
                            verifyPamMap.put("skipRealNameAuth", false);
                            String jsonString = JSONObject.toJSONString(verifyPamMap);
                            String verifyRes = HttpUtil.httpPostUtil(verify_beijing_url, jsonString, null, 30, 30, true);
                            JSONObject verifyJsonObject = JSONObject.parseObject(verifyRes);
                            String tokenByVerify = verifyJsonObject.getJSONObject("data").getString("token");
                            cookieInfoEntity.setXTicket(tokenByVerify);
                            cookieInfoEntity.setAdminToken(tokenByVerify);
                            cookieInfoEntity.setLoginTime(new Timestamp(System.currentTimeMillis()));
                            saveCookieInfo(cookieInfoEntity, userName, area);
                        }
                    }
                }

                log.info(userName + "登录" + area + "交易平台成功！");
                return true;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            if (driver != null) {
                driver.quit();
                log.info("driver is close!");
            }
            if (browserMobProxy != null) {
                browserMobProxy.stop();
                log.info("browserMobProxy is close!");
            }
        }
        return false;

    }

    /**
     * 模拟人工移动
     *
     * @param driver
     */
    public static void move(WebDriver driver, WebElement element, int distance) throws InterruptedException {
        int moveY = 0;
        try {
            //初始化鼠标对象
            Actions actions = new Actions(driver);
            //鼠标按住左键不动
            actions.clickAndHold(element).perform();
            Thread.sleep(200);
            actions.moveByOffset(distance + 10, moveY).perform();
            Thread.sleep(200);
            actions.release(element).perform();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static String getPhoneCode() throws IOException {
        // 创建服务器Socket
        ServerSocket serverSocket = new ServerSocket(8234);
        log.info("服务器已启动，等待客户端连接...");
        // 接受客户端连接
        Socket clientSocket = serverSocket.accept();
        log.info("客户端已连接：" + clientSocket.getInetAddress());

        InputStream in = clientSocket.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(in, "UTF-8"));
        String message = reader.readLine();
        log.info("收到客户端消息：" + message);

        clientSocket.close();
        serverSocket.close();
        return message.substring(31);
    }

    public int saveCookieInfo(CookieInfoEntity cookieInfoEntity, String userName, String area) {

        QueryWrapper<CookieInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", userName);
        queryWrapper.eq("area", area);
        Long count = cookieInfoMapper.selectCount(queryWrapper);
        if (count <= 0) {
            int insert = cookieInfoMapper.insert(cookieInfoEntity);
            return insert;
        } else {
            int update = cookieInfoMapper.update(cookieInfoEntity, queryWrapper);
            return update;
        }
    }


    public static InetAddress getRealIP() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                if (networkInterface.isLoopback() || networkInterface.isVirtual() || networkInterface.isPointToPoint() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    if (!inetAddress.isLoopbackAddress() && inetAddress instanceof java.net.Inet4Address) {
                        return inetAddress;
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Could not resolve real ip", e);
        }
        return ClientUtil.getConnectableAddress();
    }
}
