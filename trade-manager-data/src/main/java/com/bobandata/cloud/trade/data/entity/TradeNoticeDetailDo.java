package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-05-29 
 */
@Data
@TableName(value = "trade_notice_detail",autoResultMap = true)
public class TradeNoticeDetailDo implements Serializable {

	private static final long serialVersionUID =  1378711091760591657L;

	private String tradeseqId;

	private String tradeseqCaption;

	private String tradeMarketType;

	private String transactionOrganizer;

	private LocalDateTime startTime;

	private LocalDateTime endTime;

	private String outPoint;

	private String totalPower;

	private String maxPower;

	private String sell;

	private String transmission;

	private String purchase;

	private String lineName;

	private String power;

	private String tradeWay;

	private String periodTime;

	private String clearingWay;

	private String uncoupledClearMiddle;

	private String sellOrder;

	private String saleOrder;

	private String pricePrecision;

	private String powerPrecision;

	private String map;

	private Integer num;

	private String isFlag;

	private String timelines;

	private String timeline;

	private String headList;

	private String list;

	private String tradeDeclaration;

	private String mymarkets;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<TradeConfigDo> trJyConfigList;

}
