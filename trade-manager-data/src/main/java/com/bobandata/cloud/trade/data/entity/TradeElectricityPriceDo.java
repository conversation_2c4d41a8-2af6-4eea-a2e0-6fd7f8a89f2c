package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.math.BigDecimal;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024-05-30
 */
@Data
@TableName("trade_electricity_price")
public class TradeElectricityPriceDo implements Serializable {

    private static final long serialVersionUID = 1256283860496666673L;

    private String powerType;

    private String voltageGrade;

    private Timestamp executeStartDate;

    private Timestamp executeEndDate;

    private String dianduPrice;

    private String onlinePrice;

    private String lineLossPrice;

    private String transportMatchPrice;

    private String systemWorkPrice;

    private String governmentFund;

    private String peakPrice;

    private String highestPrice;

    private String flatPrice;

    private String valleyPrice;

    private String maxNeedCapacity;

    private String transformerCapacity;

    private Integer number;

}
