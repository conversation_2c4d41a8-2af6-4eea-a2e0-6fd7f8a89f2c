package com.bobandata.cloud.trade.data.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: BYC
 * @Date: 2024/2/21 10:05:02
 * @description:
 **/
@Component
@ConfigurationProperties(prefix = "interfaceurl")
@Getter
@Setter
@ToString
public class TradeInterfaceUrlConfig {

    private Map<String, String> gsPathMaps;
    private Map<String, String> hnPathMaps;
    private String isSSL;
}
