package com.bobandata.cloud.trade.data.vo;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024-03-07日 15:45
 * @description
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MlTermContractInfo implements Serializable {

    private String tradeId;
    private Integer dateYear;
    private Integer dateMonth;
    private String tradeName;
    private String arrId;
    private String arrName;
    private String sellerUnitId;
    private String sellerUnitName;
    private String purchaseUnitId;
    private String purchaseUnitName;
    private Timestamp startTime;
    private Timestamp endTime;
    private BigDecimal contractEnergy;
    private BigDecimal contractPrice;

    private MlTermContractEnergy[] contractEnergies;
}
