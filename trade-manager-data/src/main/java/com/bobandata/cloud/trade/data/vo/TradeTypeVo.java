package com.bobandata.cloud.trade.data.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/1/31
 * @description
 **/
@Data
public class TradeTypeVo {
    private String preTradeseqId;
    private String tradeseqId;
    private String tradeseqName;
    private String tradeseqCaption;
    private String parentTradetypeCode;
    private String subTradetypeName;
    private String subTradetypeCode;
    private String parentTradetypeName;
    private String tradetypeId;
    private String tradeseqCycle;
    private String beginDate;
    private String endDate;
    private String bandNum;
    private String createTime;
    private String createUser;
    private String marketId;
    private String openCode;
    private String snDate;
    private String snOutParty;
    private String snOutPartyName;
    private String snInParty;
    private String snInPartyName;
    private String snJyType;
    private String snCycleType;
    private String snOtherInfo;
    private String tradeGgId;
    private String tradeVariety;
    private String subTradeVariety;
    private String tradeMarketType;
    private String snOutBranch;
    private String snInBranch;
    private String tradeChannel;
    private String tradeAttr1;
    private String tradeAttr2;
    private String tradeAttr3;
    private String isEmergencySupport;
    private String transactionOrganizer;
    private String isExistAgreement;
    private String tradeComments;
    private String priority;
    private String tradeRegion;
    private String timerpartNum;
    private String checkState;
    private String isExistContract;
    private String intentionId;
    private String protocolId;
    private String targetEnergy;
    private String encryptInfo;
    private String tradetypeMode;
    private String pledgetemplateId;
    private String isDele;
    private String specialTrade;
    private String transactionName;
    private String isDelete;
    private String isWitnessContract;
    private String ddCode;
    private String tradeVarietyName;
    private String sunEnergy;
    private String avgPrice;
    private String isFixedPath;
    private String ddRouteCode;
    private String startTime;
    private String endTime;
    private String timeStr;
    private String powerDirection;
    private String vbandNum;
    private String sbandNum;
    private String tradeseqIdList;
    private String generateVersion;
    private String powerNum;
    private String tradeRole;
    private String participantId;
    private String year;
    private String tradeTimepart;
    private String isConstraint;

}
