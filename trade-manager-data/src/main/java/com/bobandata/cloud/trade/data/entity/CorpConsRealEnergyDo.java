package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-04-23 
 */
@Data
@TableName("corp_cons_real_energy_beifen")
public class CorpConsRealEnergyDo implements Serializable {

	private static final long serialVersionUID =  6313153243736139354L;

	private Integer id;

	/**
	 * 用户id
	 */
	private Integer consId;

	/**
	 * 用户号
	 */
	private String consNo;

	/**
	 * 日期
	 */
	private LocalDate dataDate;

	/**
	 * 尖峰
	 */
	private BigDecimal energy1;

	/**
	 * 高峰
	 */
	private BigDecimal energy2;

	/**
	 * 平段
	 */
	private BigDecimal energy3;

	/**
	 * 谷段
	 */
	private BigDecimal energy4;

	/**
	 * 总计
	 */
	private BigDecimal totalBattery;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 创建者ID
	 */
	private Integer creatorId;

	/**
	 * 刷新时间
	 */
	private LocalDateTime lastRefreshTime;

	/**
	 * 最后修改人
	 */
	private Integer lastModifierId;

}
