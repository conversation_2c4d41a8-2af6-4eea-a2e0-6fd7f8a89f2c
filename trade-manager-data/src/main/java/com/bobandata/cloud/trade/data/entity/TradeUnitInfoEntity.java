package com.bobandata.cloud.trade.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024-07-16
 */
@Data
@TableName("trade_unit_info")
public class TradeUnitInfoEntity implements Serializable {

    private static final long serialVersionUID = 6217204735951528662L;

    /**
     * 用户账号名
     */
    private String userName;

    /**
     * 用户密码
     */
    private String userPwd;


    /**
     * 用户地区
     */
    private String area;
    /**
     * 场站名称
     */
    private String unitName;

    /**
     * 场站id
     */
    private String unitId;

    /**
     * 交易单元id
     */
    private String tradeUnitId;

    /**
     * 调度单元id
     */
    private String dispatchUnitId;

}
