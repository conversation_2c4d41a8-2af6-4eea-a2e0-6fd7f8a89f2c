package com.bobandata.cloud.trade.data.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-09-20 
 */


@TableName("trade_green_power_hn")
@Data
public class TradeGreenPowerHnDo implements Serializable {

	private static final long serialVersionUID =  6760752487945548538L;

	private String guid;

	/**
	 * 绿电分解查询月份
	 */
	private String mktMonth;

	/**
	 * 时段：1、2、3、4代表：尖峰平谷
	 */
	private String period;

	/**
	 * 用电单元id
	 */
	private String busiunitId;

	/**
	 * 用电单元名称
	 */
	private String busiunitName;

	/**
	 * 分解电量
	 */
	private Double energy;

	/**
	 * 交易系统数据更新时间
	 */
	private String updateTime;

	/**
	 * 是否锁定：0代表未锁定；1代表锁定
	 */
	private String lockValue;

	/**
	 * 营销用户编号
	 */
	private String consNo;

	/**
	 * 营销用户名称
	 */
	private String consName;

	/**
	 * 绿电合同电量
	 */
	private Double energyT;

	/**
	 * 合同id
	 */
	private String contractId;

	/**
	 * 合同名称
	 */
	private String contractName;

	/**
	 * 分解状态：1代表已提交
	 */
	private String declarationStatus;

	/**
	 * 确认状态：1代表确认
	 */
	private String confirmStatus;

	/**
	 * 售方名称
	 */
	private String sellParticipantName;

	/**
	 * 买方名称
	 */
	private String userParticipantName;

	private String declGuid;

	/**
	 * 合同电价
	 */
	private String contractPriceT;

	/**
	 * 绿色电量环境价
	 */
	private String greenRightsPrice;

	/**
	 * 电能量差价
	 */
	private String priceM;

}
