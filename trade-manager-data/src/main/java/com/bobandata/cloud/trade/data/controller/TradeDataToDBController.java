package com.bobandata.cloud.trade.data.controller;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.cookieInfo.LoginForCookieImp;
import com.bobandata.cloud.trade.data.service.impl.EnergyFileServiceImp;
import com.bobandata.cloud.trade.data.service.impl.TradeSysServiceImp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;


/**
 * @Author: BYC
 * @Date: 2024/1/29 15:03:11
 * @description: 长沙和甘肃华能交易合同数据
 **/

@Slf4j
@Tag(name = "交易合同数据")
@RequestMapping("/tradeData")
@RestController
public class TradeDataToDBController {

    @Autowired
    private TradeSysServiceImp tradeSysServiceImp;
    @Autowired
    private EnergyFileServiceImp energyFileServiceImp;
    @Autowired
    private LoginForCookieImp loginForCookieImp;

    @GetMapping("/login")
    @Operation(summary = "登录")
    ServiceResult login(String userName, String userPwd, String area) {
        return loginForCookieImp.loginForCookie(userName, userPwd, area);
    }

    @GetMapping("/saveTradeAllResult")
    @Operation(summary = "获取交易系统中长期合同数据入库-全量")
    ServiceResult getAllZCQResult(Map<String, String> headerMap) {
        return tradeSysServiceImp.getAllZCQResult(headerMap);
    }

    @GetMapping("/getTradeType")
    @Operation(summary = "获取交易类型")
    ServiceResult getTradeType(String area, String Cookie, String Currentroute) {
        return tradeSysServiceImp.getTradeType(area, Cookie, Currentroute);
    }

    @GetMapping("/getTradeCaptionBytype")
    @Operation(summary = "获取交易序列")
    ServiceResult getTradeCaptionBytype(String area, String Cookie, String Currentroute, String tradetypeId) {
        return tradeSysServiceImp.getTradeCaptionBytype(area, Cookie, Currentroute, tradetypeId);
    }


    @GetMapping("/getResultCalcu")
    @Operation(summary = "根据交易序列号获取中长期合同数据-可选择入库")
    ServiceResult getResultCalcu(String area, String Cookie, String Currentroute, String tradeseqId, String tradeTypeId, String subTradetypeCode, Integer pageNum, String isSave) {
        return tradeSysServiceImp.getResultCalcu(area, Cookie, Currentroute, tradeseqId, tradeTypeId, subTradetypeCode, pageNum, isSave);
    }

    @RequestMapping(value = "/sqlFile", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "sql文件-energy数据导入")
    public ServiceResult uploadSqlFile(@RequestPart("file") MultipartFile file) {
        return energyFileServiceImp.analysisSqlFile(file);
    }

    @GetMapping("/getTradeNotice")
    @Operation(summary = "导入交易公告数据入库")
    public ServiceResult getTradeNotice(String Area, String Cookie, String Currentroute) {
        return tradeSysServiceImp.getTradeNotice(Area, Cookie, Currentroute);
    }


    @RequestMapping(value = "/ProxyPriceFile", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "工商业代理差价文件数据导入")
    public ServiceResult uploadProxyPriceFile(@RequestPart("file") MultipartFile file) {
        return tradeSysServiceImp.uploadProxyPriceFile(file);
    }

    @GetMapping("/getGreenPower")
    @Operation(summary = "导入绿电分解交易数据入库")
    public ServiceResult getGreenPower(String month, String Cookie, String Currentroute) {
        return tradeSysServiceImp.greenPower(month, Cookie, Currentroute);
    }

    /**
     * 获取现货交易
     */
    @GetMapping("/getSpotTrade")
    @Operation(summary = "导入现货交易数据入库")
    public ServiceResult getSpotTrade(String startDate, String endDate, String Cookie, String Currentroute, String membersType, String unitId) {
        return tradeSysServiceImp.getSpotTradeByDateRange(startDate, endDate, Cookie, Currentroute, membersType, unitId);
    }

    /**
     * 自动化下载excel表格，并入库
     */
    @GetMapping("/autoDownload")
    @Operation(summary = "自动化下载excel表格，并入库")
    public ServiceResult autoDownload(String userName, String userPwd, String area,String date,String type) {
        ServiceResult serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
        if(serviceResult.getData() instanceof String){
            serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
        }
        WebDriver driver = (WebDriver) serviceResult.getData();
        //设置屏幕最大化
        driver.manage().window().maximize();
        return tradeSysServiceImp.autoDownload(driver,date,type);
    }

}
