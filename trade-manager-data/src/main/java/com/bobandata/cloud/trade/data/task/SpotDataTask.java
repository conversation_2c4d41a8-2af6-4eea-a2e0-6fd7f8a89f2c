package com.bobandata.cloud.trade.data.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.cookieInfo.LoginForCookieImp;
import com.bobandata.cloud.trade.data.cookieInfo.LoginForSelenium;
import com.bobandata.cloud.trade.data.dto.CookieInfoMapper;
import com.bobandata.cloud.trade.data.dto.TradeUnitInfoMapper;
import com.bobandata.cloud.trade.data.entity.CookieInfoEntity;
import com.bobandata.cloud.trade.data.entity.TradeUnitInfoEntity;
import com.bobandata.cloud.trade.data.service.impl.TradeSysServiceImp;
import com.bobandata.cloud.trade.data.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@EnableScheduling
public class SpotDataTask {


    @Autowired
    private CookieInfoMapper cookieInfoMapper;


    @Autowired
    private LoginForCookieImp loginForCookieImp;

    @Autowired
    private TradeSysServiceImp tradeSysServiceImp;

    @Scheduled(cron = "${rqspotcron}")
    public void task() {
        List<CookieInfoEntity> cookieInfoEntities = cookieInfoMapper.selectList();
        for (CookieInfoEntity cookieInfoEntity : cookieInfoEntities) {
            if (cookieInfoEntity.getArea().equals("hunan")) {
                String userName = cookieInfoEntity.getUserName();
                String userPwd = cookieInfoEntity.getUserPwd();
                String area = cookieInfoEntity.getArea();
                ServiceResult serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
                if (serviceResult.getData() instanceof String) {
                    serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
                }
                WebDriver driver = (WebDriver) serviceResult.getData();
                //设置屏幕最大化
                driver.manage().window().maximize();
                tradeSysServiceImp.autoDownload(driver, "2025-06-14", "日前");
            }
        }
    }

//    @Scheduled(cron = "${ssspotcron}")
//    public void task2() {
//        List<CookieInfoEntity> cookieInfoEntities = cookieInfoMapper.selectList();
//        for (CookieInfoEntity cookieInfoEntity : cookieInfoEntities) {
//            if (cookieInfoEntity.getArea().equals("hunan")) {
//                String userName = cookieInfoEntity.getUserName();
//                String userPwd = cookieInfoEntity.getUserPwd();
//                String area = cookieInfoEntity.getArea();
//                ServiceResult serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
//                if (serviceResult.getData() instanceof String) {
//                    serviceResult = loginForCookieImp.loginForCookie(userName, userPwd, area);
//                }
//                WebDriver driver = (WebDriver) serviceResult.getData();
//                //设置屏幕最大化
//                driver.manage().window().maximize();
//                tradeSysServiceImp.autoDownload(driver, "2025-06-14", "实时");
//            }
//        }
//    }
}
