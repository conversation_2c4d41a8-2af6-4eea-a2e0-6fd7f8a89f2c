package com.bobandata.cloud.trade.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-11-05 
 */
@Data
@TableName("cookie_info")
public class CookieInfoEntity implements Serializable {

	private static final long serialVersionUID =  9034312114797142883L;

	/**
	 * 交易单元账号
	 */
	private String userName;

	/**
	 * 交易单元密码
	 */
	private String userPwd;

	/**
	 * 地区
	 */
	private String area;

	/**
	 * 场站名称
	 */
	private String unitName;

	/**
	 * 私钥
	 */
	private String secretKey;

	private String xTicket;

	private String adminToken;

	private String currentRoute;

	private String grayTag;

	private String clientTag;

	private String sidebarStatus;

	private String xToken;

	private Timestamp loginTime;

}
