package com.bobandata.cloud.trade.data.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.trade.data.cookieInfo.LoginForSelenium;
import com.bobandata.cloud.trade.data.dto.CookieInfoMapper;
import com.bobandata.cloud.trade.data.dto.TradeUnitInfoMapper;
import com.bobandata.cloud.trade.data.entity.CookieInfoEntity;
import com.bobandata.cloud.trade.data.entity.TradeUnitInfoEntity;
import com.bobandata.cloud.trade.data.service.impl.TradeSysServiceImp;
import com.bobandata.cloud.trade.data.util.DateUtil;
import com.bobandata.cloud.trade.data.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author： BYC
 * @create： 2024/7/19 16:10
 * @description：TODO
 */

@Slf4j
@Component
@EnableScheduling
public class TradeDataTask {

    private final static String hn_getMenu_url = "https://pmos.hn.sgcc.cn/px-trade-extranet/trTradeType/getMenu";

    private final static String CurrentRoute = "/pxf-trade-extranet/myTransaction/TradeQuery?date=";

    @Autowired
    private CookieInfoMapper cookieInfoMapper;

    @Autowired
    private TradeUnitInfoMapper tradeUnitInfoMapper;

    @Autowired
    private LoginForSelenium loginForSelenium;

    @Autowired
    private TradeSysServiceImp tradeSysServiceImp;

    @Scheduled(cron = "${cron}")
    public void task() {
        List<TradeUnitInfoEntity> tradeUnitInfoEntities = tradeUnitInfoMapper.selectList();
        spiderData(tradeUnitInfoEntities);

    }


    public void spiderData(List<TradeUnitInfoEntity> tradeUnitInfoEntities) {

        for (TradeUnitInfoEntity tradeUnitInfoEntity : tradeUnitInfoEntities) {
            String userName = tradeUnitInfoEntity.getUserName();
            String userPwd = tradeUnitInfoEntity.getUserPwd();
            String area = tradeUnitInfoEntity.getArea();
            Map<String, String> paramMap = new HashMap<>();

            Map<String, String> headMap = getHeadMap(userName, userPwd, area);


            if (area.equals("hunan")) {
                String menuRes = HttpUtil.httpPostUtil(hn_getMenu_url, paramMap.toString(), headMap, 60, 60, true);
                String message = JSONObject.parseObject(menuRes).getString("message");
                if (message.equals("Success")) {
                    tradeSysServiceImp.getAllZCQResult(headMap);
                    log.info(userName + area + ":中长期交易结果数据已导入！");
                } else {
                    loginForSelenium.login(userName, userPwd, area);
                    Map<String, String> head = getHeadMap(userName, userPwd, area);
                    tradeSysServiceImp.getAllZCQResult(head);
                    log.info(userName + area + ":中长期交易结果数据已导入！");
                }
            }
        }
    }

    private Map<String, String> getHeadMap(String userName, String userPwd, String area) {
        QueryWrapper<CookieInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_name", userName);
        queryWrapper.eq("user_pwd", userPwd);
        queryWrapper.eq("area", area);

        CookieInfoEntity cookieInfoEntity = cookieInfoMapper.selectOne(queryWrapper);
        String xTicket = cookieInfoEntity.getXTicket();
        String adminToken = cookieInfoEntity.getAdminToken();
        String currentRoute = cookieInfoEntity.getCurrentRoute();
        String grayTag = cookieInfoEntity.getGrayTag();
        String clientTag = cookieInfoEntity.getClientTag();
        String sidebarStatus = cookieInfoEntity.getSidebarStatus();
        String xToken = cookieInfoEntity.getXToken();


        HashMap<String, String> haedMap = new HashMap<>();
        haedMap.put("X-Ticket", xTicket);
        haedMap.put("Admin-Token", adminToken);
        haedMap.put("CurrentRoute", currentRoute);
        haedMap.put("Gray-Tag", grayTag);
        haedMap.put("ClientTag", clientTag);
        haedMap.put("sidebarStatus", sidebarStatus);
        haedMap.put("X-Token", xToken);
        haedMap.put("CurrentRoute", CurrentRoute + System.currentTimeMillis());
        return haedMap;
    }

    private void saveGSData(String userName, Map<String, String> headMap, String area) {
        tradeSysServiceImp.getAllZCQResult(headMap);
        log.info(userName + area + ":中长期交易结果数据已导入！");
    }
}
