package com.bobandata.cloud.trade.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@TableName("cqdl")
public class MlTradeCqdlEntity implements Serializable {

    private static final long serialVersionUID = 9034312114797142886L;

    /**
     * 单元ID
     */
    private String id;


    /**
     * 数据时间
     */
    private Date dataTime;

    /**
     * 数据类型（
     */
    private String dataType;

    /**
     * 数值1
     */
    private BigDecimal value1;

    /**
     * 数值2
     */
    private BigDecimal value2;

    /**
     * 数值3
     */
    private BigDecimal value3;

    /**
     * 数值4
     */
    private BigDecimal value4;

    /**
     * 数值5
     */
    private BigDecimal value5;

    /**
     * 数值6
     */
    private BigDecimal value6;

    /**
     * 数值7
     */
    private BigDecimal value7;

    /**
     * 数值8
     */
    private BigDecimal value8;

    /**
     * 数值9
     */
    private BigDecimal value9;

    /**
     * 数值10
     */
    private BigDecimal value10;

    /**
     * 数值11
     */
    private BigDecimal value11;

    /**
     * 数值12
     */
    private BigDecimal value12;

    /**
     * 数值13
     */
    private BigDecimal value13;

    /**
     * 数值14
     */
    private BigDecimal value14;

    /**
     * 数值15
     */
    private BigDecimal value15;

    /**
     * 数值16
     */
    private BigDecimal value16;

    /**
     * 数值17
     */
    private BigDecimal value17;

    /**
     * 数值18
     */
    private BigDecimal value18;

    /**
     * 数值19
     */
    private BigDecimal value19;

    /**
     * 数值20
     */
    private BigDecimal value20;

    /**
     * 数值21
     */
    private BigDecimal value21;

    /**
     * 数值22
     */
    private BigDecimal value22;

    /**
     * 数值23
     */
    private BigDecimal value23;

    /**
     * 数值24
     */
    private BigDecimal value24;


}
