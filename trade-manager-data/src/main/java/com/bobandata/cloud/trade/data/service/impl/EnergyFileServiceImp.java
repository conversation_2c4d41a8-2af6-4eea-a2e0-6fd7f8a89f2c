package com.bobandata.cloud.trade.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.data.dto.CorpConsMapper;
import com.bobandata.cloud.trade.data.dto.CorpConsRealEnergyMapper;
import com.bobandata.cloud.trade.data.entity.CorpConsDo;
import com.bobandata.cloud.trade.data.entity.CorpConsRealEnergyDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * @author： BYC
 * @create： 2024/4/23 15:03
 * @description：TODO
 */

@Slf4j
@Service
public class EnergyFileServiceImp {


    @Autowired
    private CorpConsRealEnergyMapper corpConsRealEnergyMapper;

    @Autowired
    private CorpConsMapper corpConsMapper;

    @Autowired
    private DataSource dataSource;

    private final static String sql_prefix = "INSERT INTO corp_cons_real_energy_beifen ( cons_id, cons_no, data_date, energy1, energy2, energy3, energy4, total_battery) VALUES (";

    public ServiceResult analysisSqlFile(@RequestParam("file") MultipartFile file) {

        Connection connection = null;
        PreparedStatement ps = null;
        BufferedReader br = null;
        List<String> energySql = new ArrayList<>();
        try {
            br = new BufferedReader(new InputStreamReader(file.getInputStream()));
            String line;
            //INSERT INTO `energy` VALUES ('4303130802328', '2024-04-10', '0', '2800', '2240', '1520', '6560');
            while ((line = br.readLine()) != null) {
                if (line.contains("INSERT INTO `energy`")) {

                    String custNo = line.substring(30, 43);
                    String date = line.substring(47, 57);

                    QueryWrapper<CorpConsRealEnergyDo> energyWrap = new QueryWrapper<>();
                    energyWrap.eq("cons_no", custNo);
                    energyWrap.eq("data_date", date);
                    Long count = corpConsRealEnergyMapper.selectCount(energyWrap);
                    if (count <= 0) {
                        QueryWrapper<CorpConsDo> consWrap = new QueryWrapper<>();
                        consWrap.eq("cons_no", custNo);
                        CorpConsDo corpConsDo = corpConsMapper.selectOne(consWrap);
                        String id = corpConsDo.getId();
                        String sql = sql_prefix + id + "," + line.substring(29, line.length());
                        energySql.add(sql);
                    }
                }
            }
            connection = dataSource.getConnection();
            for (String sql : energySql) {
                log.info("sql:" + sql);
                ps = connection.prepareStatement(sql);
                ps.executeUpdate();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ServiceResult.error(500, "energy数据导入失败！");
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return ServiceResult.success("energy数据导入成功！");
    }
}
