package com.bobandata.cloud.trade.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;


/**
 * @Description  
 * <AUTHOR>
 * @Date 2024-05-29 
 */
@Data
@TableName("trade_notice_list_hn")
public class TradeNoticeListHnDo implements Serializable {

	private static final long serialVersionUID =  4556356590604491241L;

	private String tradeseqId;

	private String tradeseqCaption;

	private Timestamp beginDate;

	private Timestamp endDate;

	private String pubTime;

	private String priority;

	private String tradeChannel;

	private String tradeComments;

	private String isPub;

	private String tradetypeId;

	private String tradeGgId;

	private String tradeRole;

	private String targetEnergy;

	private String tradeTypeMode;

	private Integer isThreePart;

	private String subtradeTypeCode;

	private String yearMonth;

	private String createTime;

	private String tradeseqName;

	private String tradeTypeModo;

	private String declareState;

	private String tradeState;

	private String taradeTypeId;

	private String declareRole;

	private String tradeTyppeName;

	private LocalDateTime declareBeginDate;

	private LocalDateTime declareEndDate;

	private String restrainPubState;

	private String norestrainPubState;

	private String jyConfigs;

	private String trFiles;

	private String pubStatus;

	private String declareType;

	private Integer intentionTradeSeq;

	private String subTradetypeName;

	private String dailyPart;

	private String bandNum;

	private String pledgetemplateId;

	private String protocolId;

	private Integer independent;

	private String bandBibidTradeSeq;

	private String isNewEnergy;

	private Integer isSelectContract;

	private Integer isCrossPlatform;

	private Integer isCommitment;

	private String parentTradetypeCode;

	private String tradetypeModel;

}
