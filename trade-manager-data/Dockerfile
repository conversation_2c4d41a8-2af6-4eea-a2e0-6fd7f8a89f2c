FROM registry.cn-hangzhou.aliyuncs.com/boban-devops/gitlab-docker-runner:1.1 as builder

WORKDIR /workspace

COPY . ./

RUN mvn -DskipTests -Dcheckstyle.skip=true clean package -s=.mvn/settings_boban.xml -q

FROM registry.cn-hangzhou.aliyuncs.com/boban/java:opencv-3.4.15

# 安装中文语言包
RUN apt-get update && \
    apt-get install -y locales && \
    locale-gen zh_CN.UTF-8 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh \
    LC_ALL=zh_CN.UTF-8

COPY --from=builder /workspace/trade-manager-data/target/trade-manager-data-1.0-SNAPSHOT.jar /workspace/trade-manager-data-1.0-SNAPSHOT.jar
EXPOSE 20020

ENTRYPOINT ["java", "-Djava.library.path=/usr/local/share/OpenCV/java", "-jar", "/workspace/trade-manager-data-1.0-SNAPSHOT.jar" ]
