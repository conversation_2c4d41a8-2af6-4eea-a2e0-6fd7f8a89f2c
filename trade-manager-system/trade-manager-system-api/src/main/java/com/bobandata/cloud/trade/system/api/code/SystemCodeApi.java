package com.bobandata.cloud.trade.system.api.code;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.api.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = ApiConstants.NAME,url = "http://${trade.manager.system.server:localhost:20081}/trade/system")
@Tag(name =  "RPC 服务 - 系统编码")
public interface SystemCodeApi {

    String PREFIX = ApiConstants.PREFIX + "/system-code";

    @PostMapping(PREFIX + "/list")
    @Operation(summary = "查询所有系统统一编码")
    ServiceResult<List<SystemCodeRespDto>> getSystemCodeList(Timestamp maxUpdateTime);
}
