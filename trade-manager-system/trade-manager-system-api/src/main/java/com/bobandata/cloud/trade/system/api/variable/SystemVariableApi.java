package com.bobandata.cloud.trade.system.api.variable;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.api.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(name = ApiConstants.NAME, url = "http://${trade.manager.system.server:localhost:20081}/trade/system")
@Tag(name = "RPC 服务 - 系统变量")
public interface SystemVariableApi {

    String PREFIX = ApiConstants.PREFIX + "/system-var";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "查询所有变量")
    ServiceResult<List<SystemVariableRespDTO>> getSysVarList(Timestamp maxUpdateTime);

    @PostMapping(PREFIX + "/init")
    @Operation(summary = "初始化变量")
    ServiceResult<Boolean> autoGenerateSysVarList(@RequestBody List<VariableAutoGenerateReqDTO> autoGenerateDTOs);
}
