package com.bobandata.cloud.trade.mvc.query;

import com.bobandata.cloud.trade.system.TradeSystemApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024-01-11日 15:37
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TradeSystemApplication.class})
public class ITTestBaseService {

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Test
    public void testBinder() {
        String encode = passwordEncoder.encode("123456");
        System.out.println("encode = " + encode);
    }

}
