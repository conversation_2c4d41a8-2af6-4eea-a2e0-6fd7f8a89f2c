GET {{host}}/trade/system/rpc-api/system-var/list
Content-Type: application/json

{
  "data": [
    {
      "bidCount": 0.0,
      "compensate": 0.0,
      "date": "",
      "declareCount": 0.0,
      "guid": "1221",
      "responseEnergy": 0.0,
      "responseType": "1",
      "startCount": 0.0,
      "totalDemand": 0.0,
      "tradeClear": 0.0,
      "tradeDeclare": 0.0
    },
    {
      "bidCount": 0.0,
      "compensate": 0.0,
      "date": "",
      "declareCount": 0.0,
      "guid": "1221",
      "responseEnergy": 0.0,
      "responseType": "2",
      "startCount": 0.0,
      "totalDemand": 0.0,
      "tradeClear": 0.0,
      "tradeDeclare": 0.0
    }
  ],
  "msgId": "1780864406415093760",
  "serviceCode": "YTH_XH6666"
}