package com.bobandata.cloud.trade.system.convert.logger;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.api.logger.dto.LoginLogCreateReqDTO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogRespVO;
import com.bobandata.cloud.trade.system.dal.dataobject.logger.LoginLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface LoginLogConvert {

    LoginLogConvert INSTANCE = Mappers.getMapper(LoginLogConvert.class);

    PagingResult<LoginLogRespVO> convertPage(PagingResult<LoginLogDO> page);

    LoginLogDO convert(LoginLogCreateReqDTO bean);

}
