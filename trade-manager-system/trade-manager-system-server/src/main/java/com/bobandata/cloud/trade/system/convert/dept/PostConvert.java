package com.bobandata.cloud.trade.system.convert.dept;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostCreateReqVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostRespVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostSimpleRespVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.PostDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PostConvert {

    PostConvert INSTANCE = Mappers.getMapper(PostConvert.class);

    List<PostSimpleRespVO> convertList02(List<PostDO> list);

    PagingResult<PostRespVO> convertPage(PagingResult<PostDO> page);

    PostRespVO convert(PostDO id);

    PostDO convert(PostCreateReqVO bean);

    PostDO convert(PostUpdateReqVO reqVO);

}
