package com.bobandata.cloud.trade.system.dal.mysql.permission;

import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.bobandata.cloud.trade.system.controller.permission.vo.role.RoleExportReqVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.role.RolePageReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.RoleDO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.lang.Nullable;

@Mapper
public interface RoleMapper extends BaseCrudMapper<RoleDO> {

    default PagingResult<RoleDO> selectPage(RolePageReqVO reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<RoleDO>()
                .likeIfPresent(RoleDO::getName, reqVO.getName())
                .likeIfPresent(RoleDO::getCode, reqVO.getCode())
                .eqIfPresent(RoleDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(BaseDo::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RoleDO::getId));
    }

    default List<RoleDO> selectList(RoleExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RoleDO>()
                                  .likeIfPresent(RoleDO::getName, reqVO.getName())
                                  .likeIfPresent(RoleDO::getCode, reqVO.getCode())
                                  .eqIfPresent(RoleDO::getStatus, reqVO.getStatus())
                                  .betweenIfPresent(BaseDo::getCreateTime, reqVO.getCreateTime()));
    }

    default RoleDO selectByName(String name) {
        return selectOne(RoleDO::getName, name);
    }

    default RoleDO selectByCode(String code) {
        return selectOne(RoleDO::getCode, code);
    }

    default List<RoleDO> selectListByStatus(@Nullable Collection<Integer> statuses) {
        return selectList(RoleDO::getStatus, statuses);
    }

}
