package com.bobandata.cloud.trade.system.convert.dept;

import com.bobandata.cloud.trade.system.api.dept.dto.DeptRespDTO;
import com.bobandata.cloud.trade.system.controller.dept.vo.dept.DeptCreateReqVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.dept.DeptRespVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.dept.DeptSimpleRespVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.dept.DeptUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.DeptDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DeptConvert {

    DeptConvert INSTANCE = Mappers.getMapper(DeptConvert.class);

    List<DeptRespVO> convertList(List<DeptDO> list);

    List<DeptSimpleRespVO> convertList02(List<DeptDO> list);

    DeptRespVO convert(DeptDO bean);

    DeptDO convert(DeptCreateReqVO bean);

    DeptDO convert(DeptUpdateReqVO bean);

    List<DeptRespDTO> convertList03(List<DeptDO> list);

    DeptRespDTO convert03(DeptDO bean);

}
