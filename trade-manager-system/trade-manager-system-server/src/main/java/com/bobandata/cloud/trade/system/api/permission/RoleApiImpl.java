package com.bobandata.cloud.trade.system.api.permission;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.service.permission.RoleService;
import java.util.Collection;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;

    @Override
    public ServiceResult<Boolean> validRoleList(Collection<Long> ids) {
        roleService.validateRoleList(ids);
        return success(true);
    }
}
