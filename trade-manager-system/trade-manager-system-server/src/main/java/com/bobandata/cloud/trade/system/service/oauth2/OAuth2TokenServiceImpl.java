package com.bobandata.cloud.trade.system.service.oauth2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.common.enums.GlobalErrorCodeConstants;
import com.bobandata.cloud.common.utils.date.DateUtils;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2ClientDO;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import com.bobandata.cloud.trade.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import com.bobandata.cloud.trade.system.dal.mysql.oauth2.OAuth2RefreshTokenMapper;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.bobandata.cloud.common.exception.util.ServiceExceptionUtil.exception0;
import static com.bobandata.cloud.common.utils.array.CollectionUtils.convertSet;

/**
 * OAuth2.0 Token Service 实现类
 */
@Slf4j
@Service
public class OAuth2TokenServiceImpl extends ServiceImpl<OAuth2AccessTokenMapper, OAuth2AccessTokenDO> implements OAuth2TokenService {

    @Resource
    private OAuth2AccessTokenMapper oauth2AccessTokenMapper;
    @Resource
    private OAuth2RefreshTokenMapper oauth2RefreshTokenMapper;

    @Resource
    private OAuth2ClientService oauth2ClientService;

    @Override
    @Transactional
    public OAuth2AccessTokenDO createAccessToken(Long userId, Integer userType, String clientId, List<String> scopes) {
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 创建刷新令牌
        OAuth2RefreshTokenDO refreshTokenDO = createOAuth2RefreshToken(userId, userType, clientDO, scopes);
        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO refreshAccessToken(String refreshToken, String clientId) {
        // 查询访问令牌
        OAuth2RefreshTokenDO refreshTokenDO = oauth2RefreshTokenMapper.selectByRefreshToken(refreshToken);
        if (refreshTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "无效的刷新令牌");
        }

        // 校验 Client 匹配
        OAuth2ClientDO clientDO = oauth2ClientService.validOAuthClientFromCache(clientId);
        if (ObjectUtil.notEqual(clientId, refreshTokenDO.getClientId())) {
            throw exception0(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "刷新令牌的客户端编号不正确");
        }

        // 移除相关的访问令牌
        List<OAuth2AccessTokenDO> accessTokenDOs = oauth2AccessTokenMapper.selectListByRefreshToken(refreshToken);
        if (CollUtil.isNotEmpty(accessTokenDOs)) {
            oauth2AccessTokenMapper.deleteBatchIds(convertSet(accessTokenDOs, OAuth2AccessTokenDO::getId));
        }

        // 已过期的情况下，删除刷新令牌
        if (DateUtils.isExpired(refreshTokenDO.getExpiresTime())) {
            oauth2RefreshTokenMapper.deleteById(refreshTokenDO.getId());
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "刷新令牌已过期");
        }

        // 创建访问令牌
        return createOAuth2AccessToken(refreshTokenDO, clientDO);
    }

    @Override
    public OAuth2AccessTokenDO getAccessToken(String accessToken) {
        // todo 优先从 缓存 中获取
        // 获取不到，从 MySQL 中获取
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        // 如果在 MySQL 存在，则往 缓存 中写入

        return accessTokenDO;
    }

    @Override
    public OAuth2AccessTokenDO checkAccessToken(String accessToken) {
        OAuth2AccessTokenDO accessTokenDO = getAccessToken(accessToken);
        if (accessTokenDO == null) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌不存在");
        }
        if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
            throw exception0(GlobalErrorCodeConstants.UNAUTHORIZED.getCode(), "访问令牌已过期");
        }
        return accessTokenDO;
    }

    // todo 定时删除过期token
    @Transactional
    public void removeAccessToken() {
        List<OAuth2AccessTokenDO> accessTokenDOS = this.list();
        List<OAuth2AccessTokenDO> expiredToken = new ArrayList<>();
        for (final OAuth2AccessTokenDO accessTokenDO : accessTokenDOS) {
            if (DateUtils.isExpired(accessTokenDO.getExpiresTime())) {
                expiredToken.add(accessTokenDO);
            }
        }
        if (CollUtil.isNotEmpty(expiredToken)) {
            boolean b = this.removeBatchByIds(accessTokenDOS);
            List<String> refreshTokens = expiredToken.stream()
                                                     .map(OAuth2AccessTokenDO::getRefreshToken)
                                                     .collect(Collectors.toList());
            oauth2RefreshTokenMapper.deleteByRefreshToken(refreshTokens);
            log.debug("删除过期token {}", expiredToken.size());
        }
    }

    @Override
    public OAuth2AccessTokenDO removeAccessToken(String accessToken) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2AccessTokenMapper.selectByAccessToken(accessToken);
        if (accessTokenDO == null) {
            return null;
        }
        oauth2AccessTokenMapper.deleteById(accessTokenDO.getId());

        // 删除刷新令牌
        oauth2RefreshTokenMapper.deleteByRefreshToken(accessTokenDO.getRefreshToken());
        return accessTokenDO;
    }

    @Override
    public PagingResult<OAuth2AccessTokenDO> getAccessTokenPage(OAuth2AccessTokenPageReqVO reqVO) {
        return oauth2AccessTokenMapper.selectPage(reqVO);
    }

    private OAuth2AccessTokenDO createOAuth2AccessToken(OAuth2RefreshTokenDO refreshTokenDO, OAuth2ClientDO clientDO) {
        OAuth2AccessTokenDO accessTokenDO = new OAuth2AccessTokenDO().setAccessToken(generateAccessToken())
                                                                     .setUserId(refreshTokenDO.getUserId())
                                                                     .setUserType(refreshTokenDO.getUserType())
                                                                     .setClientId(clientDO.getClientId())
                                                                     .setScopes(refreshTokenDO.getScopes())
                                                                     .setRefreshToken(refreshTokenDO.getRefreshToken())
                                                                     .setExpiresTime(LocalDateTime.now()
                                                                                                  .plusSeconds(
                                                                                                          clientDO.getAccessTokenValiditySeconds()));
        //        accessTokenDO.setTenantId(TenantContextHolder.getTenantId());
        oauth2AccessTokenMapper.insert(accessTokenDO);
        // 记录到 Redis 中
        //        oauth2AccessTokenRedisDAO.set(accessTokenDO);
        return accessTokenDO;
    }

    private OAuth2RefreshTokenDO createOAuth2RefreshToken(Long userId,
                                                          Integer userType,
                                                          OAuth2ClientDO clientDO,
                                                          List<String> scopes) {
        OAuth2RefreshTokenDO refreshToken = new OAuth2RefreshTokenDO().setRefreshToken(generateRefreshToken())
                                                                      .setUserId(userId)
                                                                      .setUserType(userType)
                                                                      .setClientId(clientDO.getClientId())
                                                                      .setScopes(scopes)
                                                                      .setExpiresTime(LocalDateTime.now()
                                                                                                   .plusSeconds(
                                                                                                           clientDO.getRefreshTokenValiditySeconds()));
        oauth2RefreshTokenMapper.insert(refreshToken);
        return refreshToken;
    }

    private static String generateAccessToken() {
        return IdUtil.fastSimpleUUID();
    }

    private static String generateRefreshToken() {
        return IdUtil.fastSimpleUUID();
    }

}
