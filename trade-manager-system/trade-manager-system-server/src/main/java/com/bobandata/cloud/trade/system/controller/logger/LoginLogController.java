package com.bobandata.cloud.trade.system.controller.logger;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogPageReqVO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogRespVO;
import com.bobandata.cloud.trade.system.convert.logger.LoginLogConvert;
import com.bobandata.cloud.trade.system.dal.dataobject.logger.LoginLogDO;
import com.bobandata.cloud.trade.system.service.logger.LoginLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 登录日志")
@RestController
@RequestMapping("/system/login-log")
@Validated
public class LoginLogController {

    @Resource
    private LoginLogService loginLogService;

    @GetMapping("/page")
    @Operation(summary = "获得登录日志分页列表")
    @PreAuthorize("@ss.hasPermission('system:login-log:query')")
    public PagingResult<LoginLogRespVO> getLoginLogPage(@Valid LoginLogPageReqVO reqVO) {
        PagingResult<LoginLogDO> page = loginLogService.getLoginLogPage(reqVO);
        return PagingResult.successWrap(LoginLogConvert.INSTANCE.convertPage(page));
    }

}