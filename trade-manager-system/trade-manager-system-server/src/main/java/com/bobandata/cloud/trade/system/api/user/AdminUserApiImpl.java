package com.bobandata.cloud.trade.system.api.user;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.api.user.dto.AdminUserRespDTO;
import com.bobandata.cloud.trade.system.convert.user.UserConvert;
import com.bobandata.cloud.trade.system.dal.dataobject.user.AdminUserDO;
import com.bobandata.cloud.trade.system.service.user.AdminUserService;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserService userService;

    @Override
    public ServiceResult<AdminUserRespDTO> getUser(Long id) {
        AdminUserDO user = userService.getUser(id);
        return success(UserConvert.INSTANCE.convert4(user));
    }

    @Override
    public ServiceResult<List<AdminUserRespDTO>> getUsers(Collection<Long> ids) {
        List<AdminUserDO> users = userService.getUserList(ids);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public ServiceResult<List<AdminUserRespDTO>> getUserListByDeptIds(Collection<Long> deptIds) {
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public ServiceResult<List<AdminUserRespDTO>> getUserListByPostIds(Collection<Long> postIds) {
        List<AdminUserDO> users = userService.getUserListByPostIds(postIds);
        return success(UserConvert.INSTANCE.convertList4(users));
    }

    @Override
    public ServiceResult<Boolean> validUserList(Set<Long> ids) {
        userService.validateUserList(ids);
        return success(true);
    }

}
