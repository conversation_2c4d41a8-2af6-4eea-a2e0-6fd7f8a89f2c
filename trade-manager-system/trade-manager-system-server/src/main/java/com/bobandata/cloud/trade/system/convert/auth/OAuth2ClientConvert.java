package com.bobandata.cloud.trade.system.convert.auth;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.client.OAuth2ClientCreateReqVO;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.client.OAuth2ClientRespVO;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.client.OAuth2ClientUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2ClientDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * OAuth2 客户端 Convert
 */
@Mapper
public interface OAuth2ClientConvert {

    OAuth2ClientConvert INSTANCE = Mappers.getMapper(OAuth2ClientConvert.class);

    OAuth2ClientDO convert(OAuth2ClientCreateReqVO bean);

    OAuth2ClientDO convert(OAuth2ClientUpdateReqVO bean);

    OAuth2ClientRespVO convert(OAuth2ClientDO bean);

    List<OAuth2ClientRespVO> convertList(List<OAuth2ClientDO> list);

    PagingResult<OAuth2ClientRespVO> convertPage(PagingResult<OAuth2ClientDO> page);

}
