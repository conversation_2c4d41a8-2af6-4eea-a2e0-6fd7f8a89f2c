package com.bobandata.cloud.trade.system.dal.mysql.oauth2;

import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2ApproveDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OAuth2ApproveMapper extends BaseCrudMapper<OAuth2ApproveDO> {

    default int update(OAuth2ApproveDO updateObj) {
        return update(updateObj, new LambdaQueryWrapperX<OAuth2ApproveDO>()
                .eq(OAuth2ApproveDO::getUserId, updateObj.getUserId())
                .eq(OAuth2ApproveDO::getUserType, updateObj.getUserType())
                .eq(OAuth2ApproveDO::getClientId, updateObj.getClientId())
                .eq(OAuth2ApproveDO::getScope, updateObj.getScope()));
    }

    default List<OAuth2ApproveDO> selectListByUserIdAndUserTypeAndClientId(Long userId,
                                                                           Integer userType,
                                                                           String clientId) {
        return selectList(new LambdaQueryWrapperX<OAuth2ApproveDO>()
                                  .eq(OAuth2ApproveDO::getUserId, userId)
                                  .eq(OAuth2ApproveDO::getUserType, userType)
                                  .eq(OAuth2ApproveDO::getClientId, clientId));
    }

}
