package com.bobandata.cloud.trade.system.dal.mysql.dept;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.UserPostDO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserPostMapper extends BaseCrudMapper<UserPostDO> {

    default List<UserPostDO> selectListByUserId(Long userId) {
        return selectList(UserPostDO::getUserId, userId);
    }

    default void deleteByUserIdAndPostId(Long userId, Collection<Long> postIds) {
        delete(new LambdaQueryWrapperX<UserPostDO>()
                       .eq(UserPostDO::getUserId, userId)
                       .in(UserPostDO::getPostId, postIds));
    }

    default List<UserPostDO> selectListByPostIds(Collection<Long> postIds) {
        return selectList(UserPostDO::getPostId, postIds);
    }

    default void deleteByUserId(Long userId) {
        delete(Wrappers.lambdaUpdate(UserPostDO.class).eq(UserPostDO::getUserId, userId));
    }
}
