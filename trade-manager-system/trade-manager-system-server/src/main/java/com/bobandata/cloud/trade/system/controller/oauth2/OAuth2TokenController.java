package com.bobandata.cloud.trade.system.controller.oauth2;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.api.enms.logger.LoginLogTypeEnum;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.token.OAuth2AccessTokenRespVO;
import com.bobandata.cloud.trade.system.convert.auth.OAuth2TokenConvert;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.bobandata.cloud.trade.system.service.auth.AdminAuthService;
import com.bobandata.cloud.trade.system.service.oauth2.OAuth2TokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;

@Tag(name = "管理后台 - OAuth2.0 令牌")
@RestController
@RequestMapping("/system/oauth2-token")
public class OAuth2TokenController {

    @Resource
    private OAuth2TokenService oauth2TokenService;
    @Resource
    private AdminAuthService authService;

    @GetMapping("/page")
    @Operation(summary = "获得访问令牌分页", description = "只返回有效期内的")
    @PreAuthorize("@ss.hasPermission('system:oauth2-token:page')")
    public PagingResult<OAuth2AccessTokenRespVO> getAccessTokenPage(@Valid OAuth2AccessTokenPageReqVO reqVO) {
        PagingResult<OAuth2AccessTokenDO> pageResult = oauth2TokenService.getAccessTokenPage(reqVO);
        return PagingResult.successWrap(OAuth2TokenConvert.INSTANCE.convert(pageResult));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除访问令牌")
    @Parameter(name = "accessToken", description = "访问令牌", required = true, example = "tudou")
    @PreAuthorize("@ss.hasPermission('system:oauth2-token:delete')")
    public ServiceResult<Boolean> deleteAccessToken(@RequestParam("accessToken") String accessToken) {
        authService.logout(accessToken, LoginLogTypeEnum.LOGOUT_DELETE.getType());
        return success(true);
    }

}