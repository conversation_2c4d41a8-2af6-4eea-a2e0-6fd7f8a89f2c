package com.bobandata.cloud.trade.system;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @date 2023-11-06日 15:25
 * @description
 */
@MapperScan(basePackages = {
        "com.bobandata.cloud.trade.*.dal.mysql"
})
@SpringBootApplication(scanBasePackages = "com.bobandata")
public class TradeSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(TradeSystemApplication.class, args);
    }
}
