package com.bobandata.cloud.trade.system.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.api.code.SystemCodeApi;
import com.bobandata.cloud.trade.system.api.code.SystemCodeRespDto;
import com.bobandata.cloud.trade.system.dal.dataobject.SysDictCodeDo;
import com.bobandata.cloud.trade.system.service.SysDictCodeService;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-11-16日 10:09
 * @description
 */
@RestController
public class SystemCodeApiImpl implements SystemCodeApi {

    @Autowired
    private SysDictCodeService dictCodeService;

    @Override
    public ServiceResult<List<SystemCodeRespDto>> getSystemCodeList(Timestamp maxUpdateTime) {
        List<SysDictCodeDo> sysDictCodeDos = dictCodeService.list(
                new LambdaQueryWrapper<SysDictCodeDo>().gt(SysDictCodeDo::getLastRefreshTime, maxUpdateTime));
        List<SystemCodeRespDto> codeRespDtos = sysDictCodeDos
                .stream()
                .map(sysDictCodeDo -> SystemCodeRespDto.builder()
                                                       .dictKey(sysDictCodeDo.getValue())
                                                       .dictLabel(sysDictCodeDo.getLabel())
                                                       .lastUpdateTime(sysDictCodeDo.getLastRefreshTime())
                                                       .build())
                .collect(Collectors.toList());
        return ServiceResult.success(codeRespDtos);
    }
}
