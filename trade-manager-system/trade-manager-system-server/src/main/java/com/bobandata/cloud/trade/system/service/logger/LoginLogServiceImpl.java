package com.bobandata.cloud.trade.system.service.logger;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.api.logger.dto.LoginLogCreateReqDTO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogExportReqVO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogPageReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.logger.LoginLogDO;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 登录日志 Service 实现
 */
@Service
@Validated
public class LoginLogServiceImpl implements LoginLogService {

    //    @Resource
    //    private LoginLogMapper loginLogMapper;

    @Override
    public PagingResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO reqVO) {
        return null;
    }

    @Override
    public List<LoginLogDO> getLoginLogList(LoginLogExportReqVO reqVO) {
        return null;
    }

    @Override
    public void createLoginLog(LoginLogCreateReqDTO reqDTO) {
        //        LoginLogDO loginLog = LoginLogConvert.INSTANCE.convert(reqDTO);
        //        loginLogMapper.insert(loginLog);
    }

}
