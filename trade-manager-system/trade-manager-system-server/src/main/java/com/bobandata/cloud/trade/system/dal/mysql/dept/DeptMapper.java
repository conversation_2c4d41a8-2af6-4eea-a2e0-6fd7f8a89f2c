package com.bobandata.cloud.trade.system.dal.mysql.dept;

import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.system.controller.dept.vo.dept.DeptListReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.DeptDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DeptMapper extends BaseCrudMapper<DeptDO> {

    default List<DeptDO> selectList(DeptListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DeptDO>()
                                  .likeIfPresent(DeptDO::getName, reqVO.getName())
                                  .eqIfPresent(DeptDO::getStatus, reqVO.getStatus()));
    }

    default DeptDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(DeptDO::getParentId, parentId, DeptDO::getName, name);
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(DeptDO::getParentId, parentId);
    }

}
