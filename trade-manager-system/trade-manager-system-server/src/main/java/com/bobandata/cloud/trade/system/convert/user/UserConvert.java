package com.bobandata.cloud.trade.system.convert.user;

import com.bobandata.cloud.trade.system.api.user.dto.AdminUserRespDTO;
import com.bobandata.cloud.trade.system.controller.user.vo.profile.UserProfileRespVO;
import com.bobandata.cloud.trade.system.controller.user.vo.profile.UserProfileUpdatePasswordReqVO;
import com.bobandata.cloud.trade.system.controller.user.vo.profile.UserProfileUpdateReqVO;
import com.bobandata.cloud.trade.system.controller.user.vo.user.UserCreateReqVO;
import com.bobandata.cloud.trade.system.controller.user.vo.user.UserPageItemRespVO;
import com.bobandata.cloud.trade.system.controller.user.vo.user.UserSimpleRespVO;
import com.bobandata.cloud.trade.system.controller.user.vo.user.UserUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.DeptDO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.PostDO;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.RoleDO;
import com.bobandata.cloud.trade.system.dal.dataobject.user.AdminUserDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    UserPageItemRespVO convert(AdminUserDO bean);

    UserPageItemRespVO.Dept convert(DeptDO bean);

    AdminUserDO convert(UserCreateReqVO bean);

    AdminUserDO convert(UserUpdateReqVO bean);

    UserProfileRespVO convert03(AdminUserDO bean);

    List<UserProfileRespVO.Role> convertList(List<RoleDO> list);

    UserProfileRespVO.Dept convert02(DeptDO bean);

    AdminUserDO convert(UserProfileUpdateReqVO bean);

    AdminUserDO convert(UserProfileUpdatePasswordReqVO bean);

    List<UserProfileRespVO.Post> convertList02(List<PostDO> list);

    List<UserSimpleRespVO> convertList04(List<AdminUserDO> list);

    AdminUserRespDTO convert4(AdminUserDO bean);

    List<AdminUserRespDTO> convertList4(List<AdminUserDO> users);

}
