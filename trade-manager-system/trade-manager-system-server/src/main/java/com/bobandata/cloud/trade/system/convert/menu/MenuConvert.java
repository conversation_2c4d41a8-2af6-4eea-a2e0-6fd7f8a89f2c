package com.bobandata.cloud.trade.system.convert.menu;

import com.bobandata.cloud.trade.system.controller.permission.vo.menu.MenuCreateReqVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.menu.MenuRespVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.menu.MenuSimpleRespVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.menu.MenuUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.MenuDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MenuConvert {

    MenuConvert INSTANCE = Mappers.getMapper(MenuConvert.class);

    List<MenuRespVO> convertList(List<MenuDO> list);

    MenuDO convert(MenuCreateReqVO bean);

    MenuDO convert(MenuUpdateReqVO bean);

    MenuRespVO convert(MenuDO bean);

    List<MenuSimpleRespVO> convertList02(List<MenuDO> list);

}
