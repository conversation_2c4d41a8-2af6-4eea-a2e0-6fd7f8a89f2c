package com.bobandata.cloud.trade.system.dal.mysql.oauth2;

import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OAuth2RefreshTokenMapper extends BaseCrudMapper<OAuth2RefreshTokenDO> {

    default int deleteByRefreshToken(String refreshToken) {
        return delete(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
                              .eq(OAuth2RefreshTokenDO::getRefreshToken, refreshToken));
    }

    default int deleteByRefreshToken(List<String> refreshTokens) {
        return delete(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
                              .inIfPresent(OAuth2RefreshTokenDO::getRefreshToken, refreshTokens));
    }

    default OAuth2RefreshTokenDO selectByRefreshToken(String refreshToken) {
        return selectOne(OAuth2RefreshTokenDO::getRefreshToken, refreshToken);
    }

}
