package com.bobandata.cloud.trade.system.controller.auth;

import cn.hutool.core.util.StrUtil;
import com.bobandata.cloud.common.enums.CommonStatusEnum;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.common.utils.array.SetUtils;
import com.bobandata.cloud.trade.operatelog.core.annotations.OperateLog;
import com.bobandata.cloud.trade.security.config.SecurityProperties;
import com.bobandata.cloud.trade.system.api.enms.logger.LoginLogTypeEnum;
import com.bobandata.cloud.trade.system.api.enms.permission.MenuTypeEnum;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthLoginReqVO;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthLoginRespVO;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthMenuRespVO;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthPermissionInfoRespVO;
import com.bobandata.cloud.trade.system.convert.auth.AuthConvert;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.MenuDO;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.RoleDO;
import com.bobandata.cloud.trade.system.dal.dataobject.user.AdminUserDO;
import com.bobandata.cloud.trade.system.service.auth.AdminAuthService;
import com.bobandata.cloud.trade.system.service.permission.PermissionService;
import com.bobandata.cloud.trade.system.service.permission.RoleService;
import com.bobandata.cloud.trade.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.bobandata.cloud.common.pojo.ServiceResult.success;
import static com.bobandata.cloud.trade.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.bobandata.cloud.trade.security.core.util.SecurityFrameworkUtils.obtainAuthorization;
import static java.util.Collections.singleton;

@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/system/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
    private AdminAuthService authService;
    @Resource
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private PermissionService permissionService;

    @Resource
    private SecurityProperties securityProperties;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "使用账号密码登录")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public ServiceResult<AuthLoginRespVO> login(@RequestBody @Valid AuthLoginReqVO reqVO) {
        return success(authService.login(reqVO));
    }

    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public ServiceResult<Boolean> logout(HttpServletRequest request) {
        String token = obtainAuthorization(request, securityProperties.getTokenHeader());
        if (StrUtil.isNotBlank(token)) {
            authService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    @OperateLog(enable = false) // 避免 Post 请求被记录操作日志
    public ServiceResult<AuthLoginRespVO> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return success(authService.refreshToken(refreshToken));
    }

    @GetMapping("/get-permission-info")
    @Operation(summary = "获取登录用户的权限信息")
    public ServiceResult<AuthPermissionInfoRespVO> getPermissionInfo() {
        // 获得用户信息
        AdminUserDO user = userService.getUser(getLoginUserId());
        if (user == null) {
            return null;
        }
        // 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdsFromCache(
                getLoginUserId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        List<RoleDO> roleList = roleService.getRoleListFromCache(roleIds);
        // 获得菜单列表
        List<MenuDO> menuList = permissionService.getRoleMenuListFromCache(
                roleIds,
                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType(), MenuTypeEnum.BUTTON.getType()),
                singleton(CommonStatusEnum.ENABLE.getStatus())
        ); // 只要开启的
        // 拼接结果返回
        return success(AuthConvert.INSTANCE.convert(user, roleList, menuList));
    }

    @GetMapping("/list-menus")
    @Operation(summary = "获得登录用户的菜单列表")
    public ServiceResult<List<AuthMenuRespVO>> getMenus() {
        // 获得角色列表
        Set<Long> roleIds = permissionService.getUserRoleIdsFromCache(
                getLoginUserId(), singleton(CommonStatusEnum.ENABLE.getStatus()));
        // 获得用户拥有的菜单列表
        List<MenuDO> menuList = permissionService.getRoleMenuListFromCache(
                roleIds,
                SetUtils.asSet(MenuTypeEnum.DIR.getType(), MenuTypeEnum.MENU.getType()), // 只要目录和菜单类型
                singleton(CommonStatusEnum.ENABLE.getStatus())
        ); // 只要开启的
        // 转换成 Tree 结构返回
        return success(AuthConvert.INSTANCE.buildMenuTree(menuList));
    }

}