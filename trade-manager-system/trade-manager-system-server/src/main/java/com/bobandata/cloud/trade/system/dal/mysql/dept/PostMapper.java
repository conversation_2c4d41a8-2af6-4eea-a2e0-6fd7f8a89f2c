package com.bobandata.cloud.trade.system.dal.mysql.dept;

import com.bobandata.cloud.trade.mvc.core.query.LambdaQueryWrapperX;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostExportReqVO;
import com.bobandata.cloud.trade.system.controller.dept.vo.post.PostPageReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.PostDO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PostMapper extends BaseCrudMapper<PostDO> {

    default List<PostDO> selectList(Collection<Long> ids, Collection<Integer> statuses) {
        return selectList(new LambdaQueryWrapperX<PostDO>()
                                  .inIfPresent(PostDO::getId, ids)
                                  .inIfPresent(PostDO::getStatus, statuses));
    }

    default PagingResult<PostDO> selectPage(PostPageReqVO reqVO) {
        return selectPage(reqVO.toPagination(), new LambdaQueryWrapperX<PostDO>()
                .likeIfPresent(PostDO::getCode, reqVO.getCode())
                .likeIfPresent(PostDO::getName, reqVO.getName())
                .eqIfPresent(PostDO::getStatus, reqVO.getStatus())
                .orderByDesc(PostDO::getId));
    }

    default List<PostDO> selectList(PostExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<PostDO>()
                                  .likeIfPresent(PostDO::getCode, reqVO.getCode())
                                  .likeIfPresent(PostDO::getName, reqVO.getName())
                                  .eqIfPresent(PostDO::getStatus, reqVO.getStatus()));
    }

    default PostDO selectByName(String name) {
        return selectOne(PostDO::getName, name);
    }

    default PostDO selectByCode(String code) {
        return selectOne(PostDO::getCode, code);
    }

}
