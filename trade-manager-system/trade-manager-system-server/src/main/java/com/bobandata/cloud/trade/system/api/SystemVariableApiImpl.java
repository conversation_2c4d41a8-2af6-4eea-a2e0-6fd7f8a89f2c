package com.bobandata.cloud.trade.system.api;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.system.api.variable.SystemVariableApi;
import com.bobandata.cloud.trade.system.api.variable.SystemVariableRespDTO;
import com.bobandata.cloud.trade.system.api.variable.VariableAutoGenerateReqDTO;
import com.bobandata.cloud.trade.system.service.SysVariableConfigService;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-11-20日 10:34
 * @description
 */
@RestController
public class SystemVariableApiImpl implements SystemVariableApi {

    @Autowired
    private SysVariableConfigService variableConfigService;

    @Override
    public ServiceResult<List<SystemVariableRespDTO>> getSysVarList(final Timestamp maxUpdateTime) {
        //        List<SysVariableConfigDo> sysVariableConfigDos = variableConfigService.list(
        //                new LambdaQueryWrapper<SysVariableConfigDo>()
        //                        .gt(SysVariableConfigDo::getLastRefreshTime, maxUpdateTime)
        //        );
        //        List<SystemVariableRespDTO> codeRespDtos = sysVariableConfigDos
        //                .stream()
        //                .map(sysVariableConfigDo -> SystemVariableRespDTO.builder()
        //                                                                 .var(sysVariableConfigDo.getVariableKey())
        //                                                                 .value(sysVariableConfigDo.getVariableValue())
        //                                                                 .lastUpdateTime(
        //                                                                         sysVariableConfigDo.getLastRefreshTime())
        //                                                                 .build()).collect(Collectors.toList());
        return ServiceResult.success();
    }

    @Override
    public ServiceResult<Boolean> autoGenerateSysVarList(final List<VariableAutoGenerateReqDTO> autoGenerateDTOs) {
        variableConfigService.autoGenerateSysVarList(autoGenerateDTOs);
        return ServiceResult.success(true);
    }
}
