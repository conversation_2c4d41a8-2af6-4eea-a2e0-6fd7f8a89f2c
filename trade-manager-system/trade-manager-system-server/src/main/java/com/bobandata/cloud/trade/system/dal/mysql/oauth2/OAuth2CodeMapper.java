package com.bobandata.cloud.trade.system.dal.mysql.oauth2;

import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2CodeDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OAuth2CodeMapper extends Base<PERSON>rudMapper<OAuth2CodeDO> {

    default OAuth2CodeDO selectByCode(String code) {
        return selectOne(OAuth2CodeDO::getCode, code);
    }

}
