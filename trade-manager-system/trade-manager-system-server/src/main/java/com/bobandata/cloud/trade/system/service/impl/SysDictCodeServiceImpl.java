package com.bobandata.cloud.trade.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bobandata.cloud.trade.system.dal.dataobject.SysDictCodeDo;
import com.bobandata.cloud.trade.system.dal.mysql.SysDictCodeMapper;
import com.bobandata.cloud.trade.system.service.SysDictCodeService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_dict_code】的数据库操作Service实现
 * @createDate 2023-11-16 09:42:01
 */
@Service
public class SysDictCodeServiceImpl extends ServiceImpl<SysDictCodeMapper, SysDictCodeDo> implements SysDictCodeService {

}




