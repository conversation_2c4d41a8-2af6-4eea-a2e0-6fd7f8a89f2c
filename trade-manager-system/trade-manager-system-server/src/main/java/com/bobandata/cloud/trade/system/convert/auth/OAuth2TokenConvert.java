package com.bobandata.cloud.trade.system.convert.auth;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import com.bobandata.cloud.trade.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.token.OAuth2AccessTokenRespVO;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OAuth2TokenConvert {

    OAuth2TokenConvert INSTANCE = Mappers.getMapper(OAuth2TokenConvert.class);

    OAuth2AccessTokenCheckRespDTO convert(OAuth2AccessTokenDO bean);

    PagingResult<OAuth2AccessTokenRespVO> convert(PagingResult<OAuth2AccessTokenDO> page);

    OAuth2AccessTokenRespDTO convert2(OAuth2AccessTokenDO bean);

}
