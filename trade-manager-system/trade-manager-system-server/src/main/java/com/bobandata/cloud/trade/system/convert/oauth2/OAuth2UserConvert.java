package com.bobandata.cloud.trade.system.convert.oauth2;

import com.bobandata.cloud.trade.system.controller.oauth2.vo.user.OAuth2UserInfoRespVO;
import com.bobandata.cloud.trade.system.controller.oauth2.vo.user.OAuth2UserUpdateReqVO;
import com.bobandata.cloud.trade.system.controller.user.vo.profile.UserProfileUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.DeptDO;
import com.bobandata.cloud.trade.system.dal.dataobject.dept.PostDO;
import com.bobandata.cloud.trade.system.dal.dataobject.user.AdminUserDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OAuth2UserConvert {

    OAuth2UserConvert INSTANCE = Mappers.getMapper(OAuth2UserConvert.class);

    OAuth2UserInfoRespVO convert(AdminUserDO bean);

    OAuth2UserInfoRespVO.Dept convert(DeptDO dept);

    List<OAuth2UserInfoRespVO.Post> convertList(List<PostDO> list);

    UserProfileUpdateReqVO convert(OAuth2UserUpdateReqVO bean);

}
