package com.bobandata.cloud.trade.system.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 维度表：全局变量配置表
 *
 * @TableName sys_variable_config
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_variable_config")
public class SysVariableConfigDo extends BaseDo {

    /**
     * 配置key
     */
    @TableField(value = "variable_key")
    private String variableKey;

    /**
     * 配置value
     */
    @TableField(value = "config_value")
    private String variableValue;

    /**
     * 使用状态 0停用 1使用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 配置信息详细描述
     */
    @TableField(value = "description")
    private String description;

}