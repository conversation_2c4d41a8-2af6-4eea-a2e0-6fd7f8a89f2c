package com.bobandata.cloud.trade.system.service.auth;

import cn.hutool.core.util.ObjectUtil;
import com.bobandata.cloud.common.enums.CommonStatusEnum;
import com.bobandata.cloud.common.enums.UserTypeEnum;
import com.bobandata.cloud.trade.system.api.enms.logger.LoginLogTypeEnum;
import com.bobandata.cloud.trade.system.api.enms.logger.LoginResultEnum;
import com.bobandata.cloud.trade.system.api.enms.oauth2.OAuth2ClientConstants;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthLoginReqVO;
import com.bobandata.cloud.trade.system.controller.auth.vo.AuthLoginRespVO;
import com.bobandata.cloud.trade.system.convert.auth.AuthConvert;
import com.bobandata.cloud.trade.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import com.bobandata.cloud.trade.system.dal.dataobject.user.AdminUserDO;
import com.bobandata.cloud.trade.system.service.oauth2.OAuth2TokenService;
import com.bobandata.cloud.trade.system.service.user.AdminUserService;
import javax.annotation.Resource;
import javax.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS;
import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.AUTH_LOGIN_USER_DISABLED;
import static com.bobandata.cloud.common.exception.util.ServiceExceptionUtil.exception;

/**
 * Auth Service 实现类
 */
@Service
@Slf4j
public class AdminAuthServiceImpl implements AdminAuthService {

    @Resource
    private AdminUserService userService;
    //    @Resource
    //    private LoginLogService loginLogService;
    @Resource
    private OAuth2TokenService oauth2TokenService;

    @Resource
    private Validator validator;

    @Override
    public AdminUserDO authenticate(String username, String password) {
        final LoginLogTypeEnum logTypeEnum = LoginLogTypeEnum.LOGIN_USERNAME;
        // 校验账号是否存在
        AdminUserDO user = userService.getUserByUsername(username);
        if (user == null) {
            createLoginLog(null, username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        if (!userService.isPasswordMatch(password, user.getPassword())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.BAD_CREDENTIALS);
            throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验是否禁用
        if (ObjectUtil.notEqual(user.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            createLoginLog(user.getId(), username, logTypeEnum, LoginResultEnum.USER_DISABLED);
            throw exception(AUTH_LOGIN_USER_DISABLED);
        }
        return user;
    }

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO reqVO) {
        // 校验验证码

        // 使用账号密码，进行登录
        AdminUserDO user = authenticate(reqVO.getUsername(), reqVO.getPassword());

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(user.getId(), reqVO.getUsername(), LoginLogTypeEnum.LOGIN_USERNAME);
    }

    private void createLoginLog(Long userId, String username,
                                LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {
        // 插入登录日志
        //            LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        //            reqDTO.setLogType(logTypeEnum.getType());
        //            reqDTO.setTraceId(TracerUtils.getTraceId());
        //            reqDTO.setUserId(userId);
        //            reqDTO.setUserType(getUserType().getValue());
        //            reqDTO.setUsername(username);
        //            reqDTO.setUserAgent(ServletUtils.getUserAgent());
        //            reqDTO.setUserIp(ServletUtils.getClientIP());
        //            reqDTO.setResult(loginResult.getResult());
        //            loginLogService.createLoginLog(reqDTO);
        //            // 更新最后登录时间
        //            if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
        //                userService.updateUserLogin(userId, ServletUtils.getClientIP());
        //            }
    }

    private AuthLoginRespVO createTokenAfterLoginSuccess(Long userId, String username, LoginLogTypeEnum logType) {
        // 插入登陆日志
        createLoginLog(userId, username, logType, LoginResultEnum.SUCCESS);
        // 创建访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.createAccessToken(userId, getUserType().getValue(),
                                                                                 OAuth2ClientConstants.CLIENT_ID_DEFAULT,
                                                                                 null
        );
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public AuthLoginRespVO refreshToken(String refreshToken) {
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.refreshAccessToken(
                refreshToken, OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        return AuthConvert.INSTANCE.convert(accessTokenDO);
    }

    @Override
    public void logout(String token, Integer logType) {
        // 删除访问令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.removeAccessToken(token);
        if (accessTokenDO == null) {
            return;
        }
        // 删除成功，则记录登出日志
        createLogoutLog(accessTokenDO.getUserId(), accessTokenDO.getUserType(), logType);
    }

    private void createLogoutLog(Long userId, Integer userType, Integer logType) {
        //        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        //        reqDTO.setLogType(logType);
        //        reqDTO.setTraceId(TracerUtils.getTraceId());
        //        reqDTO.setUserId(userId);
        //        reqDTO.setUserType(userType);
        //        if (ObjectUtil.equal(getUserType().getValue(), userType)) {
        //            reqDTO.setUsername(getUsername(userId));
        //        } else {
        //            reqDTO.setUsername(memberService.getMemberUserMobile(userId));
        //        }
        //        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        //        reqDTO.setUserIp(ServletUtils.getClientIP());
        //        reqDTO.setResult(LoginResultEnum.SUCCESS.getResult());
        //        loginLogService.createLoginLog(reqDTO);
    }

    private String getUsername(Long userId) {
        if (userId == null) {
            return null;
        }
        AdminUserDO user = userService.getUser(userId);
        return user != null ? user.getUsername() : null;
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.ADMIN;
    }

}
