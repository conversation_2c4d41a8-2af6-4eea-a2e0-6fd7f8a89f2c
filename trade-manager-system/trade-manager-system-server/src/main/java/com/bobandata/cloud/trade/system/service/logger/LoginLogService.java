package com.bobandata.cloud.trade.system.service.logger;

import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.system.api.logger.dto.LoginLogCreateReqDTO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogExportReqVO;
import com.bobandata.cloud.trade.system.controller.logger.vo.loginlog.LoginLogPageReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.logger.LoginLogDO;
import java.util.List;
import javax.validation.Valid;

/**
 * 登录日志 Service 接口
 */
public interface LoginLogService {

    /**
     * 获得登录日志分页
     *
     * @param reqVO 分页条件
     * @return 登录日志分页
     */
    PagingResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO reqVO);

    /**
     * 获得登录日志列表
     *
     * @param reqVO 列表条件
     * @return 登录日志列表
     */
    List<LoginLogDO> getLoginLogList(LoginLogExportReqVO reqVO);

    /**
     * 创建登录日志
     *
     * @param reqDTO 日志信息
     */
    void createLoginLog(@Valid LoginLogCreateReqDTO reqDTO);

}
