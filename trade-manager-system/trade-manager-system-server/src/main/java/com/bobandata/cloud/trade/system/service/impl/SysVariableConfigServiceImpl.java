package com.bobandata.cloud.trade.system.service.impl;

import com.bobandata.cloud.trade.system.api.variable.VariableAutoGenerateReqDTO;
import com.bobandata.cloud.trade.system.service.SysVariableConfigService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_variable_config(维度表：全局变量配置表)】的数据库操作Service实现
 * @createDate 2023-11-20 10:31:19
 */
@Service
public class SysVariableConfigServiceImpl implements SysVariableConfigService {

    @Override
    public void autoGenerateSysVarList(final List<VariableAutoGenerateReqDTO> autoGenerateDTOs) {

    }
}




