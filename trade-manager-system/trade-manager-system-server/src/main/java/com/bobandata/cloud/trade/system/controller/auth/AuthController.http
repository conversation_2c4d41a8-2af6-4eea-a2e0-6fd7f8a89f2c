### 请求 /login 接口 => 成功（无验证码)
POST {{baseUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

### 请求 /get-permission-info 接口 => 成功
GET {{baseUrl}}/system/auth/get-permission-info
Authorization: Bearer ebf2cd12320947d58ea522ca66fa519e

### 请求 /list-menus 接口 => 成功
GET {{baseUrl}}/system/auth/list-menus
#Authorization: Bearer {{token}}
Authorization: Bearer ebf2cd12320947d58ea522ca66fa519e
