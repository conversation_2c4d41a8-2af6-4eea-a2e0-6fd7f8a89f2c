package com.bobandata.cloud.trade.system.convert.menu;

import com.bobandata.cloud.trade.system.controller.permission.vo.role.RoleCreateReqVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.role.RoleRespVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.role.RoleSimpleRespVO;
import com.bobandata.cloud.trade.system.controller.permission.vo.role.RoleUpdateReqVO;
import com.bobandata.cloud.trade.system.dal.dataobject.permission.RoleDO;
import com.bobandata.cloud.trade.system.service.permission.bo.RoleCreateReqBO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RoleConvert {

    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);

    RoleDO convert(RoleUpdateReqVO bean);

    RoleRespVO convert(RoleDO bean);

    RoleDO convert(RoleCreateReqVO bean);

    List<RoleSimpleRespVO> convertList02(List<RoleDO> list);

    RoleDO convert(RoleCreateReqBO bean);

}
