package com.bobandata.cloud.trade.system.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @TableName sys_dict_code
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_dict_code")
public class SysDictCodeDo extends BaseDo {

    /**
     * 类型
     */
    private String dictType;

    /**
     * 权限
     */
    private String orgNo;

    /**
     * 代码
     */
    private String value;

    /**
     * 代码对应的显示值
     */
    private String label;

    /**
     * 描述
     */
    private String description;

    /**
     * 使用状态 0停用 1使用
     */
    private Integer status;

}