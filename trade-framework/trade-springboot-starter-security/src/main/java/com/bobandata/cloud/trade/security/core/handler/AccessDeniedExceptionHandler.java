package com.bobandata.cloud.trade.security.core.handler;

import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.web.handler.ExtendExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;

import static com.bobandata.cloud.common.enums.GlobalErrorCodeConstants.AUTH_ACCESS_DENIED;

/**
 * <AUTHOR>
 * @date 2024-03-11日 13:24
 * @description
 */
@Slf4j
public class AccessDeniedExceptionHandler implements ExtendExceptionHandler {

    @Override
    public ServiceResult<?> handlerException(final Throwable ex) {
        if (ex instanceof AccessDeniedException) {
            AccessDeniedException accessDeniedException = (AccessDeniedException) ex;
            log.warn(accessDeniedException.getMessage(), accessDeniedException);
            return ServiceResult.error(AUTH_ACCESS_DENIED.getCode(), accessDeniedException.getMessage());
        }
        return ExtendExceptionHandler.super.handlerException(ex);
    }
}
