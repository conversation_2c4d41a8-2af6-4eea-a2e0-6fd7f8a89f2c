package com.bobandata.cloud.trade.security.config;

import com.bobandata.cloud.trade.security.core.aop.PreAuthenticatedAspect;
import com.bobandata.cloud.trade.security.core.context.TransmittableThreadLocalSecurityContextHolderStrategy;
import com.bobandata.cloud.trade.security.core.filter.TokenAuthenticationFilter;
import com.bobandata.cloud.trade.security.core.handler.AccessDeniedExceptionHandler;
import com.bobandata.cloud.trade.security.core.handler.AccessDeniedHandlerImpl;
import com.bobandata.cloud.trade.security.core.handler.AuthenticationEntryPointImpl;
import com.bobandata.cloud.trade.security.core.service.SecurityFrameworkService;
import com.bobandata.cloud.trade.security.core.service.SecurityFrameworkServiceImpl;
import com.bobandata.cloud.trade.system.api.oauth2.OAuth2TokenApi;
import com.bobandata.cloud.trade.system.api.permission.PermissionApi;
import com.bobandata.cloud.web.handler.GlobalExceptionHandler;
import javax.annotation.Resource;
import org.springframework.beans.factory.config.MethodInvokingFactoryBean;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;

/**
 * Spring Security 自动配置类，主要用于相关组件的配置
 *
 * 注意，不能和 {@link TradeWebSecurityConfigurerAdapter} 用一个，原因是会导致初始化报错。 参见
 * https://stackoverflow.com/questions/53847050/spring-boot-delegatebuilder-cannot-be-null-on-autowiring-authenticationmanager
 * 文档。
 */
@AutoConfiguration
@EnableConfigurationProperties(SecurityProperties.class)
public class TradeSecurityAutoConfiguration {

    @Resource
    private SecurityProperties securityProperties;

    /**
     * 处理用户未登录拦截的切面的 Bean
     */
    @Bean
    public PreAuthenticatedAspect preAuthenticatedAspect() {
        return new PreAuthenticatedAspect();
    }

    /**
     * 认证失败处理类 Bean
     */
    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new AuthenticationEntryPointImpl();
    }

    /**
     * 权限不够处理器 Bean
     */
    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return new AccessDeniedHandlerImpl();
    }

    /**
     * Spring Security 加密器 考虑到安全性，这里采用 BCryptPasswordEncoder 加密器
     *
     * @see <a href="http://stackabuse.com/password-encoding-with-spring-security/">Password Encoding with Spring
     * Security</a>
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * Token 认证过滤器 Bean
     */
    @Bean
    public TokenAuthenticationFilter authenticationTokenFilter(GlobalExceptionHandler globalExceptionHandler,
                                                               OAuth2TokenApi oauth2TokenApi) {
        GlobalExceptionHandler.addExceptionHandler(new AccessDeniedExceptionHandler());
        return new TokenAuthenticationFilter(securityProperties, globalExceptionHandler, oauth2TokenApi);
    }

    @Bean("ss") // 使用 Spring Security 的缩写，方便使用
    public SecurityFrameworkService securityFrameworkService(PermissionApi permissionApi) {
        return new SecurityFrameworkServiceImpl(permissionApi, this.securityProperties);
    }

    /**
     * 声明调用 {@link SecurityContextHolder#setStrategyName(String)} 方法， 设置使用
     * {@link TransmittableThreadLocalSecurityContextHolderStrategy} 作为 Security 的上下文策略
     */
    @Bean
    public MethodInvokingFactoryBean securityContextHolderMethodInvokingFactoryBean() {
        MethodInvokingFactoryBean methodInvokingFactoryBean = new MethodInvokingFactoryBean();
        methodInvokingFactoryBean.setTargetClass(SecurityContextHolder.class);
        methodInvokingFactoryBean.setTargetMethod("setStrategyName");
        methodInvokingFactoryBean.setArguments(TransmittableThreadLocalSecurityContextHolderStrategy.class.getName());
        return methodInvokingFactoryBean;
    }

}
