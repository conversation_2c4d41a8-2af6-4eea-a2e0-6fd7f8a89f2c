package com.bobandata.cloud.trade.variable.core;

import cn.hutool.core.util.StrUtil;
import com.bobandata.cloud.common.enums.BaseCons;
import com.bobandata.cloud.common.utils.StrUtils;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2023-11-16日 17:07
 * @description
 */
public class SystemVariableUtil {

    private static final ConcurrentMap<String, String> SYSTEM_VARIABLES = new ConcurrentHashMap<>();

    public static void putAll(Map<String, String> messages) {
        SystemVariableUtil.SYSTEM_VARIABLES.putAll(messages);
    }

    public static void put(String variable, String message) {
        SystemVariableUtil.SYSTEM_VARIABLES.put(variable, message);
    }

    public static String getVarStrValue(String variableKey) {
        String value = SystemVariableUtil.SYSTEM_VARIABLES.get(variableKey);
        Objects.requireNonNull(variableKey, "变量：" + variableKey + "值为空");
        return value;
    }

    public static boolean getVarBooleanValue(String variableKey) {
        String varStrValue = getVarStrValue(variableKey);
        return StrUtils.toBoolean(varStrValue);
    }

    public static Integer getVarIntValue(String variableKey) {
        String varStrValue = getVarStrValue(variableKey);
        return StrUtils.toInt(varStrValue);
    }

    public static String[] getVarArrayValue(String variableKey) {
        String varStrValue = getVarStrValue(variableKey);
        return StrUtil.splitToArray(varStrValue, BaseCons.SEPARATOR_COMMA);
    }

    public static Long getVarLongValue(String variableKey) {
        String varStrValue = getVarStrValue(variableKey);
        return StrUtils.toLong(varStrValue);
    }
}
