package com.bobandata.cloud.trade.variable.core.generator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import com.bobandata.cloud.trade.system.api.variable.SystemVariableApi;
import com.bobandata.cloud.trade.system.api.variable.VariableAutoGenerateReqDTO;
import com.bobandata.cloud.trade.variable.core.SysConfigVar;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;

/**
 * ErrorCodeAutoGenerator 的实现类 目的是，扫描指定的 {@link #constantsClassList} 类，写入到 system 服务中
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
public class SysVariableAutoGeneratorImpl implements SysVariableAutoGenerator {

    /**
     * 应用分组
     */
    private final String applicationName;
    /**
     * 错误码枚举类
     */
    private final List<String> constantsClassList;
    /**
     * 错误码 Api
     */
    private final SystemVariableApi systemVariableApi;

    @Override
    @EventListener(ApplicationReadyEvent.class)
    @Async // 异步，保证项目的启动过程，毕竟非关键流程
    public void execute() {
        // 第一步，解析
        List<VariableAutoGenerateReqDTO> autoGenerateDTOs = parseSysVar();
        log.info("[execute][解析到变量数量为 ({}) 个]", autoGenerateDTOs.size());
        if (autoGenerateDTOs.isEmpty()) {
            return;
        }
        // 第二步，写入到 system 服务
        systemVariableApi.autoGenerateSysVarList(autoGenerateDTOs).checkError();
        log.info("[execute][写入到 system 组件完成]");
    }

    /**
     * 解析 constantsClassList 变量
     */
    private List<VariableAutoGenerateReqDTO> parseSysVar() {
        // 校验 errorCodeConstantsClass 参数
        if (CollUtil.isEmpty(constantsClassList)) {
            log.info("[execute][未配置 constants-class-list 配置项，不进行自动写入到 system 服务中]");
            return new ArrayList<>();
        }

        // 解析错误码
        List<VariableAutoGenerateReqDTO> autoGenerateDTOs = new ArrayList<>();
        constantsClassList.forEach(constantsClass -> {
            try {
                // 解析错误码枚举类
                Class<?> errorCodeConstantsClazz = ClassUtil.loadClass(constantsClass);
                // 解析错误码
                autoGenerateDTOs.addAll(parseSysVar(errorCodeConstantsClazz));
            } catch (Exception ex) {
                log.warn("[parseSysVar][constantsClass({}) 加载失败({})]", constantsClass,
                         ExceptionUtil.getRootCauseMessage(ex)
                );
            }
        });
        return autoGenerateDTOs;
    }

    /**
     * 解析
     */
    private List<VariableAutoGenerateReqDTO> parseSysVar(Class<?> constantsClass) {
        List<VariableAutoGenerateReqDTO> autoGenerateDTOs = new ArrayList<>();
        Arrays.stream(constantsClass.getFields())
              .forEach(field -> {
                  if (field.getType() != SysConfigVar.class) {
                      return;
                  }
                  SysConfigVar sysConfigVar = (SysConfigVar) ReflectUtil.getFieldValue(constantsClass, field);
                  autoGenerateDTOs.add(new VariableAutoGenerateReqDTO()
                                               .setVariableKey(sysConfigVar.getVariableKey())
                                               .setVariableValue(sysConfigVar.getVariableValue())
                  );
              });
        return autoGenerateDTOs;
    }

}

