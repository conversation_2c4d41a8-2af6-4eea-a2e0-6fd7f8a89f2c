package com.bobandata.cloud.trade.variable.core.loader;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.common.utils.date.DateUtils;
import com.bobandata.cloud.trade.system.api.variable.SystemVariableApi;
import com.bobandata.cloud.trade.system.api.variable.SystemVariableRespDTO;
import java.sql.Timestamp;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * <AUTHOR>
 * @date 2023-11-16日 17:25
 * @description
 */
@Slf4j
public class SystemVariableLoaderImpl implements SystemVariableLoader {

    /**
     * 刷新的频率，单位：毫秒
     */
    private static final int REFRESH_ERROR_CODE_PERIOD = 60 * 1000;

    /**
     * 应用模块
     */
    private final String applicationName;
    /**
     * Api
     */
    private final SystemVariableApi systemVariableApi;

    /**
     * 缓存最大更新时间，用于后续的增量轮询，判断是否有更新
     */
    private Timestamp maxUpdateTime;

    public SystemVariableLoaderImpl(final String applicationName, final SystemVariableApi systemVariableApi) {
        this.applicationName = applicationName;
        this.systemVariableApi = systemVariableApi;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void loadSystemVariables() {
        this.loadSystemVariable();
    }

    @Scheduled(fixedDelay = REFRESH_ERROR_CODE_PERIOD, initialDelay = REFRESH_ERROR_CODE_PERIOD)
    public void refreshSystemVariables() {
        this.loadSystemVariable();
    }

    private void loadSystemVariable() {
        // 加载
        List<SystemVariableRespDTO> systemVariableRespDTOS = systemVariableApi.getSysVarList(maxUpdateTime)
                                                                              .checkedData();
        if (CollUtil.isEmpty(systemVariableRespDTOS)) {
            return;
        }
        log.info("[loadSystemVariable][加载到 ({}) 个系统变量]", systemVariableRespDTOS.size());

        // 刷新
        systemVariableRespDTOS.forEach(varRespDTO -> {
            // 写入到缓存
            putSystemVariable(varRespDTO.getVar(), varRespDTO.getValue());
            // 记录下更新时间，方便增量更新
            maxUpdateTime = DateUtils.max(maxUpdateTime, varRespDTO.getLastUpdateTime());
        });
    }

}
