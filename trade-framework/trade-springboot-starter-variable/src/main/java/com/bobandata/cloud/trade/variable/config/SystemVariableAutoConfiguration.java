package com.bobandata.cloud.trade.variable.config;

import com.bobandata.cloud.trade.system.api.variable.SystemVariableApi;
import com.bobandata.cloud.trade.variable.core.loader.SystemVariableLoader;
import com.bobandata.cloud.trade.variable.core.loader.SystemVariableLoaderImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2023-11-10日 15:22
 * @description
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "trade.system-var", value = "enable", matchIfMissing = true)
@EnableConfigurationProperties(SystemVariableProperties.class)
@EnableScheduling
public class SystemVariableAutoConfiguration {

    @Bean
    public SystemVariableLoader systemCodeLoader(@Value("${spring.application.name}") String applicationName,
                                                 SystemVariableApi systemVariableApi) {
        return new SystemVariableLoaderImpl(applicationName, systemVariableApi);
    }
}
