package com.bobandata.cloud.trade.variable.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2023-11-10日 15:22
 * @description
 */
@ConfigurationProperties("trade.system-var")
@Data
public class SystemVariableProperties {

    /**
     * 是否开启
     */
    private Boolean enable = true;

    private List<String> constantsClassList;
}
