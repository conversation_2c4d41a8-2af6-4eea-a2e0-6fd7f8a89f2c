package com.bobandata.cloud.trade.data.permission.core.rule;

import com.bobandata.cloud.common.utils.servlet.HttpServletParamUtils;
import java.util.Map;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.LikeExpression;
import net.sf.jsqlparser.schema.Column;

/**
 * <AUTHOR>
 * @date 2024-01-10日 16:49
 * @description 基于表 global_org_info - org_no 数据权限规则
 */
public class OrgDataPermissionRule implements DataPermissionRule {

    public static final String ORG_COLUMN_NAME = "orgNo";

    public static final String REQUEST_ORG_NO_KEY = "orgNo";

    private static final String LIKE_MASK = "%";

    private final String orgColumnName;

    private final String requestOrgNoKey;

    public OrgDataPermissionRule(String orgColumnName, String requestOrgNoKey) {
        this.orgColumnName = orgColumnName;
        this.requestOrgNoKey = requestOrgNoKey;
    }

    @Override
    public Boolean supportCurrentTable(final String tableName) {
        return true;
    }

    @Override
    public Expression getExpression(final String tableName, final Alias tableAlias) {
        String mainTableName = tableAlias == null ? tableName : tableAlias.getName();
        Map<String, String> requestParams = HttpServletParamUtils.getAllParam();
        String currentOrgNo = requestParams.get(requestOrgNoKey);
        return this.rebuildWhereExpression(mainTableName, currentOrgNo);
    }

    private Expression rebuildWhereExpression(String mainTableName, String currentOrgNo) {
        Column orgColumn = new Column(mainTableName + "." + orgColumnName);
        LikeExpression likeExpression = new LikeExpression();
        likeExpression.setLeftExpression(orgColumn);
        StringValue stringValue = new StringValue();
        stringValue.setValue(currentOrgNo + LIKE_MASK);
        likeExpression.setRightExpression(stringValue);
        return likeExpression;
    }
}
