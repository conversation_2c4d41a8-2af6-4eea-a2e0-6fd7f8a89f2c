package com.bobandata.cloud.trade.data.permission.core.rule;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-10日 14:45
 * @description 数据权限规则工厂类
 */
public interface DataPermissionRuleFactory {

    /**
     * 获得所有数据权限规则数组
     *
     * @return 数据权限规则数组
     */
    List<DataPermissionRule> getDataPermissionRules();

    /**
     * 获得指定 Mapper 的数据权限规则数组
     *
     * @param mappedStatementId 指定 Mapper 的编号
     * @return 数据权限规则数组
     */
    List<DataPermissionRule> getDataPermissionRule(String mappedStatementId);
}
