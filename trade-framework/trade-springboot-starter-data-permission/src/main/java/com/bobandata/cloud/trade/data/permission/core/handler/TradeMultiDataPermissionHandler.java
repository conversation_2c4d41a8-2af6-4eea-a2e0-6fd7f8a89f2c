package com.bobandata.cloud.trade.data.permission.core.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.MultiDataPermissionHandler;
import org.apache.ibatis.mapping.MappedStatement;

/**
 * <AUTHOR>
 * @date 2024-01-10日 14:57
 * @description 实现最新的数据权限处理器
 */
public interface TradeMultiDataPermissionHandler extends MultiDataPermissionHandler {

    /**
     * 当前 ms 是否需要 重写SQL条件
     */
    boolean rewriteTable(MappedStatement ms);
}
