package com.bobandata.cloud.trade.data.permission.core.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.bobandata.cloud.trade.data.permission.core.rule.DataPermissionRule;
import com.bobandata.cloud.trade.data.permission.core.rule.DataPermissionRuleFactory;
import com.bobandata.cloud.trade.mybatisplus.util.MyBatisUtils;
import java.util.List;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.schema.Table;
import org.apache.ibatis.mapping.MappedStatement;

/**
 * <AUTHOR>
 * @date 2024-01-10日 16:16
 * @description
 */
public class RuleTradeMultiDataPermissionHandler implements TradeMultiDataPermissionHandler {

    private final String[] tableNames;

    private final DataPermissionRuleFactory dataPermissionRuleFactory;

    public RuleTradeMultiDataPermissionHandler(String[] tableNames,
                                               DataPermissionRuleFactory dataPermissionRuleFactory) {
        this.tableNames = tableNames;
        this.dataPermissionRuleFactory = dataPermissionRuleFactory;
    }

    @Override
    public boolean rewriteTable(final MappedStatement ms) {
        List<DataPermissionRule> dataPermissionRules = dataPermissionRuleFactory.getDataPermissionRule(ms.getId());
        return CollUtil.isEmpty(dataPermissionRules);
    }

    @Override
    public Expression getSqlSegment(final Table table, final Expression where, final String mappedStatementId) {
        String tableName = MyBatisUtils.getTableName(table);
        if (ignoreTable(tableName)) {
            return null;
        }
        List<DataPermissionRule> dataPermissionRules = dataPermissionRuleFactory.getDataPermissionRules();
        Expression allExpression = null;
        for (final DataPermissionRule dataPermissionRule : dataPermissionRules) {
            if (!dataPermissionRule.supportCurrentTable(tableName)) {
                continue;
            }
            Expression oneExpress = dataPermissionRule.getExpression(tableName, table.getAlias());
            allExpression = allExpression == null ? oneExpress
                    : new AndExpression(allExpression, oneExpress);
        }
        return allExpression;
    }

    private boolean ignoreTable(String tableName) {
        return ArrayUtil.contains(tableNames, tableName);
    }
}
