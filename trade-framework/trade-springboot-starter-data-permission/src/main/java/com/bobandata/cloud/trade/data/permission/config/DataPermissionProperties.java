package com.bobandata.cloud.trade.data.permission.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2024-01-10日 15:03
 * @description
 */
@ConfigurationProperties("trade.data-permission")
@Data
@Validated
public class DataPermissionProperties {

    /**
     * 数据权限是否开启
     */
    private Boolean globalEnable = true;

    /**
     * 全局忽略数据权限表名称
     */
    private String[] ignoreTables;

    /**
     * org 规则配置
     */
    private OrgRule orgRule;

    @Data
    public static class OrgRule {

        private String orgNoKey;

        private String orgColumnName;
    }
}
