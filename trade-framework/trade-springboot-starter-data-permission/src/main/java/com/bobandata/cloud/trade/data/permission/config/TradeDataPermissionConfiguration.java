package com.bobandata.cloud.trade.data.permission.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.bobandata.cloud.trade.data.permission.core.aop.DataPermissionAnnotationAdvisor;
import com.bobandata.cloud.trade.data.permission.core.handler.RuleTradeMultiDataPermissionHandler;
import com.bobandata.cloud.trade.data.permission.core.handler.TradeDataPermissionInterceptor;
import com.bobandata.cloud.trade.data.permission.core.rule.DataPermissionRule;
import com.bobandata.cloud.trade.data.permission.core.rule.DataPermissionRuleFactory;
import com.bobandata.cloud.trade.data.permission.core.rule.DataPermissionRuleFactoryImpl;
import com.bobandata.cloud.trade.data.permission.core.rule.OrgDataPermissionRule;
import com.bobandata.cloud.trade.mybatisplus.util.MyBatisUtils;
import java.util.List;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2024-01-10日 14:38
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(DataPermissionProperties.class)
@ConditionalOnProperty(prefix = "trade.data-permission", value = "globalEnable", matchIfMissing = true)
public class TradeDataPermissionConfiguration {

    private final DataPermissionProperties dataPermissionProperties;

    public TradeDataPermissionConfiguration(DataPermissionProperties dataPermissionProperties) {
        this.dataPermissionProperties = dataPermissionProperties;
    }

    @Bean
    @ConditionalOnProperty(prefix = "trade.data-permission", value = "globalEnable", matchIfMissing = true)
    public OrgDataPermissionRule dataPermissionRule() {
        DataPermissionProperties.OrgRule orgRule = dataPermissionProperties.getOrgRule();
        if (orgRule == null) {
            return new OrgDataPermissionRule(
                    OrgDataPermissionRule.ORG_COLUMN_NAME, OrgDataPermissionRule.REQUEST_ORG_NO_KEY);
        }
        return new OrgDataPermissionRule(orgRule.getOrgNoKey(), orgRule.getOrgNoKey());
    }

    @Bean
    public DataPermissionRuleFactory dataPermissionRuleFactory(List<DataPermissionRule> rules) {
        return new DataPermissionRuleFactoryImpl(rules);
    }

    @Bean
    public TradeDataPermissionInterceptor dataPermissionDatabaseInterceptor(MybatisPlusInterceptor interceptor,
                                                                            DataPermissionRuleFactory ruleFactory) {
        String[] ignoreTables = dataPermissionProperties.getIgnoreTables();
        RuleTradeMultiDataPermissionHandler dataPermissionHandler = new RuleTradeMultiDataPermissionHandler(
                ignoreTables, ruleFactory);
        // 创建 DataPermissionDatabaseInterceptor 拦截器
        TradeDataPermissionInterceptor inner = new TradeDataPermissionInterceptor(dataPermissionHandler);
        if (dataPermissionProperties.getGlobalEnable()) {
            // 添加到 interceptor 中
            // 需要加在首个，主要是为了在分页插件前面。这个是 MyBatis Plus 的规定
            MyBatisUtils.addInterceptor(interceptor, inner, 0);
        }
        return inner;
    }

    @Bean
    public DataPermissionAnnotationAdvisor dataPermissionAnnotationAdvisor() {
        return new DataPermissionAnnotationAdvisor();
    }
}
