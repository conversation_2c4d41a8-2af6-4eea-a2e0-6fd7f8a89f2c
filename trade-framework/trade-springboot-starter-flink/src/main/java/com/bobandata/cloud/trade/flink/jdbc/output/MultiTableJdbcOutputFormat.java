package com.bobandata.cloud.trade.flink.jdbc.output;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.flink.table.data.RowData;

/**
 * <AUTHOR>
 * @date 2024-03-28日 10:52
 * @description 适用于多表写入 数据量少1000的情况
 */
@Slf4j
public class MultiTableJdbcOutputFormat<T> extends RichOutputFormat<T> {

    private final String[] tableNames;

    private final JdbcConnectionProvider connectionProvider;

    private final RecordExtractFunction<T> extractFunction;

    private final StatementExecutorFactory executorFactory;

    private final Map<String, JdbcBatchStatementExecutor<RowData>> executorCache;

    public MultiTableJdbcOutputFormat(String[] tableNames,
                                      JdbcConnectionProvider connectionProvider,
                                      RecordExtractFunction<T> extractFunction,
                                      StatementExecutorFactory executorFactory) {
        this.tableNames = tableNames;
        this.connectionProvider = connectionProvider;
        this.extractFunction = extractFunction;
        this.executorFactory = executorFactory;
        this.executorCache = new HashMap<>();
    }

    @Override
    public void configure(final Configuration parameters) {
        //do nothing
    }

    @Override
    public void open(final int taskNumber, final int numTasks) throws IOException {
        try {
            connectionProvider.getOrEstablishConnection();
        } catch (Exception e) {
            throw new IOException("unable to open JDBC writer", e);
        }
    }

    private JdbcBatchStatementExecutor<RowData> getJdbcBatchStatementExecutor(String tableName) {
        JdbcBatchStatementExecutor<RowData> statementExecutor = executorCache.get(tableName);
        if (statementExecutor == null) {
            statementExecutor = executorFactory.getStatementExecutor(tableName);
            try {
                statementExecutor.prepareStatements(connectionProvider.getConnection());
                executorCache.put(tableName, statementExecutor);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
        return statementExecutor;
    }

    @Override
    public void writeRecord(final T record) throws IOException {
        for (final String tableName : tableNames) {
            JdbcBatchStatementExecutor<RowData> statementExecutor = this.getJdbcBatchStatementExecutor(tableName);
            RowData rowData = extractFunction.extractData(tableName, record);
            if (rowData == null) {
                return;
            }
            try {
                statementExecutor.addToBatch(rowData);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
    }

    public synchronized void flush() throws SQLException {
        for (final Map.Entry<String, JdbcBatchStatementExecutor<RowData>> entry : executorCache.entrySet()) {
            JdbcBatchStatementExecutor<RowData> statementExecutor = entry.getValue();
            statementExecutor.executeBatch();
        }
    }

    @Override
    public void close() throws IOException {
        try {
            this.flush();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        for (final Map.Entry<String, JdbcBatchStatementExecutor<RowData>> entry : executorCache.entrySet()) {
            JdbcBatchStatementExecutor<RowData> statementExecutor = entry.getValue();
            try {
                statementExecutor.closeStatements();
            } catch (SQLException e) {
                log.warn(e.getMessage(), e);
            }
        }
        connectionProvider.closeConnection();
    }
}
