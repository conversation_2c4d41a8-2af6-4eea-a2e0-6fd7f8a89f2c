package com.bobandata.cloud.trade.flink.jdbc.map;

import java.io.Serializable;
import java.lang.reflect.Field;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-04-01日 09:10
 * @description 按照字段顺序序列化 反序列化
 */
public class ReflectRowDeserializeConverter<T extends Serializable> implements RowDeserializeConverter<T> {

    private final Class<T> clazz;

    public ReflectRowDeserializeConverter(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public T deserialize(final Row data) throws Exception {
        T t = clazz.newInstance();
        Field[] fields = FieldUtils.getAllFields(clazz);
        for (int i = 0; i < fields.length; i++) {
            FieldUtils.writeField(fields[i], t, data.getField(i), true);
        }
        return t;
    }

}
