package com.bobandata.cloud.trade.flink.jdbc;

import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.SQLException;
import javax.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.dialect.mysql.MySqlDialect;
import org.apache.flink.connector.jdbc.internal.JdbcOutputFormat;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.converter.MySQLRowConverter;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.flink.connector.jdbc.internal.executor.TableSimpleStatementExecutor;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.RowType;

/**
 * <AUTHOR>
 * @date 2024-03-28日 09:21
 * @description
 */
@Slf4j
public class JdbcExecutorFactory implements Serializable {

    private static final MySqlDialect MYSQL_DIALECT = new MySqlDialect();

    public void JdbcInputFormat() {

    }

    public RowType getDataType(Class<?> dataClazz) {
        RowTypeInfo rowTypeInfo = this.getRowTypeInfo(dataClazz);
        return FlinkJdbcUtil.convertToRowType(rowTypeInfo);
    }

    /**
     * 只能获取基本数据类型的映射
     */
    public RowTypeInfo getRowTypeInfo(Class<?> dataClazz) {
        Field[] declaredFields = dataClazz.getDeclaredFields();
        TypeInformation<?>[] typeInformations = new TypeInformation<?>[declaredFields.length];
        String[] fieldNames = new String[declaredFields.length];
        for (int i = 0; i < declaredFields.length; i++) {
            Class<?> type = declaredFields[i].getType();
            TypeInformation<?> typeInformation = FlinkJdbcUtil.getTypeInformation(type);
            typeInformations[i] = typeInformation;
            fieldNames[i] = declaredFields[i].getName();
        }
        return new RowTypeInfo(typeInformations, fieldNames);
    }

    public <T, JdbcExec extends JdbcBatchStatementExecutor<RowData>> JdbcOutputFormat<T, RowData, JdbcExec> createSingleTableJdbcOutputFormat(
            @Nonnull JdbcConnectionProvider connectionProvider,
            @Nonnull JdbcExecutionOptions executionOptions,
            @Nonnull JdbcOutputFormat.StatementExecutorFactory<JdbcExec> statementExecutorFactory,
            @Nonnull JdbcOutputFormat.RecordExtractor<T, RowData> recordExtractor) {
        return new JdbcOutputFormat<>(connectionProvider, executionOptions,
                                      statementExecutorFactory, recordExtractor
        );
    }

    public TableSimpleStatementExecutor createTableSimpleStatement(String tableName,
                                                                   String sql,
                                                                   String[] dataFieldNames,
                                                                   JdbcConnectionProvider connectionProvider) {
        JdbcTableMetaProvider jdbcTableMetaProvider = new JdbcTableMetaProvider();
        TableMetaInfo tableMetaInfo;
        try {
            tableMetaInfo = jdbcTableMetaProvider.loadTableMetaInfo(
                    tableName, connectionProvider.getOrEstablishConnection());
        } catch (SQLException | ClassNotFoundException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            connectionProvider.closeConnection();
        }
        LogicalType[] logicalTypes = tableMetaInfo.getByDataFields(dataFieldNames);
        return this.createTableSimpleStatement(sql, dataFieldNames, logicalTypes);
    }

    /**
     * 操作 {@link RowData} 类型的executor
     */
    public TableSimpleStatementExecutor createTableSimpleStatement(String sql,
                                                                   String[] dataFieldNames,
                                                                   LogicalType[] logicalTypes) {
        TableStatementFactoryImpl statementFactory = new TableStatementFactoryImpl(dataFieldNames, sql);
        RowType rowType = RowType.of(logicalTypes, dataFieldNames);
        MySQLRowConverter mySQLRowConverter = new MySQLRowConverter(rowType);
        return new TableSimpleStatementExecutor(statementFactory, mySQLRowConverter);
    }
}
