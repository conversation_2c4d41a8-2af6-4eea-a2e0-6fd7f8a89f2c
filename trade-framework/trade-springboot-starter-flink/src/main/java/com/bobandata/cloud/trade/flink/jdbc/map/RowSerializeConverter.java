package com.bobandata.cloud.trade.flink.jdbc.map;

import java.io.Serializable;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

public interface RowSerializeConverter<T extends Serializable> extends Serializable, MapFunction<T, Row> {

    Row serialize(T data) throws Exception;

    default Row map(T value) throws Exception {
        return serialize(value);
    }
}
