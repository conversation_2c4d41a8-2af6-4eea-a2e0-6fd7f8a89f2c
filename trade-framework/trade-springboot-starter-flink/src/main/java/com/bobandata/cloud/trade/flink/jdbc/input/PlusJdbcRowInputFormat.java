package com.bobandata.cloud.trade.flink.jdbc.input;

import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Array;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Arrays;
import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.InputFormat;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.split.JdbcParameterValuesProvider;
import org.apache.flink.core.io.GenericInputSplit;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.core.io.InputSplitAssigner;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2024-03-29日 09:11
 * @description
 */
public class PlusJdbcRowInputFormat extends RichInputFormat<Row, InputSplit> {

    protected static final long serialVersionUID = 2L;
    protected static final Logger LOG = LoggerFactory.getLogger(JdbcInputFormat.class);

    protected JdbcConnectionProvider connectionProvider;
    protected String queryTemplate;
    protected int resultSetType;
    protected int resultSetConcurrency;
    protected RowTypeInfo rowTypeInfo;

    protected transient PreparedStatement statement;
    protected transient ResultSet resultSet;
    protected int fetchSize;
    // Boolean to distinguish between default value and explicitly set autoCommit mode.
    protected Boolean autoCommit;

    protected boolean hasNext;
    protected Object[][] parameterValues;

    public PlusJdbcRowInputFormat() {
    }

    @Override
    public void configure(Configuration parameters) {
        // do nothing here
    }

    @Override
    public void openInputFormat() {
        // called once per inputFormat (on open)
        try {
            Connection dbConn = connectionProvider.getOrEstablishConnection();

            // set autoCommit mode only if it was explicitly configured.
            // keep connection default otherwise.
            if (autoCommit != null) {
                dbConn.setAutoCommit(autoCommit);
            }

            statement = dbConn.prepareStatement(queryTemplate, resultSetType, resultSetConcurrency);
            if (fetchSize == Integer.MIN_VALUE || fetchSize > 0) {
                statement.setFetchSize(fetchSize);
            }
        } catch (SQLException se) {
            throw new IllegalArgumentException("open() failed." + se.getMessage(), se);
        } catch (ClassNotFoundException cnfe) {
            throw new IllegalArgumentException(
                    "JDBC-Class not found. - " + cnfe.getMessage(), cnfe);
        }
    }

    @Override
    public void closeInputFormat() {
        // called once per inputFormat (on close)
        try {
            if (statement != null) {
                statement.close();
            }
        } catch (SQLException se) {
            LOG.info("Inputformat Statement couldn't be closed - " + se.getMessage());
        } finally {
            statement = null;
        }

        connectionProvider.closeConnection();

        parameterValues = null;
    }

    /**
     * Connects to the source database and executes the query in a <b>parallel fashion</b> if this {@link InputFormat}
     * is built using a parameterized query (i.e. using a {@link PreparedStatement}) and a proper
     * {@link JdbcParameterValuesProvider}, in a <b>non-parallel fashion</b> otherwise.
     *
     * @param inputSplit which is ignored if this InputFormat is executed as a non-parallel source, a "hook" to the
     *                   query parameters otherwise (using its <i>splitNumber</i>)
     * @throws IOException if there's an error during the execution of the query
     */
    @Override
    public void open(InputSplit inputSplit) throws IOException {
        try {
            if (inputSplit != null && parameterValues != null) {
                for (int i = 0; i < parameterValues[inputSplit.getSplitNumber()].length; i++) {
                    Object param = parameterValues[inputSplit.getSplitNumber()][i];
                    if (param instanceof String) {
                        statement.setString(i + 1, (String) param);
                    } else if (param instanceof Long) {
                        statement.setLong(i + 1, (Long) param);
                    } else if (param instanceof Integer) {
                        statement.setInt(i + 1, (Integer) param);
                    } else if (param instanceof Double) {
                        statement.setDouble(i + 1, (Double) param);
                    } else if (param instanceof Boolean) {
                        statement.setBoolean(i + 1, (Boolean) param);
                    } else if (param instanceof Float) {
                        statement.setFloat(i + 1, (Float) param);
                    } else if (param instanceof BigDecimal) {
                        statement.setBigDecimal(i + 1, (BigDecimal) param);
                    } else if (param instanceof Byte) {
                        statement.setByte(i + 1, (Byte) param);
                    } else if (param instanceof Short) {
                        statement.setShort(i + 1, (Short) param);
                    } else if (param instanceof Date) {
                        statement.setDate(i + 1, (Date) param);
                    } else if (param instanceof Time) {
                        statement.setTime(i + 1, (Time) param);
                    } else if (param instanceof Timestamp) {
                        statement.setTimestamp(i + 1, (Timestamp) param);
                    } else if (param instanceof Array) {
                        statement.setArray(i + 1, (Array) param);
                    } else {
                        // extends with other types if needed
                        throw new IllegalArgumentException(
                                "open() failed. Parameter "
                                        + i
                                        + " of type "
                                        + param.getClass()
                                        + " is not handled (yet).");
                    }
                }
                if (LOG.isDebugEnabled()) {
                    LOG.debug(
                            String.format(
                                    "Executing '%s' with parameters %s",
                                    queryTemplate,
                                    Arrays.deepToString(
                                            parameterValues[inputSplit.getSplitNumber()])
                            ));
                }
            }
            resultSet = statement.executeQuery();
            hasNext = resultSet.next();
        } catch (SQLException se) {
            throw new IllegalArgumentException("open() failed." + se.getMessage(), se);
        }
    }

    /**
     * Closes all resources used.
     *
     * @throws IOException Indicates that a resource could not be closed.
     */
    @Override
    public void close() throws IOException {
        if (resultSet == null) {
            return;
        }
        try {
            resultSet.close();
        } catch (SQLException se) {
            LOG.info("Inputformat ResultSet couldn't be closed - " + se.getMessage());
        }
    }

    /**
     * Checks whether all data has been read.
     *
     * @return boolean value indication whether all data has been read.
     * @throws IOException
     */
    @Override
    public boolean reachedEnd() throws IOException {
        return !hasNext;
    }

    /**
     * Stores the next resultSet row in a tuple.
     *
     * @param reuse row to be reused.
     * @return row containing next {@link Row}
     * @throws IOException
     */
    @Override
    public Row nextRecord(Row reuse) throws IOException {
        try {
            if (!hasNext) {
                return null;
            }
            for (int pos = 0; pos < reuse.getArity(); pos++) {
                reuse.setField(pos, this.getFieldValue(resultSet, pos + 1));
            }
            hasNext = resultSet.next();
            return reuse;
        } catch (SQLException se) {
            throw new IOException("Couldn't read data - " + se.getMessage(), se);
        } catch (NullPointerException npe) {
            throw new IOException("Couldn't access resultSet", npe);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Object getFieldValue(ResultSet resultSet, int fieldPos) throws SQLException {
        TypeInformation<?> typeInformation = rowTypeInfo.getTypeAt(fieldPos);
        Integer jdbcType = FlinkJdbcUtil.getJdbcType(typeInformation);
        return this.getFieldValue(resultSet, jdbcType, fieldPos);
    }

    public Object getFieldValue(ResultSet upload, int type, int index) throws SQLException {
        try {
            // casting values as suggested by
            // http://docs.oracle.com/javase/1.5.0/docs/guide/jdbc/getstart/mapping.html
            switch (type) {
                case java.sql.Types.NULL:
                    return null;
                case java.sql.Types.BOOLEAN:
                case java.sql.Types.BIT:
                    return upload.getBoolean(index + 1);
                case java.sql.Types.CHAR:
                case java.sql.Types.NCHAR:
                case java.sql.Types.VARCHAR:
                case java.sql.Types.LONGVARCHAR:
                case java.sql.Types.LONGNVARCHAR:
                    return upload.getString(index + 1);
                case java.sql.Types.TINYINT:
                    return upload.getByte(index + 1);
                case java.sql.Types.SMALLINT:
                    return upload.getShort(index + 1);
                case java.sql.Types.INTEGER:
                    return upload.getInt(index + 1);
                case java.sql.Types.BIGINT:
                    return upload.getLong(index + 1);
                case java.sql.Types.REAL:
                    return upload.getFloat(index + 1);
                case java.sql.Types.FLOAT:
                case java.sql.Types.DOUBLE:
                    return upload.getDouble(index + 1);
                case java.sql.Types.DECIMAL:
                case java.sql.Types.NUMERIC:
                    return upload.getBigDecimal(index + 1);
                case java.sql.Types.DATE:
                    return upload.getDate(index + 1);
                case java.sql.Types.TIME:
                    return upload.getTime(index + 1);
                case java.sql.Types.TIMESTAMP:
                    return upload.getTimestamp(index + 1);
                case java.sql.Types.BINARY:
                case java.sql.Types.VARBINARY:
                case java.sql.Types.LONGVARBINARY:
                    return upload.getBytes(index + 1);
                default:
                    LOG.warn(
                            "Unmanaged sql type ({}) for column {}.",
                            type,
                            index + 1
                    );
                    return upload.getObject(index + 1);
                // case java.sql.Types.SQLXML
                // case java.sql.Types.ARRAY:
                // case java.sql.Types.JAVA_OBJECT:
                // case java.sql.Types.BLOB:
                // case java.sql.Types.CLOB:
                // case java.sql.Types.NCLOB:
                // case java.sql.Types.DATALINK:
                // case java.sql.Types.DISTINCT:
                // case java.sql.Types.OTHER:
                // case java.sql.Types.REF:
                // case java.sql.Types.ROWID:
                // case java.sql.Types.STRUC
            }
        } catch (ClassCastException e) {
            // enrich the exception with detailed information.
            String errorMessage =
                    String.format(
                            "%s, field index: %s",
                            e.getMessage(), index
                    );
            ClassCastException enrichedException = new ClassCastException(errorMessage);
            enrichedException.setStackTrace(e.getStackTrace());
            throw enrichedException;
        }
    }

    @Override
    public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
        return cachedStatistics;
    }

    @Override
    public InputSplit[] createInputSplits(int minNumSplits) throws IOException {
        if (parameterValues == null) {
            return new GenericInputSplit[] {new GenericInputSplit(0, 1)};
        }
        GenericInputSplit[] ret = new GenericInputSplit[parameterValues.length];
        for (int i = 0; i < ret.length; i++) {
            ret[i] = new GenericInputSplit(i, ret.length);
        }
        return ret;
    }

    @Override
    public InputSplitAssigner getInputSplitAssigner(InputSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }

    @VisibleForTesting
    protected PreparedStatement getStatement() {
        return statement;
    }

    @VisibleForTesting
    protected Connection getDbConn() {
        return connectionProvider.getConnection();
    }
}
