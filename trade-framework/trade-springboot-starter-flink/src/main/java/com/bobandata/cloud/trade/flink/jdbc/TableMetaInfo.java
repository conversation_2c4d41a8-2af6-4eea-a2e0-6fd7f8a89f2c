package com.bobandata.cloud.trade.flink.jdbc;

import cn.hutool.core.util.ArrayUtil;
import com.bobandata.cloud.trade.flink.core.util.FlinkJdbcUtil;
import java.io.Serializable;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.ToString;
import org.apache.flink.connector.jdbc.dialect.JdbcDialect;
import org.apache.flink.table.types.logical.LogicalType;

/**
 * <AUTHOR>
 * @date 2024-03-21日 14:23
 * @description
 */
@Getter
@ToString
public class TableMetaInfo implements Serializable {

    private final String tableName;

    private final String[] fieldNames;

    private final int[] fieldTypes;

    private final LogicalType[] logicalTypes;

    public TableMetaInfo(String tableName, String[] fieldNames, int[] fieldTypes, LogicalType[] logicalTypes) {
        this.tableName = tableName;
        this.fieldNames = fieldNames;
        this.fieldTypes = fieldTypes;
        this.logicalTypes = logicalTypes;
    }

    public LogicalType[] getByDataFields(String[] dataFields) {
        LogicalType[] colTypes = new LogicalType[dataFields.length];
        for (int i = 0; i < dataFields.length; i++) {
            int index = this.indexField(dataFields[i]);
            colTypes[i] = logicalTypes[index];
        }
        return colTypes;
    }

    private int indexField(String fieldName) {
        int index = ArrayUtil.indexOfIgnoreCase(fieldNames, fieldName);
        if (index == -1) {
            throw new IllegalArgumentException("表" + tableName + "中没有此字段" + fieldName);
        }
        return index;
    }

    public String getExecuteInsertSQL(JdbcDialect jdbcDialect, String[] insertCols) {
        return FlinkJdbcUtil.getInsertSql(getTableName(), jdbcDialect, insertCols);
    }

    /**
     * @param colNames 表所属字段名
     * @return 字段对应的SQL——TYPE {@link Types}
     */
    public int[] indexFieldsType(String[] colNames) {
        int[] colTypes = new int[colNames.length];
        for (int i = 0; i < colNames.length; i++) {
            int index = this.indexField(colNames[i]);
            colTypes[i] = fieldTypes[index];
        }
        return colTypes;
    }

    public static class Builder {

        private String tableName;

        private final List<String> fieldNames = new ArrayList<>();

        private final List<Integer> fieldTypes = new ArrayList<>();

        private final List<LogicalType> logicalTypes = new ArrayList<>();

        public Builder setTableName(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public void addField(String fieldName, Integer fieldType, LogicalType logicalType) {
            this.fieldNames.add(fieldName);
            this.fieldTypes.add(fieldType);
            this.logicalTypes.add(logicalType);
        }

        public TableMetaInfo build() {
            String[] fieldNamesArray = this.fieldNames.toArray(new String[0]);
            int[] fieldTypesArray = this.fieldTypes.stream().mapToInt(Integer::intValue).toArray();
            LogicalType[] logicalTypesArray = this.logicalTypes.toArray(new LogicalType[0]);
            return new TableMetaInfo(tableName, fieldNamesArray, fieldTypesArray, logicalTypesArray);
        }
    }
}
