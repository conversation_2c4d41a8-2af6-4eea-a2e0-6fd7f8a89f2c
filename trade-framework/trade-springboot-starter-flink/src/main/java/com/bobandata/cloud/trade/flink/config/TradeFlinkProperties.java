package com.bobandata.cloud.trade.flink.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024-03-29日 10:10
 * @description
 */
@ConfigurationProperties("trade.flink")
@Data
public class TradeFlinkProperties {

    private List<DataSourceConfig> dataSource;

    @Data
    public static class DataSourceConfig {

        private Integer id;

        private String driverClassName;

        private String url;

        private String username;

        private String password;
    }
}
