package com.bobandata.cloud.trade.flink.jdbc.map;

import java.io.Serializable;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;

public interface RowDeserializeConverter<T extends Serializable> extends Serializable, MapFunction<Row, T> {

    T deserialize(Row data) throws Exception;

    default T map(Row value) throws Exception {
        return deserialize(value);
    }
}
