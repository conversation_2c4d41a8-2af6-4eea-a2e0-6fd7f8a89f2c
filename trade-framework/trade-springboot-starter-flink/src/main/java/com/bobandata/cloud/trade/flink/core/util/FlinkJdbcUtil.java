package com.bobandata.cloud.trade.flink.core.util;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.flink.api.common.typeinfo.LocalTimeTypeInfo;
import org.apache.flink.api.common.typeinfo.SqlTimeTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.connector.jdbc.dialect.JdbcDialect;
import org.apache.flink.shaded.guava30.com.google.common.collect.ImmutableMap;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.logical.LogicalType;
import org.apache.flink.table.types.logical.LogicalTypeRoot;
import org.apache.flink.table.types.logical.RowType;

import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.BIG_DEC_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.BOOLEAN_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.BYTE_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.DOUBLE_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.FLOAT_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.INT_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.LONG_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.SHORT_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.BasicTypeInfo.STRING_TYPE_INFO;
import static org.apache.flink.api.common.typeinfo.PrimitiveArrayTypeInfo.BYTE_PRIMITIVE_ARRAY_TYPE_INFO;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.ARRAY;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.BIGINT;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.BINARY;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.BOOLEAN;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.CHAR;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.DATE;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.DECIMAL;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.DOUBLE;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.FLOAT;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.INTEGER;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.SMALLINT;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.TIMESTAMP_WITHOUT_TIME_ZONE;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.TIMESTAMP_WITH_TIME_ZONE;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.TIME_WITHOUT_TIME_ZONE;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.TINYINT;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.VARBINARY;
import static org.apache.flink.table.types.logical.LogicalTypeRoot.VARCHAR;

public class FlinkJdbcUtil {

    private static final Map<TypeInformation<?>, Integer> TYPE_MAPPING;

    static {
        HashMap<TypeInformation<?>, Integer> m = new HashMap<>();
        m.put(STRING_TYPE_INFO, Types.VARCHAR);
        m.put(BOOLEAN_TYPE_INFO, Types.BOOLEAN);
        m.put(BYTE_TYPE_INFO, Types.TINYINT);
        m.put(SHORT_TYPE_INFO, Types.SMALLINT);
        m.put(INT_TYPE_INFO, Types.INTEGER);
        m.put(LONG_TYPE_INFO, Types.BIGINT);
        m.put(FLOAT_TYPE_INFO, Types.REAL);
        m.put(DOUBLE_TYPE_INFO, Types.DOUBLE);
        m.put(SqlTimeTypeInfo.DATE, Types.DATE);
        m.put(SqlTimeTypeInfo.TIME, Types.TIME);
        m.put(SqlTimeTypeInfo.TIMESTAMP, Types.TIMESTAMP);
        m.put(LocalTimeTypeInfo.LOCAL_DATE, Types.DATE);
        m.put(LocalTimeTypeInfo.LOCAL_TIME, Types.TIME);
        m.put(LocalTimeTypeInfo.LOCAL_DATE_TIME, Types.TIMESTAMP);
        m.put(BIG_DEC_TYPE_INFO, Types.DECIMAL);
        m.put(BYTE_PRIMITIVE_ARRAY_TYPE_INFO, Types.BINARY);
        TYPE_MAPPING = Collections.unmodifiableMap(m);
    }

    private static final Map<LogicalTypeRoot, Integer> LOGICAL_TYPE_MAPPING =
            ImmutableMap.<LogicalTypeRoot, Integer>builder()
                        .put(VARCHAR, Types.VARCHAR)
                        .put(CHAR, Types.CHAR)
                        .put(VARBINARY, Types.VARBINARY)
                        .put(BOOLEAN, Types.BOOLEAN)
                        .put(BINARY, Types.BINARY)
                        .put(TINYINT, Types.TINYINT)
                        .put(SMALLINT, Types.SMALLINT)
                        .put(INTEGER, Types.INTEGER)
                        .put(BIGINT, Types.BIGINT)
                        .put(FLOAT, Types.REAL)
                        .put(DOUBLE, Types.DOUBLE)
                        .put(DATE, Types.DATE)
                        .put(TIMESTAMP_WITHOUT_TIME_ZONE, Types.TIMESTAMP)
                        .put(TIMESTAMP_WITH_TIME_ZONE, Types.TIMESTAMP_WITH_TIMEZONE)
                        .put(TIME_WITHOUT_TIME_ZONE, Types.TIME)
                        .put(DECIMAL, Types.DECIMAL)
                        .put(ARRAY, Types.ARRAY)
                        .build();

    private static final Map<Class<?>, TypeInformation<?>> CLASS_TYPE_MAPPING;

    static {
        Map<Class<?>, TypeInformation<?>> classTypeMap = new HashMap<Class<?>, TypeInformation<?>>();
        classTypeMap.put(boolean.class, org.apache.flink.api.common.typeinfo.Types.BOOLEAN);
        classTypeMap.put(Boolean.class, org.apache.flink.api.common.typeinfo.Types.BOOLEAN);
        classTypeMap.put(byte.class, org.apache.flink.api.common.typeinfo.Types.BYTE);
        classTypeMap.put(Byte.class, org.apache.flink.api.common.typeinfo.Types.BYTE);
        classTypeMap.put(int.class, org.apache.flink.api.common.typeinfo.Types.INT);
        classTypeMap.put(Integer.class, org.apache.flink.api.common.typeinfo.Types.INT);
        classTypeMap.put(double.class, org.apache.flink.api.common.typeinfo.Types.DOUBLE);
        classTypeMap.put(Double.class, org.apache.flink.api.common.typeinfo.Types.DOUBLE);
        classTypeMap.put(long.class, org.apache.flink.api.common.typeinfo.Types.LONG);
        classTypeMap.put(Long.class, org.apache.flink.api.common.typeinfo.Types.LONG);
        classTypeMap.put(float.class, org.apache.flink.api.common.typeinfo.Types.FLOAT);
        classTypeMap.put(Float.class, org.apache.flink.api.common.typeinfo.Types.FLOAT);
        classTypeMap.put(short.class, org.apache.flink.api.common.typeinfo.Types.SHORT);
        classTypeMap.put(Short.class, org.apache.flink.api.common.typeinfo.Types.SHORT);
        classTypeMap.put(String.class, org.apache.flink.api.common.typeinfo.Types.STRING);
        classTypeMap.put(Date.class, org.apache.flink.api.common.typeinfo.Types.SQL_DATE);
        classTypeMap.put(java.util.Date.class, org.apache.flink.api.common.typeinfo.Types.SQL_DATE);
        classTypeMap.put(Time.class, org.apache.flink.api.common.typeinfo.Types.SQL_TIME);
        classTypeMap.put(Timestamp.class, org.apache.flink.api.common.typeinfo.Types.SQL_TIMESTAMP);
        classTypeMap.put(BigDecimal.class, org.apache.flink.api.common.typeinfo.Types.BIG_DEC);
        classTypeMap.put(BigInteger.class, org.apache.flink.api.common.typeinfo.Types.BIG_INT);
        classTypeMap.put(char.class, org.apache.flink.api.common.typeinfo.Types.CHAR);
        classTypeMap.put(Character.class, org.apache.flink.api.common.typeinfo.Types.CHAR);
        classTypeMap.put(LocalDate.class, org.apache.flink.api.common.typeinfo.Types.LOCAL_DATE);
        classTypeMap.put(LocalDateTime.class, org.apache.flink.api.common.typeinfo.Types.LOCAL_DATE_TIME);
        classTypeMap.put(LocalTime.class, org.apache.flink.api.common.typeinfo.Types.LOCAL_TIME);
        CLASS_TYPE_MAPPING = Collections.unmodifiableMap(classTypeMap);
    }

    /**
     * 基本数据类型转换
     */
    public static TypeInformation<?> getTypeInformation(Class<?> clazz) {
        TypeInformation<?> information = CLASS_TYPE_MAPPING.get(clazz);
        if (information == null) {
            throw new UnsupportedOperationException("Unsupported clazzType:" + clazz.getName());
        }
        return information;
    }

    public static LogicalType getLogicalType(Class<?> clazz) {
        TypeInformation<?> information = CLASS_TYPE_MAPPING.get(clazz);
        if (information == null) {
            throw new UnsupportedOperationException("Unsupported clazzType:" + clazz.getName());
        }
        Integer jdbcType = TYPE_MAPPING.get(information);
        if (jdbcType == null) {
            throw new UnsupportedOperationException("Unsupported TypeInformation:" + jdbcType);
        }
        return getLogicalType(jdbcType);
    }

    public static RowType convertToRowType(RowTypeInfo rowTypeInfo) {
        TypeInformation<?>[] fieldTypes = rowTypeInfo.getFieldTypes();
        LogicalType[] logicalTypes = new LogicalType[fieldTypes.length];
        for (int i = 0; i < fieldTypes.length; i++) {
            Integer jdbcType = getJdbcType(fieldTypes[i]);
            logicalTypes[i] = getLogicalType(jdbcType);
        }
        return RowType.of(logicalTypes, rowTypeInfo.getFieldNames());
    }

    public static Integer getJdbcType(TypeInformation<?> typeInformation) {
        Integer jdbcType = TYPE_MAPPING.get(typeInformation);
        if (jdbcType == null) {
            throw new UnsupportedOperationException("Unsupported TypeInformation:" + typeInformation);
        }
        return jdbcType;
    }

    private static final Map<Integer, DataType> JDBC_TYPE_MAPPING;

    static {
        Map<Integer, DataType> conversionMap = new HashMap<Integer, DataType>();
        conversionMap.put(Types.CHAR, DataTypes.STRING().nullable().bridgedTo(String.class));
        conversionMap.put(Types.NCHAR, DataTypes.STRING().nullable().bridgedTo(String.class));
        conversionMap.put(Types.VARCHAR, DataTypes.STRING().nullable().bridgedTo(String.class));
        conversionMap.put(Types.LONGVARCHAR, DataTypes.STRING().nullable().bridgedTo(String.class));
        conversionMap.put(Types.LONGNVARCHAR, DataTypes.STRING().nullable().bridgedTo(String.class));
        conversionMap.put(Types.BINARY, DataTypes.BYTES().nullable().bridgedTo(byte[].class));
        conversionMap.put(Types.VARBINARY, DataTypes.BYTES().nullable().bridgedTo(byte[].class));
        conversionMap.put(Types.LONGVARBINARY, DataTypes.BYTES().nullable().bridgedTo(byte[].class));
        conversionMap.put(Types.BOOLEAN, DataTypes.BOOLEAN().nullable().bridgedTo(Boolean.class));
        conversionMap.put(Types.BIT, DataTypes.BOOLEAN().nullable().bridgedTo(Boolean.class));
        conversionMap.put(Types.TINYINT, DataTypes.TINYINT().notNull().bridgedTo(Byte.class));
        conversionMap.put(Types.SMALLINT, DataTypes.SMALLINT().notNull().bridgedTo(Short.class));
        conversionMap.put(Types.INTEGER, DataTypes.INT().notNull().bridgedTo(Integer.class));
        conversionMap.put(Types.BIGINT, DataTypes.BIGINT().notNull().bridgedTo(Long.class));
        conversionMap.put(Types.REAL, DataTypes.FLOAT().notNull().bridgedTo(Float.class));
        conversionMap.put(Types.FLOAT, DataTypes.FLOAT().notNull().bridgedTo(Float.class));
        conversionMap.put(Types.DOUBLE, DataTypes.DOUBLE().notNull().bridgedTo(Double.class));
        conversionMap.put(Types.NUMERIC, DataTypes.DECIMAL(18, 6).nullable().bridgedTo(BigDecimal.class));
        conversionMap.put(Types.DECIMAL, DataTypes.DECIMAL(18, 6).nullable().bridgedTo(BigDecimal.class));
        conversionMap.put(Types.DATE, DataTypes.DATE().nullable().bridgedTo(java.sql.Date.class));
        conversionMap.put(Types.TIME, DataTypes.TIME(0).nullable().bridgedTo(java.sql.Time.class));
        conversionMap.put(Types.TIMESTAMP, DataTypes.TIMESTAMP(9).nullable().bridgedTo(java.sql.Timestamp.class));
        conversionMap.put(
                Types.TIMESTAMP_WITH_TIMEZONE,
                DataTypes.TIMESTAMP_WITH_LOCAL_TIME_ZONE(9).nullable().bridgedTo(java.sql.Timestamp.class)
        );
        JDBC_TYPE_MAPPING = Collections.unmodifiableMap(conversionMap);
    }

    /**
     * fixme 特殊数据类型使用的时候 注意精度丢失
     */
    public static LogicalType getLogicalType(int jdbcType) {
        DataType dataType = JDBC_TYPE_MAPPING.get(jdbcType);
        if (dataType == null) {
            throw new UnsupportedOperationException("Unsupported jdbcType:" + jdbcType);
        }
        return dataType.getLogicalType();
    }

    public static String getInsertSql(String tableName, JdbcDialect jdbcDialect, String[] insertCols) {
        String columns =
                Arrays.stream(insertCols)
                      .map(jdbcDialect::quoteIdentifier)
                      .collect(Collectors.joining(", "));
        String placeholders =
                Arrays.stream(insertCols).map(f -> "?").collect(Collectors.joining(", "));
        return "INSERT INTO "
                + jdbcDialect.quoteIdentifier(tableName)
                + "("
                + columns
                + ")"
                + " VALUES ("
                + placeholders
                + ")";
    }
}
