package com.bobandata.cloud.trade.flink;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-03-07日 15:46
 * @description
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MlTermContractEnergy implements Serializable {

    private String tradeId;
    private String segCode;
    private String segName;
    private String segment;
    private BigDecimal energy;
    private BigDecimal price;
}
