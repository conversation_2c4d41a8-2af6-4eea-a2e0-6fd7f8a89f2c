package com.bobandata.cloud.trade.flink;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.bobandata.cloud.common.utils.SnowflakeUtil;
import com.bobandata.cloud.common.utils.StrUtils;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.util.TimeSegmentUtils;
import com.mysql.jdbc.Driver;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.apache.flink.api.common.functions.RichGroupReduceFunction;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.operators.DataSource;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.jdbc.JdbcInputFormat;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-03-06日 15:00
 * @description
 */
public class ITTestFlinkDataSet implements Serializable {

    @Test
    public void testDataConvert() throws Exception {

    }

    @Test
    public void testFlinkBase() throws Exception {
        final ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        JdbcInputFormat.JdbcInputFormatBuilder builder = new JdbcInputFormat.JdbcInputFormatBuilder();
        RowTypeInfo rowTypeInfo = getRowTypeInfo();
        JdbcInputFormat inputFormat = builder.setDBUrl(
                                                     "jdbc:mysql://*************:32372/trade_sms?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8")
                                             .setRowTypeInfo(rowTypeInfo)
                                             .setUsername("root")
                                             .setPassword("root")
                                             .setDrivername(Driver.class.getName())
                                             .setQuery("select distinct tradeseq_id,\n" +
                                                               "                elec_contract_id,\n" +
                                                               "                vendee_units_code,\n" +
                                                               "                vendee_units_name,\n" +
                                                               "                sale_units_name,\n" +
                                                               "                sale_units_code,\n" +
                                                               "                start_time,\n" +
                                                               "                end_time,\n" +
                                                               "                time_division_code,\n" +
                                                               "                vendee_energy,\n" +
                                                               "                vendee_price,\n" +
                                                               "                trade_caption\n" +
                                                               "from trade_result_zcq_do_hn\n" +
                                                               "where ( start_time like '%2024%')")
                                             .finish();
        DataSource<Row> dataSource = env.createInput(inputFormat);
        dataSource.map(MlTradeBaseContractData::fromRow)
                  .groupBy((KeySelector<MlTradeBaseContractData, String>) MlTradeBaseContractData::getKey)
                  .reduceGroup(new RichGroupReduceFunction<MlTradeBaseContractData, MlTermContractInfo>() {
                      @Override
                      public void reduce(final Iterable<MlTradeBaseContractData> iterable,
                                         final Collector<MlTermContractInfo> collector) throws Exception {
                          ArrayList<MlTradeBaseContractData> baseContractDatas = new ArrayList<>();
                          for (final MlTradeBaseContractData mlTradeBaseContractData : iterable) {
                              baseContractDatas.add(mlTradeBaseContractData);
                          }
                          MlTradeBaseContractData mlTradeBaseContractData = baseContractDatas.get(0);
                          String idStr = SnowflakeUtil.getIdStr();
                          String startTime = mlTradeBaseContractData.getStartTime();
                          DateTime dateTime = DateUtil.parse(startTime, "yyyy-MM-dd'T'HH:mm:ss.000+0800");
                          LocalDateTime localDateTime = dateTime.toLocalDateTime();
                          Timestamp timestamp = Timestamp.valueOf(localDateTime);
                          DateTime end = DateUtil.parse(
                                  mlTradeBaseContractData.getEndTime(), "yyyy-MM-dd'T'HH:mm:ss.000+0800");
                          LocalDateTime endLocalDate = end.toLocalDateTime();
                          Timestamp endStamp = Timestamp.valueOf(endLocalDate);
                          MlTermContractInfo termContractInfo = MlTermContractInfo.builder()
                                                                                  .tradeId(idStr)
                                                                                  .tradeName(
                                                                                          mlTradeBaseContractData.getElecContractId())
                                                                                  .dateYear(
                                                                                          Year.of(localDateTime.getYear())
                                                                                              .getValue())
                                                                                  .dateMonth(localDateTime.getMonth()
                                                                                                          .getValue())
                                                                                  .arrId(mlTradeBaseContractData.getTradeSeqId())
                                                                                  .arrName(
                                                                                          mlTradeBaseContractData.getArrName())
                                                                                  .sellerUnitId(
                                                                                          mlTradeBaseContractData.getSaleUnitsCode())
                                                                                  .sellerUnitName(
                                                                                          mlTradeBaseContractData.getSaleUnitsName())
                                                                                  .purchaseUnitId(
                                                                                          mlTradeBaseContractData.getVendeeUnitsCode())
                                                                                  .purchaseUnitName(
                                                                                          mlTradeBaseContractData.getVendeeUnitsName())
                                                                                  .startTime(timestamp)
                                                                                  .endTime(endStamp)
                                                                                  .build();
                          BigDecimal sumEnergy = BigDecimal.ZERO;
                          List<MlTermContractEnergy> mlTermContractEnergies = new ArrayList<>();
                          for (final MlTradeBaseContractData baseData : baseContractDatas) {
                              sumEnergy = sumEnergy.add(baseData.getVendeeEnergy());
                              String timeDivisionCode = baseData.getTimeDivisionCode();
                              String segName = StrUtils.substringBefore(timeDivisionCode, ":");
                              List<TimeSegment> timeSegments = TimeSegmentUtils.create24TimeSegmentByPattern(
                                      timeDivisionCode);
                              if (timeSegments.isEmpty()) {
                                  return;
                              }
                              List<TimeSegment> singleSegs = timeSegments.stream()
                                                                         .flatMap(
                                                                                 timeSegment -> TimeSegmentUtils.split24Segment(
                                                                                         timeSegment).stream())
                                                                         .collect(Collectors.toList());
                              BigDecimal vendeeEnergy = baseData.getVendeeEnergy();
                              BigDecimal energy = vendeeEnergy.divide(
                                      new BigDecimal(singleSegs.size()), 3, RoundingMode.HALF_UP);
                              List<MlTermContractEnergy> tempList = singleSegs.stream()
                                                                              .map(timeSegment -> MlTermContractEnergy.builder()
                                                                                                                      .tradeId(
                                                                                                                              idStr)
                                                                                                                      .segName(
                                                                                                                              segName)
                                                                                                                      .segment(
                                                                                                                              timeSegment.toString())
                                                                                                                      .energy(energy)
                                                                                                                      .price(baseData.getVendeePrice())
                                                                                                                      .build())
                                                                              .collect(Collectors.toList());
                              mlTermContractEnergies.addAll(tempList);
                          }
                          termContractInfo.setContractEnergy(sumEnergy);
                          termContractInfo.setContractEnergies(
                                  mlTermContractEnergies.toArray(new MlTermContractEnergy[0]));
                          collector.collect(termContractInfo);
                      }
                  }).output(new RichOutputFormat<MlTermContractInfo>() {

                      private static final String ENERGY_SQL = "insert into ml_term_contract_energy (trade_id, seg_name, segment, energy, price)\n" +
                              "values (?,?,?,?,?)";

                      private static final String CONTRACT_SQL = "insert into ml_term_contract_info (trade_id, date_year, date_month, trade_name, arr_id, arr_name, seller_unit_id,\n" +
                              "                                        seller_unit_name, purchase_unit_id, purchase_unit_name, start_time, end_time,\n" +
                              "                                        contract_energy)\n" +
                              "values (?,?,?,?,?,?,?,?,?,?,?,?,?)";

                      private transient Connection connection;

                      private transient PreparedStatement energyStatement;

                      private transient PreparedStatement contractStatement;

                      @Override
                      public void configure(final Configuration configuration) {

                      }

                      @Override
                      public void open(final int i, final int i1) throws IOException {
                          try {
                              Class.forName("com.mysql.jdbc.Driver");
                              this.connection = DriverManager.getConnection(
                                      "jdbc:mysql://*************:32372/trade_sms?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=GMT%2B8",
                                      "root", "root"
                              );
                              energyStatement = this.connection.prepareStatement(ENERGY_SQL);
                              contractStatement = this.connection.prepareStatement(CONTRACT_SQL);
                          } catch (SQLException | ClassNotFoundException e) {
                              throw new RuntimeException(e);
                          }
                      }

                      @SneakyThrows
                      @Override
                      public void writeRecord(final MlTermContractInfo mlTermContractInfo) throws IOException {
                          contractStatement.setString(1, mlTermContractInfo.getTradeId());
                          contractStatement.setInt(2, mlTermContractInfo.getDateYear());
                          contractStatement.setInt(3, mlTermContractInfo.getDateMonth());
                          contractStatement.setString(4, mlTermContractInfo.getTradeName());
                          contractStatement.setString(5, mlTermContractInfo.getArrId());
                          contractStatement.setString(6, mlTermContractInfo.getArrName());
                          contractStatement.setString(7, mlTermContractInfo.getSellerUnitId());
                          contractStatement.setString(8, mlTermContractInfo.getSellerUnitName());
                          contractStatement.setString(9, mlTermContractInfo.getPurchaseUnitId());
                          contractStatement.setString(10, mlTermContractInfo.getPurchaseUnitName());
                          contractStatement.setTimestamp(11, mlTermContractInfo.getStartTime());
                          contractStatement.setTimestamp(12, mlTermContractInfo.getEndTime());
                          contractStatement.setBigDecimal(13, mlTermContractInfo.getContractEnergy());
                          contractStatement.execute();
                          MlTermContractEnergy[] contractEnergies = mlTermContractInfo.getContractEnergies();
                          for (final MlTermContractEnergy contractEnergy : contractEnergies) {
                              energyStatement.setString(1, contractEnergy.getTradeId());
                              energyStatement.setString(2, contractEnergy.getSegName());
                              energyStatement.setString(3, contractEnergy.getSegment());
                              energyStatement.setBigDecimal(4, contractEnergy.getEnergy());
                              energyStatement.setBigDecimal(5, contractEnergy.getPrice());
                              energyStatement.addBatch();
                          }
                          energyStatement.executeBatch();
                          energyStatement.clearBatch();
                      }

                      @Override
                      public void close() throws IOException {
                          if (this.connection != null) {
                              try {
                                  this.connection.close();
                              } catch (SQLException e) {
                                  throw new RuntimeException(e);
                              }
                          }
                      }
                  });
        env.execute();
    }

    private static RowTypeInfo getRowTypeInfo() {
        TypeInformation[] fieldTypes = new TypeInformation[] {
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO,
                BasicTypeInfo.BIG_DEC_TYPE_INFO,
                BasicTypeInfo.BIG_DEC_TYPE_INFO,
                BasicTypeInfo.STRING_TYPE_INFO
        };
        return new RowTypeInfo(fieldTypes);
    }
}
