package com.bobandata.cloud.trade.file.config;

import com.bobandata.cloud.trade.file.core.client.FileClientFactory;
import com.bobandata.cloud.trade.file.core.client.FileClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 文件配置类
 *
 * <AUTHOR>
@AutoConfiguration
public class TradeFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
