package com.bobandata.cloud.trade.operatelog.core.service;

import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;

/**
 * 操作日志 Framework Service 实现类
 *
 * <AUTHOR>
@RequiredArgsConstructor
public class OperateLogFrameworkServiceImpl implements OperateLogFrameworkService {

    @Override
    @Async
    public void createOperateLog(OperateLogDto operateLog) {

    }

}
