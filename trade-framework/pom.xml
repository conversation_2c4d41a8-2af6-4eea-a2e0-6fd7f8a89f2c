<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trade-manager-develop</artifactId>
        <groupId>com.bobandata.cloud</groupId>
        <version>${re.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trade-framework</artifactId>
    <name>trade : manager :: framework</name>
    <packaging>pom</packaging>
    <modules>
        <module>trade-framework-common</module>
        <module>trade-springboot-starter-web</module>
        <module>trade-springboot-starter-mybatisplus</module>
        <module>trade-springboot-starter-job</module>
        <module>trade-springboot-starter-rpc</module>
        <module>trade-springboot-starter-expression</module>
        <module>trade-springboot-starter-file</module>
        <module>trade-springboot-starter-codes</module>
        <module>trade-springboot-starter-variable</module>
        <module>trade-springboot-starter-operatelog</module>
        <module>trade-springboot-starter-mvc</module>
        <module>trade-springboot-starter-notice</module>
        <module>trade-springboot-starter-data-permission</module>
        <module>trade-springboot-starter-data-service</module>
        <module>trade-springboot-starter-excel</module>
        <module>trade-springboot-starter-curve</module>
        <module>trade-springboot-starter-flink</module>
        <module>trade-springboot-starter-security</module>
    </modules>


</project>