package com.bobandata.cloud.common.core.deserialize;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class TimestampDeserialize extends JsonDeserializer<Timestamp> {

    @Override
    public Timestamp deserialize(final JsonParser p,
                                 final DeserializationContext ctxt) throws IOException, JsonProcessingException {
        String text = p.getText();
        if (text == null || text.isEmpty()) {
            return null;
        }
        return Timestamp.valueOf(text);
    }
}
