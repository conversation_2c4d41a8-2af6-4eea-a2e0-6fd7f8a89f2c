package com.bobandata.cloud.common.pojo.label;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.sql.Date;

/**
 * <AUTHOR>
 * @date 2024-03-21日 15:11
 * @description
 */
public class DateLabelValue extends LabelValue {

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateValue;

    public DateLabelValue(Date value) {
        this.dateValue = value;
        this.label = "name";
    }

    @Override
    public Date getValue() {
        return dateValue;
    }
}
