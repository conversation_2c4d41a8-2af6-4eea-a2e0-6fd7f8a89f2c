package com.bobandata.cloud.common.utils.array;

import cn.hutool.core.util.RandomUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static cn.hutool.core.util.RandomUtil.randomDouble;
import static cn.hutool.core.util.RandomUtil.randomLong;

/**
 * <AUTHOR>
 * @date 2023-11-29日 10:40
 * @description
 */
public class RandomUtils {

    public static long[] randomLongs(int length) {
        final long[] range = new long[length];
        for (int i = 0; i < length; i++) {
            long random = randomLong();
            range[i] = random;
        }
        return range;
    }

    public static double[] randomDoubles(int length) {
        final double[] range = new double[length];
        for (int i = 0; i < length; i++) {
            double random = randomDouble();
            range[i] = random;
        }
        return range;
    }

    public static BigDecimal[] randomBigDecimals(int length) {
        final BigDecimal[] range = new BigDecimal[length];
        for (int i = 0; i < length; i++) {
            BigDecimal random = RandomUtil.randomBigDecimal().setScale(3, RoundingMode.HALF_UP);
            range[i] = random;
        }
        return range;
    }

}
