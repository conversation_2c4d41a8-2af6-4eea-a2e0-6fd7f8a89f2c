package com.bobandata.cloud.common.core.deserialize;

import com.bobandata.cloud.common.utils.BobanDateUtil;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * jackson yyyy-MM-dd格式参数转换为LocalDateTime
 */
public class LocalDateTimeDeserializer extends StdDeserializer<LocalDateTime> {

    public LocalDateTimeDeserializer() {
        super(LocalDateTime.class);
    }

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        String dateString = p.readValueAs(String.class);
        dateString = BobanDateUtil.formatDateString(dateString);
        if (dateString.length() <= BobanDateUtil.FORMAT_DATE_Y4MD.length()) {
            return LocalDate.parse(dateString, BobanDateUtil.FORMATTER_DATE_Y4MD).atStartOfDay();
        }
        return LocalDateTime.parse(dateString, BobanDateUtil.FORMATTER_DATETIME_Y4MDHMS);
    }
}
