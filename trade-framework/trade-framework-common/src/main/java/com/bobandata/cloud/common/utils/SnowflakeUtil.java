package com.bobandata.cloud.common.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @date 2022-12-24日 13:07
 * @description
 */
public class SnowflakeUtil {

    private static final Snowflake FLAKE = IdUtil.getSnowflake();

    /**
     * 生成long 类型的ID
     *
     * @return
     */
    public static Long getId() {
        return FLAKE.nextId();
    }

    /**
     * 生成String 类型的ID
     *
     * @return
     */
    public static String getIdStr() {
        return FLAKE.nextIdStr();
    }
}
