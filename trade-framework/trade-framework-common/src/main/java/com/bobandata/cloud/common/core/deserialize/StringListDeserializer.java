package com.bobandata.cloud.common.core.deserialize;

import com.bobandata.cloud.common.utils.json.JsonUtils;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import java.io.IOException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * jackson "field0010":"[\"2\",\"1\",\"3\"]" 转化成List<String>
 * <p>
 * 使用方式：字段上增加 @JsonDeserialize(using = StringListDeserializer.class)
 * </p>
 */
public class StringListDeserializer extends StdDeserializer<List<String>> {

    public StringListDeserializer() {
        super(List.class);
    }

    @Override
    public List<String> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        String stringList = p.readValueAs(String.class);
        if (StringUtils.isNotBlank(stringList)) {
            return JsonUtils.parseObject(stringList, new TypeReference<List<String>>() {
            });
        } else {
            return null;
        }
    }
}
