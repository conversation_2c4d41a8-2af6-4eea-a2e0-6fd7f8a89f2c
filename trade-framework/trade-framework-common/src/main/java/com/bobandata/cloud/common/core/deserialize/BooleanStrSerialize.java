package com.bobandata.cloud.common.core.deserialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023-10-12日 11:58
 * @description
 */
public class BooleanStrSerialize extends JsonSerializer<Boolean> {

    @Override
    public void serialize(final Boolean value,
                          final JsonGenerator gen,
                          final SerializerProvider serializers) throws IOException {
        if (value != null) {
            if (value) {
                gen.writeString("是");
            } else {
                gen.writeString("否");
            }
        } else {
            gen.writeNull();
        }
    }
}
