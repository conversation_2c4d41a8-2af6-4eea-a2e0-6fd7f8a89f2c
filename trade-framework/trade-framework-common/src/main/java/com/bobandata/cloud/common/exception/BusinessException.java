package com.bobandata.cloud.common.exception;

import com.bobandata.cloud.common.enums.ServiceStatus;
import com.bobandata.cloud.common.utils.BobanStrUtil;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用的业务异常类 BusinessException (json形式返回值同JsonResult，便于前端统一处理)
 */
public class BusinessException extends RuntimeException {

    private Integer code;

    /**
     * 错误的状态
     */
    private ServiceStatus serviceStatus;

    /**
     * 默认：操作失败
     */
    public BusinessException() {
        super(ServiceStatus.FAIL_OPERATION.label());
        this.serviceStatus = ServiceStatus.FAIL_OPERATION;
    }

    /**
     * 自定义状态码
     */
    public BusinessException(ServiceStatus serviceStatus) {
        super(serviceStatus.label());
        this.serviceStatus = serviceStatus;
    }

    /**
     * 自定义状态码和异常
     */
    public BusinessException(ServiceStatus serviceStatus, Throwable ex) {
        super(serviceStatus.label(), ex);
        this.serviceStatus = serviceStatus;
    }

    /**
     * 自定义状态码和内容提示
     */
    public BusinessException(ServiceStatus serviceStatus, String msg, Object... args) {
        super(serviceStatus.label() + ": " + BobanStrUtil.format(msg, args));
        this.serviceStatus = serviceStatus;
    }

    /**
     * 自定义状态码和内容提示
     */
    public BusinessException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    /**
     * 自定义内容提示
     */
    public BusinessException(String msg, Object... args) {
        super(BobanStrUtil.format(msg, args));
        this.serviceStatus = ServiceStatus.FAIL_OPERATION;
    }

    /**
     * 自定义内容提示
     */
    public BusinessException(ServiceStatus serviceStatus, Throwable ex, String msg, Object... args) {
        super(serviceStatus.label() + ": " + BobanStrUtil.format(msg, args), ex);
        this.serviceStatus = serviceStatus;
    }

    /**
     * 自定义内容提示
     */
    public BusinessException(int code, Throwable ex, String msg, Object... args) {
        super(BobanStrUtil.format(msg, args), ex);
        this.code = code;
    }

    /**
     * 转换为Map
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>(8);
        map.put("code", getCode());
        map.put("msg", getMessage());
        return map;
    }

    /**
     * 获取status，以便复用
     */
    public ServiceStatus getStatus() {
        return this.serviceStatus;
    }

    private Integer getCode() {
        if (this.code == null && this.serviceStatus != null) {
            this.code = this.serviceStatus.code();
        }
        return this.code;
    }

}
