package com.bobandata.cloud.common.exception;

import com.bobandata.cloud.common.utils.BobanStrUtil;
import java.util.HashMap;
import java.util.Map;

/**
 * 无效使用异常类 InvalidUsageException
 */
public class InvalidUsageException extends RuntimeException {

    /**
     * 自定义内容提示
     *
     * @param msg
     */
    public InvalidUsageException(String msg, Object... args) {
        super(BobanStrUtil.format(msg, args));
    }

    /**
     * 自定义内容提示
     *
     * @param msg
     */
    public InvalidUsageException(Throwable ex, String msg, Object... args) {
        super(BobanStrUtil.format(msg, args), ex);
    }

    /**
     * 转换为Map
     *
     * @return
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>(8);
        map.put("code", getCode());
        map.put("msg", getMessage());
        return map;
    }

    private int getCode() {
        return 5005;
    }

}
