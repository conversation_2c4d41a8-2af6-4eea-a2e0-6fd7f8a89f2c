package com.bobandata.cloud.common.pojo.label;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * LabelValue键值对形式的VO（用于构建显示名Name-存储值Value形式的结果）
 */
@Getter
@Setter
@Accessors(chain = true)
public class LabelValue implements Serializable {
    private static final long serialVersionUID = -2358161241655186720L;

    public LabelValue() {
    }

    public LabelValue(Object value) {
        this.value = value;
        this.label = "name";
    }

    public LabelValue(String label, Object value) {
        this.value = value;
        this.label = label;
    }

    /**
     * label: 显示值
     */
    protected String label;

    /**
     * value: 存储值
     */
    protected Object value;

    /**
     * 扩展值
     */
    protected Object ext;

}
