package com.bobandata.cloud.common.utils;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.util.ClassUtils;

public class BobanAnnotationUtils {

    private static final String BASE_PACKAGE = "com.bobandata";
    private static final String RESOURCE_PATTERN = "/**/*.class";

    public static <T extends Annotation> Map<Class<?>, T> scanAnnotation(Class<T> tClass) {
        PathMatchingResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();
        String pattern = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX +
                ClassUtils.convertClassNameToResourcePath(BASE_PACKAGE) + RESOURCE_PATTERN;
        Map<Class<?>, T> annotationMap = new HashMap<>();
        try {
            Resource[] resources = patternResolver.getResources(pattern);
            CachingMetadataReaderFactory metadataReaderFactory = new CachingMetadataReaderFactory(patternResolver);
            for (final Resource resource : resources) {
                MetadataReader reader = metadataReaderFactory.getMetadataReader(resource);
                ClassMetadata classMetadata = reader.getClassMetadata();
                String className = classMetadata.getClassName();
                Class<?> clazz = org.apache.commons.lang3.ClassUtils.getClass(className);
                if (clazz.isAnnotationPresent(tClass)) {
                    T annotation = clazz.getAnnotation(tClass);
                    annotationMap.put(clazz, annotation);
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        return annotationMap;
    }

    public static void setAnnotationValue(Annotation annotationClass, String key, Object value) {
        try {
            //获取这个代理实例所持有的 InvocationHandler
            InvocationHandler h = Proxy.getInvocationHandler(annotationClass);
            // 获取 AnnotationInvocationHandler 的 memberValues 字段
            Field hField = null;
            hField = h.getClass().getDeclaredField("memberValues");
            // 因为这个字段事 private final 修饰，所以要打开权限
            hField.setAccessible(true);
            Map memberValues = null;
            memberValues = (Map) hField.get(h);
            // 修改 权限注解value 属性值
            memberValues.put(key, value);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }
}
