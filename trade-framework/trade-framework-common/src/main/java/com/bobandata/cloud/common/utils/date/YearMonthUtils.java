package com.bobandata.cloud.common.utils.date;

import com.google.common.base.Preconditions;
import java.sql.Date;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-03-09日 11:15
 * @description
 */
public final class YearMonthUtils {

    public static List<String> getYearMonthsStrList(Year year) {
        return Arrays.stream(Month.values())
                     .map(month -> YearMonth.of(year.getValue(), month).toString())
                     .collect(Collectors.toList());
    }

    public static String[] getYearMonthsStr(Year year) {
        return Arrays.stream(Month.values())
                     .map(month -> YearMonth.of(year.getValue(), month).toString())
                     .toArray(String[]::new);
    }

    public static Month[] getMonths() {
        return Month.values();
    }

    /**
     * @return 最近12个月
     */
    public static List<YearMonth> getRecentlyMonth() {
        YearMonth now = YearMonth.now();
        List<YearMonth> yearMonths = new ArrayList<>(12);
        for (int i = 0; i < 12; i++) {
            yearMonths.add(now.minusMonths(i));
        }
        return yearMonths;
    }

    public static Date getStartDate(YearMonth yearMonth) {
        Preconditions.checkNotNull(yearMonth);
        return Date.valueOf(LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), 1));
    }

    public static Date getEndDate(YearMonth yearMonth) {
        Preconditions.checkNotNull(yearMonth);
        int lengthOfMonth = yearMonth.lengthOfMonth();
        LocalDate localDate = LocalDate.of(yearMonth.getYear(), yearMonth.getMonthValue(), lengthOfMonth);
        return Date.valueOf(localDate);
    }

    /**
     * @param yearMonth 月份
     * @return 当月的每天
     */
    public static List<Date> getMonthDates(YearMonth yearMonth) {
        Date startDate = getStartDate(yearMonth);
        Date endDate = getEndDate(yearMonth);
        LocalDate start = startDate.toLocalDate();
        LocalDate end = endDate.toLocalDate();
        ArrayList<Date> monthDays = new ArrayList<>();
        while (start.compareTo(end) <= 0) {
            monthDays.add(Date.valueOf(start));
            start = start.plusDays(1);
        }
        return monthDays;
    }
}
