package com.bobandata.cloud.common.pojo;

import com.bobandata.cloud.common.enums.ErrorCode;
import com.bobandata.cloud.common.enums.GlobalErrorCodeConstants;
import com.bobandata.cloud.common.enums.ServiceStatus;
import com.bobandata.cloud.common.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

/**
 * JSON返回结果
 */
public class ServiceResult<T> implements Serializable {

    private static final long serialVersionUID = 1001L;

    /***
     * 状态码
     */
    protected int code;
    /***
     * 消息内容
     */
    protected String msg;
    /***
     * 返回结果数据
     */
    protected T data;

    protected boolean success;

    /**
     * 默认成功，无返回数据
     */
    public ServiceResult() {
    }

    /**
     * 成功或失败
     */
    public ServiceResult(boolean ok) {
        this(ok ? ServiceStatus.SUCCESS : ServiceStatus.FAIL_OPERATION);
    }

    /**
     * 默认成功，有返回数据
     */
    public ServiceResult(T data) {
        this.code = ServiceStatus.SUCCESS.code();
        this.msg = ServiceStatus.SUCCESS.label();
        initMsg(null);
        this.data = data;
    }

    /**
     * 默认成功，有返回数据、及附加提示信息
     */
    public ServiceResult(T data, String additionalMsg) {
        this.code = ServiceStatus.SUCCESS.code();
        this.msg = ServiceStatus.SUCCESS.label();
        initMsg(additionalMsg);
        this.data = data;
    }

    /***
     * 非成功，指定状态
     * @param serviceStatus
     */
    public ServiceResult(ServiceStatus serviceStatus) {
        this.code = serviceStatus.code();
        this.msg = serviceStatus.label();
        initMsg(null);
        this.data = null;
    }

    /***
     * 非成功，指定状态及附加提示信息
     * @param serviceStatus
     * @param additionalMsg
     */
    public ServiceResult(ServiceStatus serviceStatus, String additionalMsg) {
        this.code = serviceStatus.code();
        this.msg = serviceStatus.label();
        initMsg(additionalMsg);
        this.data = null;
    }

    /**
     * 非成功，指定状态、返回数据
     *
     * @param serviceStatus
     * @param data
     */
    public ServiceResult(ServiceStatus serviceStatus, T data) {
        this.code = serviceStatus.code();
        this.msg = serviceStatus.label();
        initMsg(null);
        this.data = data;
    }

    /**
     * 非成功，指定状态、返回数据、及附加提示信息
     */
    public ServiceResult(ServiceStatus serviceStatus, T data, String additionalMsg) {
        this.code = serviceStatus.code();
        this.msg = serviceStatus.label();
        initMsg(additionalMsg);
        this.data = data;
    }

    /***
     * 自定义JsonResult
     * @param code
     * @param label
     * @param data
     */
    public ServiceResult(int code, String label, T data) {
        this.code = code;
        this.msg = label;
        this.data = data;
    }

    /**
     * 设置status，如果msg为空则msg设置为status.label
     *
     * @param serviceStatus
     * @return
     */
    public ServiceResult<T> status(ServiceStatus serviceStatus) {
        this.code = serviceStatus.code();
        if (this.msg == null) {
            this.msg = serviceStatus.label();
        }
        return this;
    }

    /**
     * 设置返回数据
     *
     * @param data
     * @return
     */
    public ServiceResult<T> data(T data) {
        this.data = data;
        return this;
    }

    /**
     * 设置msg
     *
     * @param additionalMsg
     * @return
     */
    public ServiceResult<T> msg(String additionalMsg) {
        initMsg(additionalMsg);
        return this;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public ServiceResult setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public void setData(T data) {
        this.data = data;
    }

    /**
     * 赋值msg（去掉重复前缀以支持与BusinessException嵌套使用）
     *
     * @param additionalMsg
     */
    private void initMsg(String additionalMsg) {
        if (StringUtils.isNotBlank(additionalMsg)) {
            if (StringUtils.startsWith(additionalMsg, this.msg)) {
                this.msg = additionalMsg;
            } else {
                this.msg += ": " + additionalMsg;
            }
        }
    }

    /***
     * 请求处理成功
     */
    public static <T> ServiceResult<T> success() {
        return new ServiceResult<>(ServiceStatus.SUCCESS);
    }

    /***
     * 请求处理成功
     */
    public static <T> ServiceResult<T> success(T data) {
        return new ServiceResult<>(ServiceStatus.SUCCESS, data);
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    public static <T> ServiceResult<T> success(T data, String msg) {
        ServiceResult<T> result = new ServiceResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.data = data;
        result.msg = msg;
        result.success = true;
        return result;
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    public static <T> ServiceResult<T> error(Integer code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        ServiceResult<T> result = new ServiceResult<>();
        result.code = code;
        result.msg = message;
        result.success = false;
        return result;
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess()) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常 如果没有，则返回 {@link #data} 数据
     */
    @JsonIgnore // 避免 jackson 序列化
    public T checkedData() {
        checkError();
        return data;
    }

    public static <T> ServiceResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> ServiceResult<T> error(ServiceException serviceException) {
        return error(serviceException.getCode(), serviceException.getMessage());
    }

    /***
     * 部分成功（一般用于批量处理场景，只处理筛选后的合法数据）
     */
    public static <T> ServiceResult<T> WARN_PARTIAL_SUCCESS(String msg) {
        return new ServiceResult<T>(ServiceStatus.WARN_PARTIAL_SUCCESS).msg(msg);
    }

    /***
     * 有潜在的性能问题
     */
    public static <T> ServiceResult<T> WARN_PERFORMANCE_ISSUE(String msg) {
        return new ServiceResult<T>(ServiceStatus.WARN_PERFORMANCE_ISSUE).msg(msg);
    }

    /***
     * 传入参数不对
     */
    public static <T> ServiceResult<T> FAIL_INVALID_PARAM(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_INVALID_PARAM).msg(msg);
    }

    /***
     * Token无效或已过期
     */
    public static <T> ServiceResult<T> FAIL_INVALID_TOKEN(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_INVALID_TOKEN).msg(msg);
    }

    /***
     * 没有权限执行该操作
     */
    public static <T> ServiceResult<T> FAIL_NO_PERMISSION(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_NO_PERMISSION).msg(msg);
    }

    /***
     * 请求资源不存在
     */
    public static <T> ServiceResult<T> FAIL_NOT_FOUND(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_NOT_FOUND).msg(msg);
    }

    /***
     * 数据校验不通过
     */
    public static <T> ServiceResult<T> FAIL_VALIDATION(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_VALIDATION).msg(msg);
    }

    /***
     * 操作执行失败
     */
    public static <T> ServiceResult<T> FAIL_OPERATION(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_OPERATION).msg(msg);
    }

    /***
     * 系统异常
     */
    public static <T> ServiceResult<T> FAIL_EXCEPTION(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_EXCEPTION).msg(msg);
    }

    /***
     * 服务不可用
     */
    public static <T> ServiceResult<T> FAIL_REQUEST_TIMEOUT(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_REQUEST_TIMEOUT).msg(msg);
    }

    /***
     * 服务不可用
     */
    public static <T> ServiceResult<T> FAIL_SERVICE_UNAVAILABLE(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_SERVICE_UNAVAILABLE).msg(msg);
    }

    /***
     * 认证不通过
     */
    public static <T> ServiceResult<T> FAIL_AUTHENTICATION(String msg) {
        return new ServiceResult<T>(ServiceStatus.FAIL_AUTHENTICATION).msg(msg);
    }

}
