package com.bobandata.cloud.common.pojo;

import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.common.pojo.label.LabelValue;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-13日 11:11
 * @description
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class KeyValueVo extends TreeMap<String, Object> {

    public KeyValueVo() {
    }

    public KeyValueVo(Map<String, ?> data) {
        super(data);
    }

    public void putKeyValues(List<KeyValue> data) {
        for (final KeyValue datum : data) {
            this.put(datum.getKey().toString(), datum.getValue());
        }
    }

    public void putKeyValues2(List<KeyValue<String, ?>> data) {
        for (final KeyValue datum : data) {
            this.put(datum.getKey().toString(), datum.getValue());
        }
    }

    public void putKeyValues(Map<String, ?> data) {
        this.putAll(data);
    }

    public void putKeyValue(String key, Object value) {
        this.put(key, value);
    }

    public void putLabelValue(LabelValue labelValue) {
        this.put(labelValue.getLabel(), labelValue.getValue());
    }

    public static KeyValueVo build(Map<String, ?> data) {
        return new KeyValueVo(data);
    }

    public static KeyValueVo build(List<String> keys, Object defaultValue) {
        KeyValueVo keyValueVo = new KeyValueVo();
        for (final String key : keys) {
            keyValueVo.put(key, defaultValue);
        }
        return keyValueVo;
    }

    public static KeyValueVo build(Map<String, ?> data, LabelValue... labelValues) {
        KeyValueVo keyValueVo = new KeyValueVo(data);
        for (final LabelValue labelValue : labelValues) {
            keyValueVo.putLabelValue(labelValue);
        }
        return keyValueVo;
    }
}
