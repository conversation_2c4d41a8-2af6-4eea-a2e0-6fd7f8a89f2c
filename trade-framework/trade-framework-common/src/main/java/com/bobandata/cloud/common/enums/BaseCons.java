package com.bobandata.cloud.common.enums;

/**
 * 基础常量定义
 */
public class BaseCons {
    /**
     * 默认字符集UTF-8
     */
    public static final String CHARSET_UTF8 = "UTF-8";
    /**
     * 逗号分隔符 ,
     */
    public static final String SEPARATOR_COMMA = ",";
    /**
     * 下划线分隔符_
     */
    public static final String SEPARATOR_UNDERSCORE = "_";
    /**
     * 横杠分隔符 -
     */
    public static final String SEPARATOR_CROSSBAR = "-";
    /**
     * 冒号分隔符
     */
    public final static String SEPARATOR_COLON = ":";
    /**
     * 斜杠路径分隔符
     */
    public final static String SEPARATOR_SLASH = "/";
    /**
     * 竖线分隔符，or
     */
    public final static String SEPARATOR_OR = "|";
    /**
     * 分号分隔符
     */
    public final static String SEPARATOR_SEMICOLON = ";";
    /**
     * 点分隔符
     */
    public static final String SEPARATOR_DOT = ".";
    /**
     * 排序 - 降序标记
     */
    public static final String ORDER_DESC = "DESC";
    /**
     * 逻辑删除列名
     */
    public static final String COLUMN_IS_DELETED = "is_deleted";
    /**
     * 默认树形结构根id
     */
    public static final String TREE_ROOT_ID = "0";
    /**
     * id空的默认值，避免null
     */
    public static final String ID_PREVENT_NULL = "0";

}
