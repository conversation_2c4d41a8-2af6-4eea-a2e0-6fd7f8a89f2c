package com.bobandata.cloud.common.core.deserialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-10-12日 11:58
 * @description
 */
public class BigDecimalSerialize extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(final BigDecimal value,
                          final JsonGenerator gen,
                          final SerializerProvider serializerProvider) throws IOException {
        if (value != null) {
            gen.writeString(value.toPlainString());
        } else {
            gen.writeNumber((BigDecimal) null);
        }
    }
}
