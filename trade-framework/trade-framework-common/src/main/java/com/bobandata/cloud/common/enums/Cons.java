package com.bobandata.cloud.common.enums;

/**
 * 基础常量定义
 */
public class Cons extends BaseCons {

    /***
     * 常用字段名定义
     */
    public enum FieldName {
        /**
         * 主键属性名
         */
        id,
        /**
         * 租户ID
         */
        tenantId,
        /**
         * 默认的上级ID属性名
         */
        parentId,
        /**
         * 子节点属性名
         */
        children,
        /**
         * 逻辑删除标记字段
         */
        deleted,
        /**
         * 创建时间字段
         */
        createTime,
        /**
         * 更新时间
         */
        updateTime,
        /**
         * 创建人
         */
        createBy,
        /**
         * 更新人
         */
        updateBy,
        /**
         * 组织id
         */
        orgId,
        /**
         * 用户id
         */
        userId,
        /**
         * 父ids路径
         */
        parentIdsPath
    }

    /***
     * 常用列名定义
     */
    public enum ColumnName {
        /**
         * 主键属性名
         */
        id,
        /**
         * 租户ID
         */
        tenant_id,
        /**
         * 默认的上级ID属性名
         */
        parent_id,
        /**
         * 子节点属性名
         */
        children,
        /**
         * 逻辑删除标记字段
         */
        is_deleted,
        /**
         * 创建时间字段
         */
        create_time,
        /**
         * 更新时间
         */
        update_time,
        /**
         * 创建人
         */
        create_by,
        /**
         * 更新人
         */
        update_by,
        /**
         * 组织id
         */
        org_id,
        /**
         * 用户id
         */
        user_id,
        /**
         * 父ids路径
         */
        parent_ids_path
    }

    /**
     * 分页相关参数
     */
    public enum PaginationParam {
        /**
         * 查询中排序参数名
         */
        orderBy,
        /**
         * 当前页数参数名
         */
        pageIndex,
        /**
         * 每页记录数参数名
         */
        pageSize,
        /**
         * 总数
         */
        totalCount;

        public static boolean isPaginationParam(String param) {
            return orderBy.name().equals(param)
                    || pageIndex.name().equals(param)
                    || pageSize.name().equals(param)
                    || totalCount.name().equals(param);
        }
    }

    /**
     * token前缀
     */
    public static final String TOKEN_PREFIX_BEARER = "Bearer";
    /**
     * token header头名称
     */
    public static final String TOKEN_HEADER_NAME = "Authorization";

    /**
     * 启用/停用 状态字典定义
     */
    public enum ENABLE_STATUS {
        /**
         * 正常
         */
        A("正常"),
        /**
         * 停用
         */
        I("停用");

        private String label;

        ENABLE_STATUS(String label) {
            this.label = label;
        }

        public String label() {
            return label;
        }

        public static String getLabel(String val) {
            if (val.equalsIgnoreCase(I.name())) {
                return I.label;
            }
            return A.label;
        }
    }

    /**
     * 成功/失败 结果状态字典定义
     */
    public enum RESULT_STATUS {
        /**
         * 正常
         */
        S("成功"),
        /**
         * 停用
         */
        F("失败");

        private String label;

        RESULT_STATUS(String label) {
            this.label = label;
        }

        public String label() {
            return label;
        }

        public static String getLabel(String val) {
            if (val.equalsIgnoreCase(S.name())) {
                return S.label;
            }
            return F.label;
        }
    }

    /**
     * 数据范围权限类型 的 字典编码
     */
    public enum DATA_PERMISSION_TYPE {
        SELF,
        SELF_AND_SUB,
        DEPT,
        DEPT_AND_SUB,
        ALL
    }

}
