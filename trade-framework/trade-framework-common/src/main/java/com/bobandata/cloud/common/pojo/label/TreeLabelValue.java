package com.bobandata.cloud.common.pojo.label;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-13日 10:39
 * @description
 */
@Getter
@Setter
@Accessors(chain = true)
public class TreeLabelValue extends LabelValue {

    /**
     * 父级ID (用于构建tree结构)
     */
    protected Object parentId;

    /**
     * 子节点集合
     */
    private List<LabelValue> children;
}
