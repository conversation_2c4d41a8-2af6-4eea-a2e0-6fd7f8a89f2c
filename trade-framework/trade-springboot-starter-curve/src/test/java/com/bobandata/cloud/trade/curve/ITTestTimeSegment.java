package com.bobandata.cloud.trade.curve;

import com.bobandata.cloud.trade.curve.core.handler.PatternTimeSegmentConvert;
import com.bobandata.cloud.trade.curve.core.handler.TimeSegmentHandler;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-03-05日 14:20
 * @description
 */
public class ITTestTimeSegment {

    @Test
    public void testCreateTimeSegment() {
        PatternTimeSegmentConvert segmentConvert = new PatternTimeSegmentConvert();
        TimeSegmentHandler timeSegmentHandler = new TimeSegmentHandler(segmentConvert);
        List<TimeSegment> timeSegments = timeSegmentHandler.create24TimeSegByPattern(
                "谷:00:00-07:00,23:00-24:00");
        System.out.println("timeSegments = " + timeSegments);
    }

    @Test
    public void testPattern() {
        String pattern = "([0-1]?[0-9]|2[0-3]):([0-5][0-9])-([0-1]?[0-9]|2[0-4]):([0-5][0-9])";
        Pattern compile = Pattern.compile(pattern);
        Matcher matcher = compile.matcher("谷:00:00-07:00,23:00-24:00");
        while (matcher.find()) {
            String group = matcher.group();
            System.out.println("group = " + group);
        }
    }
}
