package com.bobandata.cloud.trade.curve.core.seg;

import com.google.common.base.Preconditions;
import com.google.common.collect.Range;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-03-05日 13:46
 * @description
 */
public abstract class PointTimeSegment implements TimeSegment {

    @Getter
    private final String startTime;

    @Getter
    private final String endTime;

    @Getter
    private final Integer startPoint;

    @Getter
    private final Integer endPoint;

    private final Range<Integer> range;

    public PointTimeSegment(final String startTime, final String endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.startPoint = this.getPointByTime(this.getStartTime());
        this.endPoint = this.getPointByTime(this.getEndTime());
        Preconditions.checkNotNull(startPoint, "the point of startTime should not be null ! " + startTime);
        Preconditions.checkNotNull(endPoint, "the point of endTime should not be null ! " + endTime);
        Preconditions.checkArgument(startPoint < endPoint, startTime + " : " + endTime + "startTime < endTime !");
        this.range = Range.closed(startPoint, endPoint);
    }

    public abstract Integer getPointByTime(String time);

    public Range<Integer> getPointsRange() {
        return range;
    }

    public boolean contains(TimeSegment target) {
        Integer targetStart = target.getStartPoint();
        Integer targetEnd = target.getEndPoint();
        return this.startPoint <= targetStart && targetEnd <= this.endPoint;
    }

    public Integer getLength() {
        return this.endPoint - this.startPoint;
    }

    @Override
    public String toString() {
        return startTime + "-" + endTime;
    }

    @Override
    public int compareTo(final TimeSegment o) {
        if (this.getEndPoint().compareTo(o.getStartPoint()) == 0) {
            return this.getStartPoint().compareTo(o.getEndPoint());
        }
        return this.getEndPoint().compareTo(o.getStartPoint());
    }
}
