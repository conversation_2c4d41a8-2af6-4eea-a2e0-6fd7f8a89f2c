package com.bobandata.cloud.trade.curve.core.internal;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.trade.curve.core.util.DataCurveUtil;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-07-19日 14:44
 * @description
 */
public abstract class AbstractPointCurve {


    public void setPointsValues(BigDecimal[] values) {
        DataCurveUtil.setPointValues(this, values, this.getPrefix());
    }

    public Map<Integer, BigDecimal> getMapValues() {
        Map<Integer, BigDecimal> pointValues = DataCurveUtil.convert(this, this.getPrefix());
        return new HashMap<Integer, BigDecimal>(CollUtil.sort(pointValues, Integer::compare));
    }

    public BigDecimal[] getValues() {
        Map<Integer, BigDecimal> pointValues = DataCurveUtil.convert(this, this.getPrefix());
        return CollUtil.sort(pointValues, Integer::compare).values().toArray(new BigDecimal[0]);
    }

    public abstract String getPrefix();

    public abstract String getDataKey();

}
