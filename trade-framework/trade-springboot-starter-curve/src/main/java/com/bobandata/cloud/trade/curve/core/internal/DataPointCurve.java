package com.bobandata.cloud.trade.curve.core.internal;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.util.DataCurveUtil;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 曲线数据接口
 */
public interface DataPointCurve {

    String getDataKey();

    String getPrefix();

    default Map<Integer, BigDecimal> getMapValues() {
        Map<Integer, BigDecimal> pointValues = DataCurveUtil.convert(this, this.getPrefix());
        return new HashMap<Integer, BigDecimal>(CollUtil.sort(pointValues, Integer::compare));
    }

    default BigDecimal[] getValues() {
        return getMapValues().values().toArray(new BigDecimal[0]);
    }

    default double[] getDoubleValues() {
        return getMapValues().values().stream().mapToDouble(BigDecimal::doubleValue).toArray();
    }

    default BigDecimal sumValues() {
        Map<Integer, BigDecimal> mapValues = getMapValues();
        return mapValues.values().stream().reduce(BigDecimal::add).get();
    }

    default KeyValueVo getOtherKeyValue() {
        return new KeyValueVo();
    }
}
