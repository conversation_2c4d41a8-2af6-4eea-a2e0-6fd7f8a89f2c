package com.bobandata.cloud.trade.curve.core.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum NinetySixPoint implements TimeMapEnum {

    P0(0, "00:00", TwentyFourPointEnum.P0),
    P1(1, "00:15", TwentyFourPointEnum.P1),
    P2(2, "00:30", TwentyFourPointEnum.P1),
    P3(3, "00:45", TwentyFourPointEnum.P1),
    P4(4, "01:00", TwentyFourPointEnum.P1),
    P5(5, "01:15", TwentyFourPointEnum.P2),
    P6(6, "01:30", TwentyFourPointEnum.P2),
    P7(7, "01:45", TwentyFourPointEnum.P2),
    P8(8, "02:00", TwentyFourPointEnum.P2),
    P9(9, "02:15", TwentyFourPointEnum.P3),
    P10(10, "02:30", TwentyFourPointEnum.P3),
    P11(11, "02:45", TwentyFourPointEnum.P3),
    P12(12, "03:00", TwentyFourPointEnum.P3),
    P13(13, "03:15", TwentyFourPointEnum.P4),
    P14(14, "03:30", TwentyFourPointEnum.P4),
    P15(15, "03:45", TwentyFourPointEnum.P4),
    P16(16, "04:00", TwentyFourPointEnum.P4),
    P17(17, "04:15", TwentyFourPointEnum.P5),
    P18(18, "04:30", TwentyFourPointEnum.P5),
    P19(19, "04:45", TwentyFourPointEnum.P5),
    P20(20, "05:00", TwentyFourPointEnum.P5),
    P21(21, "05:15", TwentyFourPointEnum.P6),
    P22(22, "05:30", TwentyFourPointEnum.P6),
    P23(23, "05:45", TwentyFourPointEnum.P6),
    P24(24, "06:00", TwentyFourPointEnum.P6),
    P25(25, "06:15", TwentyFourPointEnum.P7),
    P26(26, "06:30", TwentyFourPointEnum.P7),
    P27(27, "06:45", TwentyFourPointEnum.P7),
    P28(28, "07:00", TwentyFourPointEnum.P7),
    P29(29, "07:15", TwentyFourPointEnum.P8),
    P30(30, "07:30", TwentyFourPointEnum.P8),
    P31(31, "07:45", TwentyFourPointEnum.P8),
    P32(32, "08:00", TwentyFourPointEnum.P8),
    P33(33, "08:15", TwentyFourPointEnum.P9),
    P34(34, "08:30", TwentyFourPointEnum.P9),
    P35(35, "08:45", TwentyFourPointEnum.P9),
    P36(36, "09:00", TwentyFourPointEnum.P9),
    P37(37, "09:15", TwentyFourPointEnum.P10),
    P38(38, "09:30", TwentyFourPointEnum.P10),
    P39(39, "09:45", TwentyFourPointEnum.P10),
    P40(40, "10:00", TwentyFourPointEnum.P10),
    P41(41, "10:15", TwentyFourPointEnum.P11),
    P42(42, "10:30", TwentyFourPointEnum.P11),
    P43(43, "10:45", TwentyFourPointEnum.P11),
    P44(44, "11:00", TwentyFourPointEnum.P11),
    P45(45, "11:15", TwentyFourPointEnum.P12),
    P46(46, "11:30", TwentyFourPointEnum.P12),
    P47(47, "11:45", TwentyFourPointEnum.P12),
    P48(48, "12:00", TwentyFourPointEnum.P12),
    P49(49, "12:15", TwentyFourPointEnum.P13),
    P50(50, "12:30", TwentyFourPointEnum.P13),
    P51(51, "12:45", TwentyFourPointEnum.P13),
    P52(52, "13:00", TwentyFourPointEnum.P13),
    P53(53, "13:15", TwentyFourPointEnum.P14),
    P54(54, "13:30", TwentyFourPointEnum.P14),
    P55(55, "13:45", TwentyFourPointEnum.P14),
    P56(56, "14:00", TwentyFourPointEnum.P14),
    P57(57, "14:15", TwentyFourPointEnum.P15),
    P58(58, "14:30", TwentyFourPointEnum.P15),
    P59(59, "14:45", TwentyFourPointEnum.P15),
    P60(60, "15:00", TwentyFourPointEnum.P15),
    P61(61, "15:15", TwentyFourPointEnum.P16),
    P62(62, "15:30", TwentyFourPointEnum.P16),
    P63(63, "15:45", TwentyFourPointEnum.P16),
    P64(64, "16:00", TwentyFourPointEnum.P16),
    P65(65, "16:15", TwentyFourPointEnum.P17),
    P66(66, "16:30", TwentyFourPointEnum.P17),
    P67(67, "16:45", TwentyFourPointEnum.P17),
    P68(68, "17:00", TwentyFourPointEnum.P17),
    P69(69, "17:15", TwentyFourPointEnum.P18),
    P70(70, "17:30", TwentyFourPointEnum.P18),
    P71(71, "17:45", TwentyFourPointEnum.P18),
    P72(72, "18:00", TwentyFourPointEnum.P18),
    P73(73, "18:15", TwentyFourPointEnum.P19),
    P74(74, "18:30", TwentyFourPointEnum.P19),
    P75(75, "18:45", TwentyFourPointEnum.P19),
    P76(76, "19:00", TwentyFourPointEnum.P19),
    P77(77, "19:15", TwentyFourPointEnum.P20),
    P78(78, "19:30", TwentyFourPointEnum.P20),
    P79(79, "19:45", TwentyFourPointEnum.P20),
    P80(80, "20:00", TwentyFourPointEnum.P20),
    P81(81, "20:15", TwentyFourPointEnum.P21),
    P82(82, "20:30", TwentyFourPointEnum.P21),
    P83(83, "20:45", TwentyFourPointEnum.P21),
    P84(84, "21:00", TwentyFourPointEnum.P21),
    P85(85, "21:15", TwentyFourPointEnum.P22),
    P86(86, "21:30", TwentyFourPointEnum.P22),
    P87(87, "21:45", TwentyFourPointEnum.P22),
    P88(88, "22:00", TwentyFourPointEnum.P22),
    P89(89, "22:15", TwentyFourPointEnum.P23),
    P90(90, "22:30", TwentyFourPointEnum.P23),
    P91(91, "22:45", TwentyFourPointEnum.P23),
    P92(92, "23:00", TwentyFourPointEnum.P23),
    P93(93, "23:15", TwentyFourPointEnum.P24),
    P94(94, "23:30", TwentyFourPointEnum.P24),
    P95(95, "23:45", TwentyFourPointEnum.P24),
    P96(96, "24:00", TwentyFourPointEnum.P24);

    private final Integer point;

    private final String time;

    private final TwentyFourPointEnum twentyFourPointEnum;

    NinetySixPoint(Integer point, String time, final TwentyFourPointEnum twentyFourPointEnum) {
        this.point = point;
        this.time = time;
        this.twentyFourPointEnum = twentyFourPointEnum;
    }

    private static final Map<Integer, String> POINTS_MAP = new HashMap<Integer, String>() {
        {
            NinetySixPoint[] values = NinetySixPoint.values();
            for (NinetySixPoint value : values) {
                this.put(value.getPoint(), value.getTime());
            }
        }
    };

    private static final Map<Integer, TwentyFourPointEnum> POINTS_TWENTY_FOUR_MAP = new HashMap<Integer, TwentyFourPointEnum>() {
        {
            NinetySixPoint[] values = NinetySixPoint.values();
            for (NinetySixPoint value : values) {
                this.put(value.getPoint(), value.getTwentyFourPointEnum());
            }
        }
    };

    public static TwentyFourPointEnum getTwentyFourByPoint(Integer point) {
        return POINTS_TWENTY_FOUR_MAP.get(point);
    }

    public static List<String> getNineSixPoint() {
        return getNineSixPoint("p");
    }

    public static List<String> getNineSixPoint(String prefix) {
        return Arrays.stream(values())
                     .filter(ninetySixPoint -> ninetySixPoint.getPoint() != 0)
                     .map(ninetySixPoint -> {
                         return prefix + ninetySixPoint.getPoint();
                     }).collect(Collectors.toList());
    }
    public static NinetySixPoint getEnumByTime(String time) {
        for (final NinetySixPoint value : values()) {
            if (value.getTime().equals(time)) {
                return value;
            }
        }
        throw new IllegalArgumentException("finding points is failed!");
    }

    public static NinetySixPoint getEnumByPoint(Integer point) {
        for (final NinetySixPoint value : values()) {
            if (value.getPoint().equals(point)) {
                return value;
            }
        }
        throw new IllegalArgumentException("finding points is failed!");
    }
    public static Optional<String> getTimeByPoint(Integer point) {
        return Optional.ofNullable(POINTS_MAP.get(point));
    }


    private static final Map<Integer, List<Integer>> POINT_ENUM_LIST_MAP = new HashMap<Integer, List<Integer>>() {

        {
            Map<Integer, List<Integer>> pointEnumListMap = Arrays.stream(NinetySixPoint.values())
                                                                 .collect(Collectors.groupingBy(
                                                                     ninetySixPoint -> ninetySixPoint.getTwentyFourPointEnum().getPoint(),
                                                                     Collectors.mapping(
                                                                         new Function<NinetySixPoint, Integer>() {
                                                                             @Override
                                                                             public Integer apply(
                                                                                 final NinetySixPoint ninetySixPoint) {
                                                                                 return ninetySixPoint.getPoint();
                                                                             }
                                                                         }, Collectors.toList())
                                                                 ));
            this.putAll(pointEnumListMap);
        }
    };

    public static List<Integer> getPointsByEnum(Integer pointEnum) {
        return POINT_ENUM_LIST_MAP.get(pointEnum);
    }

    @Override
    public TimeMapEnum[] getValues() {
        return NinetySixPoint.values();
    }

    public Integer getPoint() {
        return point;
    }

    public String getTime() {
        return time;
    }

    public TwentyFourPointEnum getTwentyFourPointEnum() {
        return twentyFourPointEnum;
    }
}
