package com.bobandata.cloud.trade.curve.core.handler;

import com.bobandata.cloud.trade.curve.core.constant.SegPattern;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-03-05日 15:10
 * @description
 */
public class PatternTimeSegmentConvert {

    private final Set<TimeSegPattern> timeSegPatterns;

    public PatternTimeSegmentConvert() {
        this(new HashSet<>());
    }

    public PatternTimeSegmentConvert(final List<TimeSegPattern> segPatterns) {
        this.timeSegPatterns = new HashSet<>(segPatterns);
    }

    public PatternTimeSegmentConvert(Set<TimeSegPattern> timeSegPatterns) {
        this.timeSegPatterns = timeSegPatterns;
        this.timeSegPatterns.add(SegPattern.DEFAULT_SEG_PATTERN);
    }

    public List<String> extractSegStr(String timeSegStr, String matchKey) {
        TimeSegPattern timeSegPattern;
        if (matchKey == null || matchKey.equals(SegPattern.SEG_PATTERN_KEY)) {
            timeSegPattern = SegPattern.DEFAULT_SEG_PATTERN;
        } else {
            timeSegPattern = indexPattern(matchKey);
        }
        String patternStr = timeSegPattern.getPattern();
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(timeSegStr);
        ArrayList<String> segStrs = new ArrayList<>();
        while (matcher.find()) {
            String group = matcher.group();
            segStrs.add(group);
        }
        return segStrs;
    }

    private TimeSegPattern indexPattern(String matchKey) {
        for (final TimeSegPattern timeSegPattern : this.timeSegPatterns) {
            if (timeSegPattern.getMatchKey().equals(matchKey)) {
                return timeSegPattern;
            }
        }
        throw new IllegalArgumentException("时段指定key不存在：" + matchKey);
    }
}
