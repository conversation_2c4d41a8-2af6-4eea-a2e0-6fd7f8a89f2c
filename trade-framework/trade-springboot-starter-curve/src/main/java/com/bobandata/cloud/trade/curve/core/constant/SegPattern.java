package com.bobandata.cloud.trade.curve.core.constant;

import com.bobandata.cloud.trade.curve.core.handler.TimeSegPattern;

/**
 * <AUTHOR>
 * @date 2024-03-05日 15:06
 * @description
 */
public final class SegPattern {

    public static final String SEG_PATTERN = "([0-1]?[0-9]|2[0-3]):([0-5][0-9])-([0-1]?[0-9]|2[0-4]):([0-5][0-9])";

    public static final String SEG_PATTERN_KEY = "default";

    public static final TimeSegPattern DEFAULT_SEG_PATTERN
            = new TimeSegPattern(SEG_PATTERN_KEY, SEG_PATTERN);
}
