package com.bobandata.cloud.trade.curve.core.util;

import cn.hutool.core.util.StrUtil;
import com.bobandata.cloud.trade.curve.core.MapCurve;
import com.bobandata.cloud.trade.curve.core.NinetySixMapCurve;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapCurve;
import com.bobandata.cloud.trade.curve.core.enums.MapCurveType;
import com.bobandata.cloud.trade.curve.core.function.CurveReduceFunction;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: yhd
 * @Date: 2022/11/15 10:04
 * @Classname CurveUtils
 * @Description:
 */
public final class CurveCompatUtils {

    private CurveCompatUtils() {
    }

    public static <T> void setCurveFieldValue(TwentyFourMapCurve twentyFourMapCurve, String prefix, T value) {
        Objects.requireNonNull(value);
        try {
            Map<Integer, BigDecimal> curveValues = twentyFourMapCurve.getCurveValues();
            Field[] fields = value.getClass().getDeclaredFields();
            for (final Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                if (StrUtil.startWith(name, prefix)) {
                    String subSuf = StrUtil.subSuf(name, prefix.length());
                    Integer index = Integer.valueOf(subSuf);
                    BigDecimal decimal = curveValues.getOrDefault(index, BigDecimal.ZERO);
                    field.set(value, decimal);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> TwentyFourMapCurve convertTwentyFour(T t, String prefix) {
        return (TwentyFourMapCurve) convert(t, prefix, MapCurveType.TWENTY_FOUR);
    }

    /**
     * 将96点曲线对象属性 转换成curveMap形式
     *
     * @param t      对象
     * @param prefix 属性前缀
     * @param <T>    对象类型
     * @return 96点曲线对象
     */
    public static <T> NinetySixMapCurve convertNinetySix(T t, String prefix) {
        return (NinetySixMapCurve) convert(t, prefix, MapCurveType.NINETY_SIX);
    }

    public static <T> MapCurve convert(T t, String prefix, MapCurveType curveType) {
        try {
            Class<?> aClass = t.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            Map<Integer, BigDecimal> segmentValue = new HashMap<>();
            for (final Field declaredField : declaredFields) {
                String name = declaredField.getName();
                if (StrUtil.startWith(name, prefix)) {
                    declaredField.setAccessible(true);
                    BigDecimal value = (BigDecimal) declaredField.get(t);
                    String subSuf = StrUtil.subSuf(name, prefix.length());
                    Integer index = Integer.valueOf(subSuf);
                    segmentValue.put(index, value);
                }
            }
            switch (curveType) {
                case NINETY_SIX:
                    return NinetySixMapCurve.NinetySixCurveBuilder.builder()
                                                                  .addAllValues(segmentValue)
                                                                  .build();
                case TWENTY_FOUR:
                    return new TwentyFourMapCurve(segmentValue);
                default:
                    throw new UnsupportedOperationException();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 曲线相加
     *
     * @param curveList 曲线集合
     * @return 按照点位相加之后的96点曲线
     */
    public static <T extends MapCurve> T reduceCurve(List<T> curveList, MapCurveType mapCurveType) {
        CurveReduceFunction<T> curveReduceFunction = new CurveReduceFunction<>(mapCurveType);
        return curveList.stream().reduce(curveReduceFunction).get();
    }

}
