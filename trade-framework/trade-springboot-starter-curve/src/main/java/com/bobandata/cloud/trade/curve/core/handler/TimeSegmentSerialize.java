package com.bobandata.cloud.trade.curve.core.handler;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-11-29日 17:29
 * @description
 */
public class TimeSegmentSerialize extends JsonSerializer<TimeSegment> {

    @Override
    public void serialize(final TimeSegment timeSegment,
                          final JsonGenerator jsonGenerator,
                          final SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeString(timeSegment.toString());
    }
}
