package com.bobandata.cloud.trade.curve.core.function;

import com.bobandata.cloud.trade.curve.core.MapCurve;
import com.bobandata.cloud.trade.curve.core.NinetySixMapCurve;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapCurve;
import com.bobandata.cloud.trade.curve.core.enums.MapCurveType;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BinaryOperator;

/**
 * <AUTHOR>
 * @date 2023-03-28日 11:37
 * @description
 */
public class CurveReduceFunction<T extends MapCurve> implements BinaryOperator<T> {

    private final MapCurveType mapCurveType;

    public CurveReduceFunction(final MapCurveType mapCurveType) {
        this.mapCurveType = mapCurveType;
    }

    @Override
    public T apply(final T curve, final T curve2) {
        if (curve == null) {
            return curve2;
        }
        if (curve2 == null) {
            return curve;
        }
        Map<Integer, BigDecimal> curveValues1 = curve.getCurveValues();
        Map<Integer, BigDecimal> curveValues2 = curve2.getCurveValues();

        Map<Integer, BigDecimal> mergeMap = new HashMap<>(curveValues1);
        curveValues2.forEach((k, v) -> {
            mergeMap.merge(k, v, BigDecimal::add);
        });
        switch (mapCurveType) {
            case TWENTY_FOUR:
                return (T) new TwentyFourMapCurve(mergeMap);
            case NINETY_SIX:
                return (T) NinetySixMapCurve.NinetySixCurveBuilder.builder().addAllValues(mergeMap).build();
            default:
                throw new IllegalArgumentException();
        }
    }
}
