package com.bobandata.cloud.trade.curve.core.seg;

import com.bobandata.cloud.trade.curve.core.enums.TimeSegmentScale;
import com.google.common.collect.Range;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-11-29日 16:48
 * @description 时间片段
 */
public interface TimeSegment extends Serializable, Comparable<TimeSegment> {

    TimeSegmentScale getTimeSegmentScale();

    Integer getStartPoint();

    Integer getEndPoint();

    String getStartTime();

    String getEndTime();

    Range<Integer> getPointsRange();
}
