package com.bobandata.cloud.trade.curve.config;

import com.bobandata.cloud.trade.curve.core.handler.PatternTimeSegmentConvert;
import com.bobandata.cloud.trade.curve.core.handler.TimeSegmentHandler;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2024-03-05日 13:19
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(TradeCurveProperties.class)
public class TradeCurveAutoConfiguration {

    private final TradeCurveProperties tradeCurveProperties;

    public TradeCurveAutoConfiguration(TradeCurveProperties tradeCurveProperties) {
        this.tradeCurveProperties = tradeCurveProperties;
    }

    @Bean
    @ConditionalOnBean(TradeCurveProperties.class)
    public TimeSegmentHandler autoNewHandler() {
        PatternTimeSegmentConvert segmentConvert = new PatternTimeSegmentConvert(tradeCurveProperties.getSegPatterns());
        return new TimeSegmentHandler(segmentConvert);
    }
}
