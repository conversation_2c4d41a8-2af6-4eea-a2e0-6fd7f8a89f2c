package com.bobandata.cloud.trade.curve.core;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-11-22日 10:37
 * @description
 */
public class TwentyFourMapCurve extends AbstractMapCurve {

    public TwentyFourMapCurve() {
        super(Collections.emptyMap());
    }

    public TwentyFourMapCurve(final Map<Integer, BigDecimal> curveValues) {
        super(curveValues);
        if (curveValues.size() != 24) {
            Map<Integer, BigDecimal> segmentValue = new HashMap<>(24);
            for (int i = 1; i <= 24; i++) {
                segmentValue.put(i, BigDecimal.ZERO);
            }
            segmentValue.putAll(this.curveValues);
            this.curveValues = segmentValue;
        }
    }

    @Override
    public String toString() {
        return "TwentyFourMapCurve{" +
                "curveValues=" + curveValues + '}';
    }

}
