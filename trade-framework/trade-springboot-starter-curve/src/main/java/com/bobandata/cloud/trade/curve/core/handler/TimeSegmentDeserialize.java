package com.bobandata.cloud.trade.curve.core.handler;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-11-29日 17:28
 * @description
 */
public class TimeSegmentDeserialize extends JsonDeserializer<TimeSegment> {
    @Override
    public TimeSegment deserialize(final JsonParser jsonParser,
                                   final DeserializationContext deserializationContext) throws IOException, JacksonException {
        String text = jsonParser.getText();
        if (text == null || text.isEmpty()) {
            return null;
        }
        return TwentyFourPointTimeSegment.valueOf(text);
    }
}
