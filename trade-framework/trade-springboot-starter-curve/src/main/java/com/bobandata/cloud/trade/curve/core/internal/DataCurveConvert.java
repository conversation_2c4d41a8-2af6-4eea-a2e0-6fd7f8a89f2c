package com.bobandata.cloud.trade.curve.core.internal;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.trade.curve.core.enums.NinetySixPoint;
import com.bobandata.cloud.trade.curve.core.enums.TwentyFourPointEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-07-19日 14:47
 * @description 曲线数据转换
 */
public class DataCurveConvert {

    public static final String DEFAULT_NAME_KEY = "name";

    public static <T extends DataPointCurve> List<KeyValueVo> convert24LabelValue(T[] dataCurves, String nameKey) {
        TwentyFourPointEnum[] values = TwentyFourPointEnum.values();
        List<String> timeKeys = Arrays.stream(values)
                                      .filter(v -> v.getPoint() != 0)
                                      .map(TwentyFourPointEnum::getTime)
                                      .collect(Collectors.toList());
        Map<String, KeyValueVo> keyValueVoMap = new HashMap<>();
        for (String timeKey : timeKeys) {
            KeyValueVo keyValueVo = new KeyValueVo();
            keyValueVo.put(nameKey, timeKey);
            keyValueVoMap.put(timeKey, keyValueVo);
        }
        for (T dataCurve : dataCurves) {
            if (dataCurve == null) {
                continue;
            }
            String dataKey = dataCurve.getDataKey();
            Map<Integer, BigDecimal> mapValues = dataCurve.getMapValues();
            for (Map.Entry<Integer, BigDecimal> entry : mapValues.entrySet()) {
                Integer key = entry.getKey();
                Optional<String> timeByPoint = TwentyFourPointEnum.getTimeByPoint(key);
                String name = timeByPoint.get();
                KeyValueVo keyValueVo = keyValueVoMap.get(name);
                if (!dataCurve.getOtherKeyValue().isEmpty()) {
                    keyValueVo.putKeyValues(dataCurve.getOtherKeyValue());
                }
                keyValueVo.put(dataKey, entry.getValue());
            }
        }
        List<KeyValueVo> keyValueVos = new ArrayList<>(keyValueVoMap.values());
        keyValueVos.sort(Comparator.comparing(o -> (Comparable) o.get(nameKey)));
        return keyValueVos;
    }

    public static <T extends DataPointCurve> List<KeyValueVo> convertLabelValue(T[] dataCurves, String nameKey) {
        NinetySixPoint[] values = NinetySixPoint.values();
        List<String> timeKeys = Arrays.stream(values)
                                      .filter(v -> v.getPoint() != 0)
                                      .map(NinetySixPoint::getTime)
                                      .collect(Collectors.toList());
        Map<String, KeyValueVo> keyValueVoMap = new HashMap<>();
        for (String timeKey : timeKeys) {
            KeyValueVo keyValueVo = new KeyValueVo();
            keyValueVo.put(nameKey, timeKey);
            keyValueVoMap.put(timeKey, keyValueVo);
        }
        for (T dataCurve : dataCurves) {
            if (dataCurve == null) {
                continue;
            }
            String dataKey = dataCurve.getDataKey();
            Map<Integer, BigDecimal> mapValues = dataCurve.getMapValues();
            for (Map.Entry<Integer, BigDecimal> entry : mapValues.entrySet()) {
                Integer key = entry.getKey();
                Optional<String> timeByPoint = NinetySixPoint.getTimeByPoint(key);
                String name = timeByPoint.get();
                KeyValueVo keyValueVo = keyValueVoMap.get(name);
                if (!dataCurve.getOtherKeyValue().isEmpty()) {
                    keyValueVo.putKeyValues(dataCurve.getOtherKeyValue());
                }
                keyValueVo.put(dataKey, entry.getValue());
            }
        }
        List<KeyValueVo> keyValueVos = new ArrayList<>(keyValueVoMap.values());
        keyValueVos.sort(Comparator.comparing(o -> (Comparable) o.get(nameKey)));
        return keyValueVos;
    }
}
