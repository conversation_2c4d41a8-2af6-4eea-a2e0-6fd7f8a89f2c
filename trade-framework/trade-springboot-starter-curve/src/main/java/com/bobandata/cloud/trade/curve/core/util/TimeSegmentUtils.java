package com.bobandata.cloud.trade.curve.core.util;

import com.bobandata.cloud.trade.curve.core.handler.PatternTimeSegmentConvert;
import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import com.google.common.collect.Range;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-11-30日 13:34
 * @description
 */
public final class TimeSegmentUtils {

    private TimeSegmentUtils() {
    }

    public static List<TimeSegment> create24TimeSegmentByPattern(String str) {
        PatternTimeSegmentConvert timeSegmentConvert = new PatternTimeSegmentConvert();
        List<String> timeSegStrs = timeSegmentConvert.extractSegStr(str, null);
        return timeSegStrs.stream().map(TwentyFourPointTimeSegment::valueOf).collect(Collectors.toList());
    }

    public static List<TimeSegment> create24TimeSegmentByPattern(String str,
                                                                 PatternTimeSegmentConvert timeSegmentConvert) {
        List<String> timeSegStrs = timeSegmentConvert.extractSegStr(str, null);
        return timeSegStrs.stream().map(TwentyFourPointTimeSegment::valueOf).collect(Collectors.toList());
    }

    public static TimeSegment create24TimeSegment(Integer lowerEndpoint, Integer upperEndpoint) {
        return TwentyFourPointTimeSegment.valueOf(lowerEndpoint, upperEndpoint);
    }

    public static boolean repeatSegment(TimeSegment[] timeSegments) {
        for (int i = 0; i < timeSegments.length; i++) {
            for (int j = i + 1; j < timeSegments.length; j++) {
                if (repeatSegment(timeSegments[i], timeSegments[j])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断区间是否重叠 是否可以合并
     */
    public static boolean repeatSegment(TimeSegment segment, TimeSegment segment1) {
        Range<Integer> pointsRange = segment.getPointsRange();
        Range<Integer> pointsRange1 = segment1.getPointsRange();
        if (pointsRange.isConnected(pointsRange1)) {
            Range<Integer> intersection = pointsRange.intersection(pointsRange1);
            return !intersection.upperEndpoint().equals(intersection.lowerEndpoint());
        }
        return false;
    }

    /**
     * 合并区间
     */
    public static TimeSegment merge24Segment(TimeSegment segment, TimeSegment segment1) {
        boolean repeatSegment = repeatSegment(segment, segment1);
        if (!repeatSegment) {
            Range<Integer> pointsRange = segment.getPointsRange();
            Range<Integer> pointsRange1 = segment1.getPointsRange();

            Range<Integer> span = pointsRange.span(pointsRange1);
            Integer lowerEndpoint = span.lowerEndpoint();
            Integer upperEndpoint = span.upperEndpoint();
            return create24TimeSegment(lowerEndpoint, upperEndpoint);
        }
        return null;
    }

    /**
     * 拆分时间段 一小时为单位
     */
    public static List<TimeSegment> split24Segment(TimeSegment timeSegment) {
        Integer startPoint = timeSegment.getStartPoint();
        Integer endPoint = timeSegment.getEndPoint();
        List<TimeSegment> timeSegments = new ArrayList<>();
        for (Integer i = startPoint; i < endPoint; i++) {
            TimeSegment segment = create24TimeSegment(i, i + 1);
            timeSegments.add(segment);
        }
        return timeSegments;
    }

    public static BigDecimal segmentsMean(TimeSegment timeSegment, BigDecimal value, int scale) {
        List<TimeSegment> timeSegments = split24Segment(timeSegment);
        return value.divide(new BigDecimal(timeSegments.size()), scale, RoundingMode.HALF_UP);
    }
}
