package com.bobandata.cloud.trade.curve.core.handler;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-05日 13:44
 * @description
 */
public class TimeSegmentHandler {

    private final PatternTimeSegmentConvert timeSegmentConvert;

    public TimeSegmentHandler(PatternTimeSegmentConvert timeSegmentConvert) {
        this.timeSegmentConvert = timeSegmentConvert;
    }

    public List<TimeSegment> create24TimeSegByPattern(String timeStr) {
        return this.create24TimeSegByPattern(timeStr, null);
    }

    public List<TimeSegment> create24TimeSegByPattern(String timeStr, String matchKey) {
        List<String> timeSegStrs = timeSegmentConvert.extractSegStr(timeStr, matchKey);
        return timeSegStrs.stream().map(TwentyFourPointTimeSegment::valueOf).collect(Collectors.toList());

    }
}
