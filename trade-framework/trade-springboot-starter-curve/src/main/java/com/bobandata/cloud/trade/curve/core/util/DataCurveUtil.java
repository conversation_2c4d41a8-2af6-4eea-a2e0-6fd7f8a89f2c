package com.bobandata.cloud.trade.curve.core.util;

import cn.hutool.core.util.StrUtil;
import com.bobandata.cloud.trade.curve.core.MapDataCurve;
import com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve;
import com.bobandata.cloud.trade.curve.core.enums.MapCurveType;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-21日 10:43
 * @description
 */
public final class DataCurveUtil {

    public static <T> com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve convertTwentyFour(T t,
                                                                                                    String prefix) {
        return (com.bobandata.cloud.trade.curve.core.TwentyFourMapDataCurve) convert(
                t, prefix, MapCurveType.TWENTY_FOUR);
    }

    public static <T> MapDataCurve convert(T t, String prefix, MapCurveType curveType) {
        try {
            Class<?> aClass = t.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            Map<Integer, BigDecimal> segmentValue = new HashMap<>();
            for (final Field declaredField : declaredFields) {
                String name = declaredField.getName();
                if (StrUtil.startWith(name, prefix)) {
                    declaredField.setAccessible(true);
                    BigDecimal value = (BigDecimal) declaredField.get(t);
                    if (value == null) {
                        value = BigDecimal.ZERO;
                    }
                    String subSuf = StrUtil.subSuf(name, prefix.length());
                    Integer index = Integer.valueOf(subSuf);
                    segmentValue.put(index, value);
                }
            }
            switch (curveType) {
                case TWENTY_FOUR:
                    return new TwentyFourMapDataCurve(segmentValue);
                default:
                    throw new UnsupportedOperationException();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> void setPointValues(T t, BigDecimal value, String prefix, int length) {
        try {
            Class<?> aClass = t.getClass();
            for (int i = 0; i < length; i++) {
                Field declaredField = aClass.getDeclaredField(prefix + (i + 1));
                declaredField.setAccessible(true);
                declaredField.set(t, value);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> void setPointValue(T t, BigDecimal value, int point, String prefix) {
        try {
            Class<?> aClass = t.getClass();
            Field declaredField = aClass.getDeclaredField(prefix + point);
            declaredField.setAccessible(true);
            declaredField.set(t, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> void setPointValues(T t, BigDecimal[] values, String prefix) {
        try {
            Class<?> aClass = t.getClass();
            for (int i = 0; i < values.length; i++) {
                Field declaredField = aClass.getDeclaredField(prefix + (i + 1));
                declaredField.setAccessible(true);
                declaredField.set(t, values[i]);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> Map<Integer, BigDecimal> convert(T t, String prefix) {
        try {
            Class<?> aClass = t.getClass();
            Field[] declaredFields = aClass.getDeclaredFields();
            Map<Integer, BigDecimal> segmentValue = new HashMap<>();
            for (final Field declaredField : declaredFields) {
                String name = declaredField.getName();
                if (StrUtil.startWith(name, prefix)) {
                    declaredField.setAccessible(true);
                    BigDecimal value = (BigDecimal) declaredField.get(t);
                    if (value == null) {
                        value = BigDecimal.ZERO;
                    }
                    String subSuf = StrUtil.subSuf(name, prefix.length());
                    Integer index = Integer.valueOf(subSuf);
                    segmentValue.put(index, value);
                }
            }
            return segmentValue;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
