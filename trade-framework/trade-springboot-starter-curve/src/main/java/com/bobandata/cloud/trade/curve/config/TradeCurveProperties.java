package com.bobandata.cloud.trade.curve.config;

import com.bobandata.cloud.trade.curve.core.handler.TimeSegPattern;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024-03-05日 14:59
 * @description
 */
@ConfigurationProperties("trade.curve")
@Data
public class TradeCurveProperties {

    /**
     * 时段正则匹配
     */
    private List<TimeSegPattern> segPatterns;

}
