package com.bobandata.cloud.trade.curve.core;

import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.label.DateLabelValue;
import com.bobandata.cloud.trade.curve.core.enums.TwentyFourPointEnum;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-20日 10:19
 * @description
 */
public class TwentyFourMapDataCurve implements MapDataCurve {

    private Map<Integer, BigDecimal> dataCurve;

    private Map<TwentyFourPointEnum, BigDecimal> pointData;

    public TwentyFourMapDataCurve() {
        this.dataCurve = new HashMap<>(24);
    }

    public TwentyFourMapDataCurve(Map<Integer, BigDecimal> dataCurve) {
        this.dataCurve = dataCurve;
    }

    public void putData(Integer point, BigDecimal value) {
        this.dataCurve.put(point, value);
    }

    public KeyValueVo convertKeyValue(Date curveDate) {
        DateLabelValue labelValue = new DateLabelValue(curveDate);
        Map<String, BigDecimal> timeDataMap = this.getTimeDataMap();
        return KeyValueVo.build(timeDataMap, labelValue);
    }

    public Map<TwentyFourPointEnum, BigDecimal> getEnumDataMap() {
        initPointData();
        return this.pointData;
    }

    public Map<String, BigDecimal> getTimeDataMap() {
        Map<String, BigDecimal> timeData = new HashMap<>();
        for (final Map.Entry<Integer, BigDecimal> entry : dataCurve.entrySet()) {
            Integer key = entry.getKey();
            String times = TwentyFourPointEnum.getTimeByPoint(key).get();
            timeData.put(times, entry.getValue());
        }
        return timeData;
    }

    public Map<Integer, BigDecimal> getDataCurve() {
        return dataCurve;
    }

    public void initPointData() {
        if (this.pointData == null) {
            return;
        }
        this.pointData = new HashMap<>();
        for (final Map.Entry<Integer, BigDecimal> entry : dataCurve.entrySet()) {
            Integer key = entry.getKey();
            TwentyFourPointEnum enumByTime = TwentyFourPointEnum.getEnumByTime(key);
            this.pointData.put(enumByTime, entry.getValue());
        }
    }
}
