package com.bobandata.cloud.trade.curve.core;

import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: yhd
 * @Date: 2022/11/15 9:44
 * @Classname NintySixCurve
 * @Description: 96点曲线Map格式 计算时使用
 */
@Slf4j
public class NinetySixMapCurve extends AbstractMapCurve {

    private NinetySixMapCurve(Map<Integer, BigDecimal> curveValues) {
        super(curveValues);
    }


    public static final class NinetySixCurveBuilder {

        private Map<Integer, BigDecimal> segmentValue;

        /**
         * 初始化时 将96点位填满 避免使用时值为null再填充
         */
        private NinetySixCurveBuilder() {
            this.segmentValue = new HashMap<>(96);
            for (int i = 1; i < 97; i++) {
                this.segmentValue.put(i, BigDecimal.ZERO);
            }
        }

        public static NinetySixCurveBuilder builder() {
            return new NinetySixCurveBuilder();
        }

        public void addValue(Integer timeSegment, BigDecimal value) {
            Preconditions.checkArgument(timeSegment > 0 && timeSegment < 97, "points must be in (1,96) !");
            this.segmentValue.put(timeSegment, value);
        }

        public NinetySixCurveBuilder addAllValues(Map<Integer, BigDecimal> segmentValue) {
            this.segmentValue.putAll(segmentValue);
            return this;
        }

        public NinetySixMapCurve build() {
            Preconditions.checkArgument(segmentValue.size() == 96, "values length must be 96");
            return new NinetySixMapCurve(this.segmentValue);
        }
    }

    @Override
    public String toString() {
        return "NinetySixMapCurve{" +
            "curveValues=" + curveValues + '}';
    }
}
