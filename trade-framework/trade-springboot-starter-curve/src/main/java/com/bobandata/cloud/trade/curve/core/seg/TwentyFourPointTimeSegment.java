package com.bobandata.cloud.trade.curve.core.seg;

import com.bobandata.cloud.trade.curve.core.enums.TimeSegmentScale;
import com.bobandata.cloud.trade.curve.core.enums.TwentyFourPointEnum;
import com.google.common.base.Preconditions;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-03-05日 13:48
 * @description
 */
public class TwentyFourPointTimeSegment extends PointTimeSegment {

    public TwentyFourPointTimeSegment(final String startTime,
                                      final String endTime) {
        super(startTime, endTime);
    }

    @Override
    public Integer getPointByTime(final String time) {
        return TwentyFourPointEnum.getPointByTime(time);
    }

    @Override
    public TimeSegmentScale getTimeSegmentScale() {
        return TimeSegmentScale.TWENTY_FOUR;
    }

    public static TimeSegment valueOf(String segment) {
        if (StringUtils.isBlank(segment)) {
            return null;
        }
        String[] split = segment.split("-");
        return new TwentyFourPointTimeSegment(split[0], split[1]);
    }

    public static TimeSegment valueOf(Integer startPoint, Integer endPoint) {
        Preconditions.checkArgument(startPoint < endPoint, "startTime < endTime !");
        String start = TwentyFourPointEnum.getTimeByPoint(startPoint).get();
        String end = TwentyFourPointEnum.getTimeByPoint(endPoint).get();
        return new TwentyFourPointTimeSegment(start, end);
    }
}
