package com.bobandata.cloud.trade.curve.core;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-11-22日 11:28
 * @description
 */
public abstract class AbstractMapCurve implements MapCurve {

    protected Map<Integer, BigDecimal> curveValues;

    protected AbstractMapCurve(final Map<Integer, BigDecimal> curveValues) {
        this.curveValues = curveValues;
    }

    public Map<Integer, BigDecimal> getCurveValues() {
        return curveValues;
    }
}
