package com.bobandata.cloud.trade.curve.core.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;

@Getter
public enum TwentyFourPointEnum implements PointEnums {

    P0(0, "00:00"),
    P1(1, "01:00"),
    P2(2, "02:00"),
    P3(3, "03:00"),
    P4(4, "04:00"),
    P5(5, "05:00"),
    P6(6, "06:00"),
    P7(7, "07:00"),
    P8(8, "08:00"),
    P9(9, "09:00"),
    P10(10, "10:00"),
    P11(11, "11:00"),
    P12(12, "12:00"),
    P13(13, "13:00"),
    P14(14, "14:00"),
    P15(15, "15:00"),
    P16(16, "16:00"),
    P17(17, "17:00"),
    P18(18, "18:00"),
    P19(19, "19:00"),
    P20(20, "20:00"),
    P21(21, "21:00"),
    P22(22, "22:00"),
    P23(23, "23:00"),
    P24(24, "24:00");

    private final Integer point;
    private final String time;

    TwentyFourPointEnum(Integer point, String time) {
        this.point = point;
        this.time = time;
    }

    public static TwentyFourPointEnum getEnumByTime(Integer point) {
        for (final TwentyFourPointEnum value : values()) {
            if (value.getPoint().equals(point)) {
                return value;
            }
        }
        throw new IllegalArgumentException("finding points is failed!");
    }

    public static Integer getPointByTime(String time) {
        for (final TwentyFourPointEnum value : values()) {
            if (value.getTime().equals(time)) {
                return value.getPoint();
            }
        }
        throw new IllegalArgumentException("finding points is failed!");
    }

    private static final Map<Integer, String> POINTS_MAP = new HashMap<Integer, String>() {
        {
            TwentyFourPointEnum[] values = TwentyFourPointEnum.values();
            for (TwentyFourPointEnum value : values) {
                this.put(value.getPoint(), value.getTime());
            }
        }
    };

    public static Optional<String> getTimeByPoint(Integer point) {
        return Optional.ofNullable(POINTS_MAP.get(point));
    }

}

