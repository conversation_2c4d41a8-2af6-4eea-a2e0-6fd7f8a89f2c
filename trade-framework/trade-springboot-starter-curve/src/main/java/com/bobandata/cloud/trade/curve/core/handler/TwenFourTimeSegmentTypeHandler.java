package com.bobandata.cloud.trade.curve.core.handler;

import com.bobandata.cloud.trade.curve.core.seg.TimeSegment;
import com.bobandata.cloud.trade.curve.core.seg.TwentyFourPointTimeSegment;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @date 2023-03-06日 14:23
 * @description
 */
public class TwenFourTimeSegmentTypeHandler extends BaseTypeHandler<TimeSegment> {
    @Override
    public void setNonNullParameter(PreparedStatement ps,
                                    int i,
                                    TimeSegment parameter,
                                    JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.toString());
    }

    @Override
    public TimeSegment getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String colValue = rs.getString(columnName);
        return TwentyFourPointTimeSegment.valueOf(colValue);
    }

    @Override
    public TimeSegment getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String colValue = rs.getString(columnIndex);
        return TwentyFourPointTimeSegment.valueOf(colValue);
    }

    @Override
    public TimeSegment getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String colValue = cs.getString(columnIndex);
        return TwentyFourPointTimeSegment.valueOf(colValue);
    }
}
