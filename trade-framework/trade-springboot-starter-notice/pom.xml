<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>trade-framework</artifactId>
        <groupId>com.bobandata.cloud</groupId>
        <version>${re.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>trade-springboot-starter-notice</artifactId>
    <name>trade : manager :: framework ::: notice</name>

    <dependencies>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-framework-common</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-manager-system-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>