package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import org.springframework.core.convert.converter.Converter;

/**
 * Date - LocalDate 转换器
 */
@CollectThisConvertor
public class Date2LocalDateConverter implements Converter<Date, LocalDate> {

    @Override
    public LocalDate convert(Date source) {
        ZonedDateTime zonedDateTime = source.toInstant().atZone(ZoneId.systemDefault());
        return zonedDateTime.toLocalDate();
    }
}
