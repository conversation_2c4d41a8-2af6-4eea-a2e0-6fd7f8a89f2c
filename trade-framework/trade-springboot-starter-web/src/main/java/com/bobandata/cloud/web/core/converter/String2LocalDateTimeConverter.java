/*
 * Copyright (c) 2015-2029, www.dibo.ltd (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import com.bobandata.cloud.common.utils.BobanDateUtil;
import com.bobandata.cloud.web.util.BobanValidatorUtil;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * String - LocalDateTime 转换器
 * <AUTHOR>
 * @version v2.7.0
 * @date 2022/7/26
 * Copyright © diboot.com
 */
@CollectThisConvertor
public class String2LocalDateTimeConverter implements Converter<String, LocalDateTime> {

    @Override
    public LocalDateTime convert(String dateString) {
        if(BobanValidatorUtil.isEmpty(dateString)){
            return null;
        }
        dateString = BobanDateUtil.formatDateString(dateString);
        if(dateString.length() <= BobanDateUtil.FORMAT_DATE_Y4MD.length()) {
            return LocalDate.parse(dateString, BobanDateUtil.FORMATTER_DATE_Y4MD).atStartOfDay();
        }
        return LocalDateTime.parse(dateString, BobanDateUtil.FORMATTER_DATETIME_Y4MDHMS);
    }

}
