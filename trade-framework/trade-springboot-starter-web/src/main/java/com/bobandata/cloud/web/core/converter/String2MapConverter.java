package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.common.utils.json.JsonUtils;
import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import java.util.Map;
import org.springframework.core.convert.converter.Converter;

/**
 * String - Map 转换器
 */
@CollectThisConvertor
public class String2MapConverter implements Converter<String, Map<String, Object>> {

    @Override
    public Map<String, Object> convert(String dateString) {
        return JsonUtils.parseObjectToMap(dateString);
    }

}
