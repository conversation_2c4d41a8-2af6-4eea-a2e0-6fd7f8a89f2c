package com.bobandata.cloud.web.util;

import com.bobandata.cloud.common.exception.InvalidUsageException;
import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

/**
 * <AUTHOR>
@Slf4j
@Component
@Lazy(false)
public class ApplicationContextHolder implements ApplicationContextAware, ApplicationListener<ApplicationReadyEvent> {

    private static ApplicationContext APPLICATION_CONTEXT;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        APPLICATION_CONTEXT = applicationContext;
        log.debug("ApplicationContext已赋值: {}", APPLICATION_CONTEXT.getDisplayName());
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        APPLICATION_CONTEXT = event.getApplicationContext();
        log.debug("ApplicationContext已注入: {}", APPLICATION_CONTEXT.getDisplayName());
    }

    /***
     * 获取ApplicationContext上下文
     */
    public static ApplicationContext getApplicationContext() {
        if (APPLICATION_CONTEXT == null) {
            log.debug("ApplicationContext未初始化，通过ContextLoader获取!");
            APPLICATION_CONTEXT = ContextLoader.getCurrentWebApplicationContext();
        }
        if (APPLICATION_CONTEXT == null) {
            log.warn(
                    "无法获取ApplicationContext，请确保ComponentScan扫描路径包含com.bobandata包路径，并在Spring初始化之后调用接口!");
            new InvalidUsageException("检查调用时机").printStackTrace();
        }
        return APPLICATION_CONTEXT;
    }

    /***
     * 根据beanId获取Bean实例
     * @param beanId
     * @return
     */
    public static Object getBean(String beanId) {
        return getApplicationContext().getBean(beanId);
    }

    /***
     * 获取指定类型的单个Bean实例
     * @param clazz
     * @return
     */
    public static <T> T getBean(Class<T> clazz) {
        try {
            return getApplicationContext().getBean(clazz);
        } catch (Exception e) {
            log.debug("instance not found: {}", clazz.getSimpleName());
            return null;
        }
    }

    public static ApplicationContext instance() {
        return getApplicationContext();
    }

    /***
     * 获取指定类型的全部实现类
     * @param type
     * @param <T>
     * @return
     */
    public static <T> List<T> getBeans(Class<T> type) {
        Map<String, T> map = getApplicationContext().getBeansOfType(type);
        if (BobanValidatorUtil.isEmpty(map)) {
            return null;
        }
        List<T> beanList = new ArrayList<>(map.size());
        beanList.addAll(map.values());
        return beanList;
    }

    /***
     * 根据注解获取beans
     * @param annotationType
     * @return
     */
    public static List<Object> getBeansByAnnotation(Class<? extends Annotation> annotationType) {
        Map<String, Object> map = getApplicationContext().getBeansWithAnnotation(annotationType);
        if (BobanValidatorUtil.isEmpty(map)) {
            return null;
        }
        List<Object> beanList = new ArrayList<>();
        beanList.addAll(map.values());
        return beanList;
    }


}
