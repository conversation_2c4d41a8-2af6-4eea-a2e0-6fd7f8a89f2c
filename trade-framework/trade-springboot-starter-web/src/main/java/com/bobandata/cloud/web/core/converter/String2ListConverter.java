package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.common.utils.BobanStrUtil;
import com.bobandata.cloud.common.utils.json.JsonUtils;
import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import com.bobandata.cloud.web.util.BobanValidatorUtil;
import java.util.Arrays;
import java.util.List;
import org.springframework.core.convert.converter.Converter;

/**
 * String - List 转换器
 */
@CollectThisConvertor
public class String2ListConverter implements Converter<String, List> {

    @Override
    public List convert(String source) {
        if (BobanValidatorUtil.notEmpty(source)) {
            boolean isArray = BobanStrUtil.startsWith(source, "[\"");
            if (isArray) {
                return JsonUtils.parseArray(source, String.class);
            } else {
                return Arrays.asList(source);
            }
        }
        return null;
    }
}