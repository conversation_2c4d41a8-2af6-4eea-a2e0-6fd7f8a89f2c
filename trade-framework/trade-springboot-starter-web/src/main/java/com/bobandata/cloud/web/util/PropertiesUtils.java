package com.bobandata.cloud.web.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * 配置文件工具类
 */
@Component
@Slf4j
public class PropertiesUtils implements EnvironmentAware {

    private static Environment environment;

    @Override
    public void setEnvironment(Environment env) {
        environment = env;
        log.trace("Environment 已设置");
    }

    /***
     *  读取配置项的值
     * @param key
     * @return
     */
    public static String get(String key, String defaultValue) {
        String value = get(key);
        if (value != null) {
            return value;
        }
        return defaultValue;
    }

    /***
     *  读取配置项的值
     * @param key
     * @return
     */
    public static String get(String key) {
        if (environment == null) {
            try {
                environment = ApplicationContextHolder.instance().getEnvironment();
                log.trace("通过Context获取Environment");
            } catch (Exception e) {
                log.warn("无法获取Environment，参数配置可能不生效");
            }
        }
        // 获取配置值
        if (environment == null) {
            log.warn("无法获取上下文Environment，请在Spring初始化之后调用!");
            return null;
        }
        String value = environment.getProperty(key);
        // 任何password相关的参数需解密
        //        boolean isSensitiveConfig = key.contains(".password") || key.contains(".secret");
        //        if (value != null && isSensitiveConfig) {
        //            value = Encryptor.decrypt(value);
        //        }
        return value;
    }

    /***
     *  读取int型的配置项
     * @param key
     * @return
     */
    public static Integer getInteger(String key) {
        // 获取配置值
        String value = get(key);
        if (StringUtils.isNotBlank(value)) {
            return Integer.parseInt(value);
        }
        return null;
    }

    /***
     *  读取Long型的配置项
     * @param key
     * @return
     */
    public static Long getLong(String key) {
        // 获取配置值
        String value = get(key);
        if (StringUtils.isNotBlank(value)) {
            return Long.parseLong(value);
        }
        return null;
    }

    /***
     * 读取boolean值的配置项
     */
    public static boolean getBoolean(String key) {
        // 获取配置值
        String value = get(key);
        if (StringUtils.isNotBlank(value)) {
            return BobanValidatorUtil.isTrue(value);
        }
        return false;
    }

}
