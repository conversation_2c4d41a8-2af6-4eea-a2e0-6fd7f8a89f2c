package com.bobandata.cloud.web.util;

import com.bobandata.cloud.common.utils.BobanStrUtil;
import com.bobandata.cloud.common.utils.json.JsonUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * Map相关工具类
 */
@Slf4j
public class MapUtils {

    /**
     * 忽略key大小写，兼容多库等场景
     *
     * @param map
     * @param key
     * @param <T>
     * @return
     */
    public static <T> T getIgnoreCase(Map<String, T> map, String key) {
        if (map == null) {
            return null;
        }
        if (map.containsKey(key)) {
            return map.get(key);
        }
        if (BobanStrUtil.isAllUpperCase(key)) {
            return map.get(key.toLowerCase());
        }
        return map.get(key.toUpperCase());
    }

    /**
     * 忽略key大小写，兼容多库等场景
     *
     * @param map
     * @param key
     * @param <T>
     * @return
     */
    public static <T> T getIgnoreCase(Map<String, T> map, String key, T defaultVal) {
        if (map == null) {
            return defaultVal;
        }
        return getIgnoreCase(map, key);
    }

    /**
     * 构建ResultMap为实体
     *
     * @param dataMap
     * @param entityClass
     * @param <T>
     * @return
     */
    public static <T> T buildEntity(Map<String, Object> dataMap, Class<T> entityClass) {
        // 字段映射
        if (BobanValidatorUtil.isEmpty(dataMap)) {
            return null;
        }
        return JsonUtils.parseObject(JsonUtils.toJsonString(dataMap), entityClass);
    }

    /**
     * 构建ResultMap为实体列表
     *
     * @param resultListMap
     * @param entityClass
     * @param <T>
     * @return
     */
    public static <T> List<T> buildEntityList(List<Map<String, Object>> resultListMap, Class<T> entityClass) {
        if (BobanValidatorUtil.isEmpty(resultListMap)) {
            return Collections.emptyList();
        }
        return JsonUtils.parseArray(JsonUtils.toJsonString(resultListMap), entityClass);
    }

}
