package com.bobandata.cloud.web.config.web;

import com.bobandata.cloud.web.util.WebFrameworkUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2023-11-10日 11:03
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(TradeWebProperties.class)
@ConditionalOnProperty(prefix = "trade.web", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TradeWebAutoConfiguration {

    private final TradeWebProperties tradeWebProperties;

    public TradeWebAutoConfiguration(final TradeWebProperties tradeWebProperties) {
        this.tradeWebProperties = tradeWebProperties;
        WebFrameworkUtils.setRequestLoginUserIdAttribute(tradeWebProperties.getLoginUserKey());
    }

    public TradeWebProperties getTradeWebProperties() {
        return tradeWebProperties;
    }
}
