package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import org.springframework.core.convert.converter.Converter;

/**
 * Timestamp - LocalDateTime 转换器
 */
@CollectThisConvertor
public class Timestamp2LocalDateTimeConverter implements Converter<Timestamp, LocalDateTime> {

    @Override
    public LocalDateTime convert(Timestamp source) {
        if (source == null) {
            return null;
        }
        return source.toLocalDateTime();
    }
}
