package com.bobandata.cloud.web.core.converter;

import com.bobandata.cloud.web.annotation.CollectThisConvertor;
import org.springframework.core.convert.converter.Converter;

import java.util.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * Date - LocalDateTime 转换器
 */
@CollectThisConvertor
public class Date2LocalDateTimeConverter implements Converter<Date, LocalDateTime> {

    @Override
    public LocalDateTime convert(Date source) {
        if (source == null) {
            return null;
        }
        ZonedDateTime zonedDateTime = source.toInstant().atZone(ZoneId.systemDefault());
        return zonedDateTime.toLocalDateTime();
    }
}
