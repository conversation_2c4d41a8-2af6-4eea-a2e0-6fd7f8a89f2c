/*
 * Copyright (c) 2015-2021, www.dibo.ltd (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.bobandata.cloud.web.plugin;

/**
 * JsonResult结果过滤器
 */
public interface JsonResultFilter {

    /**
     * 需要全局忽略的字段
     * @return
     */
    <T> void filterData(T data);

}
