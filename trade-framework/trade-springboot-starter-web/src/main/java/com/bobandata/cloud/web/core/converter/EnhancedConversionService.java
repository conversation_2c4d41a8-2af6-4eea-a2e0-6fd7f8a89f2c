package com.bobandata.cloud.web.core.converter;

import cn.hutool.core.util.ArrayUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.DefaultConversionService;

/**
 * 扩展的转换service
 */
@Slf4j
public class EnhancedConversionService extends DefaultConversionService {

    public EnhancedConversionService(List<Converter> converters) {
        super();
        if (ArrayUtil.isNotEmpty(converters)) {
            for (Converter converter : converters) {
                addConverter(converter);
                log.debug("addConverter {}", converter.getClass().getSimpleName());
            }
        }
    }

}
