package com.bobandata.cloud.trade.job.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2023-11-08日 14:14
 * @description
 */
@ConfigurationProperties("trade.xxl.job")
@Validated
@Data
public class XxlJobProperties {

    private static final Integer LOG_RETENTION_DAYS_DEFAULT = 30;

    /**
     * 是否开启，默认为 true 关闭
     */
    private Boolean enabled = true;

    private String adminAddresses;

    private String accessToken;

    private String appName;

    private String address;

    private String ip;

    private int port;

    /**
     * 存储任务日志的地方 当前项目的地址所在的目录
     */
    private String logPath;

    private Integer logRetentionDays = LOG_RETENTION_DAYS_DEFAULT;
}
