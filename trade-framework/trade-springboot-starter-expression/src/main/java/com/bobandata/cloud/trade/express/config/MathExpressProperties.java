package com.bobandata.cloud.trade.express.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2023-11-21日 14:01
 * @description
 */
@ConfigurationProperties("trade.math-express")
@Data
public class MathExpressProperties {

    private boolean enable;

    /**
     * 是否需要高精度计算
     */
    private boolean isPrecise = true;

    /**
     * 是否跟踪执行指令的过程
     */
    private boolean isTrace = false;
}
