package com.bobandata.cloud.trade.express.core.util;

import com.bobandata.cloud.trade.express.core.exception.VariableVerifyException;
import com.google.common.primitives.Doubles;
import com.google.common.primitives.Floats;
import com.google.common.primitives.Ints;
import com.google.common.primitives.Longs;
import com.ql.util.express.OperatorOfNumber;
import com.ql.util.express.exception.QLException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @date 2023-11-22日 09:37
 * @description 数组操作工具类
 */
public final class OperatorOfArray {

    public static final String ARRAY_ADD = "add";

    public static final String ARRAY_SUB = "sub";

    public static final String ARRAY_MUL = "mul";

    public static final String ARRAY_DIV = "div";

    public static boolean isArrayOrCollection(Object var) {
        if (isSupportNumberArray(var)) {
            return true;
        }
        return isSupportCollection(var);
    }

    /**
     * @param var
     * @return 是否集合类型
     */
    public static Boolean isSupportCollection(Object var) {
        if (var instanceof Map) {
            return false;
        }
        if (var instanceof Collection<?>) {
            Collection<?> value = (Collection<?>) var;
            return value.stream().allMatch(val -> val instanceof Number);
        }
        return false;
    }

    public static boolean isSupportNumberArray(Object var) {
        Objects.requireNonNull(var, "变量不能为空");
        if ((var instanceof int[]) || (var instanceof Integer[])) {
            return true;
        }
        if ((var instanceof double[]) || (var instanceof Double[])) {
            return true;
        }
        if ((var instanceof long[]) || (var instanceof Long[])) {
            return true;
        }
        if ((var instanceof float[]) || (var instanceof Float[])) {
            return true;
        }
        return var instanceof BigDecimal[];
    }

    public static List<? extends Number> transObject(Object var) {
        if (isSupportCollection(var)) {
            return transListObject(var);
        }
        return transArrayObject(var);
    }

    /**
     * 数据类型转换
     *
     * @param var
     * @return
     */
    public static List<? extends Number> transListObject(Object var) {
        Objects.requireNonNull(var, "变量不能为空");
        if (!isSupportCollection(var)) {
            throw new VariableVerifyException("变量类型不支持");
        }
        Collection<?> value = (Collection<?>) var;
        List<Number> numbers = new ArrayList<>();
        for (final Object val : value) {
            Number valNumber = (Number) val;
            Number transfer = OperatorOfNumber.transfer(valNumber, BigDecimal.class, true);
            numbers.add(transfer);
        }
        return numbers;
    }

    /**
     * 数组数据类型转换
     *
     * @param var
     * @return
     */
    public static List<? extends Number> transArrayObject(Object var) {
        Objects.requireNonNull(var, "变量不能为空");
        if (!var.getClass().isArray()) {
            throw new VariableVerifyException("变量不是数组类型");
        }
        if (var instanceof int[]) {
            int[] value = (int[]) var;
            return Ints.asList(value);
        }
        if (var instanceof double[]) {
            double[] value = (double[]) var;
            return Doubles.asList(value);
        }
        if (var instanceof long[]) {
            long[] value = (long[]) var;
            return Longs.asList(value);
        }
        if (var instanceof float[]) {
            float[] value = (float[]) var;
            return Floats.asList(value);
        }
        if (var instanceof Integer[]) {
            Integer[] value = (Integer[]) var;
            return Arrays.stream(value).collect(Collectors.toList());
        }
        if (var instanceof Double[]) {
            Double[] value = (Double[]) var;
            return Arrays.stream(value).collect(Collectors.toList());
        }
        if (var instanceof Float[]) {
            Float[] value = (Float[]) var;
            return Arrays.stream(value).collect(Collectors.toList());
        }
        if (var instanceof Long[]) {
            Long[] value = (Long[]) var;
            return Arrays.stream(value).collect(Collectors.toList());
        }
        if (var instanceof BigDecimal[]) {
            BigDecimal[] value = (BigDecimal[]) var;
            return Arrays.stream(value).collect(Collectors.toList());
        }
        throw new VariableVerifyException("不支持此数组类型：" + var.getClass());
    }

    public static Object arrayAdd(Object left, Object right, boolean isPrecise) throws Exception {
        return arrayOperator(left, right, isPrecise, ARRAY_ADD);
    }

    public static Object arraySub(Object left, Object right, boolean isPrecise) throws Exception {
        return arrayOperator(left, right, isPrecise, ARRAY_SUB);
    }

    public static Object arrayMul(Object left, Object right, boolean isPrecise) throws Exception {
        return arrayOperator(left, right, isPrecise, ARRAY_MUL);
    }

    public static Object arrayDiv(Object left, Object right) throws Exception {
        // 除法必须使用高精度 不然会精度丢失
        return arrayOperator(left, right, true, ARRAY_DIV);
    }

    /**
     * 数组类型 加减乘除 面向操作符号使用
     *
     * @param left
     * @param right
     * @param isPrecise    是否高精度
     * @param operatorType 操作类型
     * @return
     * @throws Exception
     */
    public static Object arrayOperator(Object left,
                                       Object right,
                                       boolean isPrecise,
                                       String operatorType) throws Exception {
        SingleOperatorFunction singleOperatorFunction = new SingleOperatorFunction(operatorType, isPrecise);
        if ((left instanceof Number) && (right instanceof Number)) {
            return singleOperatorFunction.apply((Number) left, (Number) right);
        }
        if (isArrayOrCollection(left) && (right instanceof Number)) {
            List<? extends Number> val0 = transObject(left);
            Number val1 = OperatorOfNumber.transfer((Number) right, BigDecimal.class, false);
            ListNumOperationFunction function = new ListNumOperationFunction(singleOperatorFunction);
            return function.apply(val0, val1);
        }
        if ((left instanceof Number) && isArrayOrCollection(right)) {
            Number val0 = OperatorOfNumber.transfer((Number) left, BigDecimal.class, false);
            List<? extends Number> val1 = transObject(right);
            NumListOperationFunction function = new NumListOperationFunction(singleOperatorFunction);
            return function.apply(val0, val1);
        }
        if (isArrayOrCollection(left) && isArrayOrCollection(right)) {
            List<? extends Number> val0 = transObject(left);
            List<? extends Number> val1 = transObject(right);
            if (val0.size() != val1.size()) {
                throw new QLException("操作数组长度不相等");
            }
            ListListOperationFunction function = new ListListOperationFunction(singleOperatorFunction);
            return function.apply(val0, val1);
        }
        throw new VariableVerifyException("不支持此数据类型：" + left.getClass() + " - " + right.getClass());
    }

    public static class SingleOperatorFunction implements BiFunction<Number, Number, Object> {

        private final String operator;

        private final boolean isPrecise;

        public SingleOperatorFunction(final String operator, final boolean isPrecise) {
            this.operator = operator;
            this.isPrecise = isPrecise;
        }

        @SneakyThrows
        @Override
        public Object apply(final Number number, final Number number2) {
            switch (operator) {
                case ARRAY_ADD:
                    return OperatorOfNumber.add(number, number2, isPrecise);
                case ARRAY_SUB:
                    return OperatorOfNumber.subtract(number, number2, isPrecise);
                case ARRAY_MUL:
                    return OperatorOfNumber.multiply(number, number2, isPrecise);
                case ARRAY_DIV:
                    return OperatorOfNumber.divide(number, number2, isPrecise);
                default:
                    throw new VariableVerifyException("不支持操作类型：" + operator);
            }
        }
    }

    public static class ListNumOperationFunction implements BiFunction<List<? extends Number>, Number, Object> {

        private final SingleOperatorFunction function;

        public ListNumOperationFunction(SingleOperatorFunction function) {
            this.function = function;
        }

        @Override
        public Object apply(final List<? extends Number> numbers, final Number number) {
            List<Object> result = new ArrayList<>(numbers.size());
            for (final Number val : numbers) {
                Object apply = function.apply(val, number);
                result.add(apply);
            }
            return result;
        }
    }

    public static class NumListOperationFunction implements BiFunction<Number, List<? extends Number>, Object> {

        private final SingleOperatorFunction function;

        public NumListOperationFunction(final SingleOperatorFunction function) {
            this.function = function;
        }

        @Override
        public Object apply(final Number number, final List<? extends Number> numbers) {
            List<Object> result = new ArrayList<>(numbers.size());
            for (final Number val : numbers) {
                Object apply = function.apply(number, val);
                result.add(apply);
            }
            return result;
        }
    }

    public static class ListListOperationFunction implements BiFunction<List<? extends Number>, List<? extends Number>, Object> {

        private final SingleOperatorFunction function;

        public ListListOperationFunction(final SingleOperatorFunction function) {
            this.function = function;
        }

        @Override
        public Object apply(final List<? extends Number> numbers,
                            final List<? extends Number> numbers2) {
            List<Object> result = new ArrayList<>(numbers.size());
            for (int i = 0; i < numbers.size(); i++) {
                Number number0 = numbers.get(i);
                Number number1 = numbers2.get(i);
                Object apply = this.function.apply(number0, number1);
                result.add(apply);
            }
            return result;
        }
    }
}
