package com.bobandata.cloud.trade.express.core.operator;

import com.bobandata.cloud.trade.express.core.constant.OperatorLevel;
import com.bobandata.cloud.trade.express.core.util.OperatorOfArray;

import static com.bobandata.cloud.trade.express.core.util.OperatorOfArray.ARRAY_ADD;
import static com.bobandata.cloud.trade.express.core.util.OperatorOfArray.ARRAY_DIV;
import static com.bobandata.cloud.trade.express.core.util.OperatorOfArray.ARRAY_MUL;
import static com.bobandata.cloud.trade.express.core.util.OperatorOfArray.ARRAY_SUB;

/**
 * <AUTHOR>
 * @date 2023-11-28日 16:15
 * @description 数组/集合类型加减乘除操作符号
 */
public class ArrayOperatorSymbolFactory {

    public static class ArrayAddOperatorSymbol extends AbstractOperatorSymbol {

        public ArrayAddOperatorSymbol() {
            super(ARRAY_ADD);
        }

        @Override
        protected Object execute(final Object[] variables) throws Exception {
            Object left = variables[0];
            Object right = variables[1];
            return OperatorOfArray.arrayAdd(left, right, this.isPrecise());
        }

        @Override
        public OperatorLevel operatorLevel() {
            return OperatorLevel.ADD;
        }
    }

    public static class ArraySubOperatorSymbol extends AbstractOperatorSymbol {

        public ArraySubOperatorSymbol() {
            super(ARRAY_SUB);
        }

        @Override
        protected Object execute(final Object[] variables) throws Exception {
            Object left = variables[0];
            Object right = variables[1];
            return OperatorOfArray.arraySub(left, right, this.isPrecise());
        }

        @Override
        public OperatorLevel operatorLevel() {
            return OperatorLevel.ADD;
        }
    }

    public static class ArrayMulOperatorSymbol extends AbstractOperatorSymbol {

        public ArrayMulOperatorSymbol() {
            super(ARRAY_MUL);
        }

        @Override
        protected Object execute(final Object[] variables) throws Exception {
            Object left = variables[0];
            Object right = variables[1];
            return OperatorOfArray.arrayMul(left, right, this.isPrecise());
        }

        @Override
        public OperatorLevel operatorLevel() {
            return OperatorLevel.MUL;
        }
    }

    public static class ArrayDivOperatorSymbol extends AbstractOperatorSymbol {

        public ArrayDivOperatorSymbol() {
            super(ARRAY_DIV);
        }

        @Override
        protected Object execute(final Object[] variables) throws Exception {
            Object left = variables[0];
            Object right = variables[1];
            return OperatorOfArray.arrayDiv(left, right);
        }

        @Override
        public OperatorLevel operatorLevel() {
            return OperatorLevel.MUL;
        }
    }
}
