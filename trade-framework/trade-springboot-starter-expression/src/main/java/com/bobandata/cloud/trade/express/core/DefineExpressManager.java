package com.bobandata.cloud.trade.express.core;

import com.bobandata.cloud.trade.express.core.exception.SymbolDefineException;
import java.util.ArrayList;
import java.util.List;
import java.util.ServiceLoader;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-11-21日 14:29
 * @description 自定义符号/函数 加载管理器
 */
@Slf4j
public class DefineExpressManager {

    private List<Symbol> defineSymbols;

    @PostConstruct
    public void loadDefineExpresses() {
        this.defineSymbols = new ArrayList<>();
        this.loadExpress();
        this.verifySymbols();
    }

    public List<Symbol> getDefineSymbols() {
        return defineSymbols;
    }

    private void verifySymbols() {
        long realCount = this.defineSymbols.stream().map(Symbol::symbolKey).distinct().count();
        if (realCount != defineSymbols.size()) {
            throw new SymbolDefineException("存在重复自定义符号");
        }
    }

    private void loadExpress() {
        ServiceLoader<Symbol> serviceLoader = ServiceLoader.load(Symbol.class);
        serviceLoader.forEach(symbol -> this.defineSymbols.add(symbol));
        log.info("--------------加载自定义数学符号---------------");
        if (log.isDebugEnabled()) {
            for (final Symbol defineSymbol : this.defineSymbols) {
                String symbolKey = defineSymbol.symbolKey();
                log.debug("{} : {}", symbolKey, defineSymbol.getClass());
            }
        }

    }
}
