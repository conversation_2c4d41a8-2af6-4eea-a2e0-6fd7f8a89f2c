package com.bobandata.cloud.trade.express.core.operator;

import com.bobandata.cloud.trade.express.core.AbstractOperator;
import com.bobandata.cloud.trade.express.core.util.OperatorOfArray;
import com.ql.util.express.exception.QLException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-11-21日 15:08
 * @description
 */
public abstract class AbstractOperatorSymbol extends AbstractOperator implements OperatorSymbol {

    protected AbstractOperatorSymbol(final String name) {
        super(name);
    }

    @Override
    protected boolean verifyVariables(final Object[] variables) throws Exception {
        Object left = variables[0];
        Object right = variables[1];
        boolean support = this.isSupport(left, right);
        if (!support) {
            throw new QLException("操作数异常");
        }
        return super.verifyVariables(variables);
    }

    protected boolean isSupport(Object left, Object right) {
        Objects.requireNonNull(left, "变量数据不能为空");
        Objects.requireNonNull(right, "变量数据不能为空");
        List<? extends Number> leftNums = OperatorOfArray.transObject(left);
        List<? extends Number> rightNums = OperatorOfArray.transObject(right);
        return rightNums.size() == leftNums.size();
    }
}
