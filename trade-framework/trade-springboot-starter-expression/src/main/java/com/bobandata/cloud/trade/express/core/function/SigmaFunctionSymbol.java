package com.bobandata.cloud.trade.express.core.function;

import com.bobandata.cloud.trade.express.core.util.OperatorOfArray;
import com.ql.util.express.OperatorOfNumber;
import com.ql.util.express.exception.QLException;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-21日 09:13
 * @description ∑ 求和函数
 */
public class SigmaFunctionSymbol extends AbstractFunctionSymbol {

    private static final String SIGMA_SIGN = "sigma";

    public SigmaFunctionSymbol() {
        super(SIGMA_SIGN);
    }

    @Override
    protected Object execute(final Object[] variables) throws Exception {
        Object var = variables[0];
        if (var instanceof Collection) {
            Collection<?> collection = (Collection<?>) var;
            Object result = 0;
            for (final Object o : collection) {
                result = OperatorOfNumber.add(result, o, this.isPrecise());
            }
            return result;
        }
        if (var.getClass().isArray()) {
            List<? extends Number> numbers = OperatorOfArray.transArrayObject(var);
            Object result = 0;
            for (final Number number : numbers) {
                result = OperatorOfNumber.add(result, number, this.isPrecise());
            }
            return result;
        }
        throw new QLException("操作变量数据类型异常");
    }

    @Override
    protected boolean verifyVariables(final Object[] variables) throws Exception {
        if (variables.length != 1) {
            throw new QLException("操作数异常");
        }
        Object var = variables[0];
        if (!var.getClass().isArray() && !(var instanceof Collection)) {
            throw new QLException("操作变量数据类型异常");
        }
        return true;
    }
}
