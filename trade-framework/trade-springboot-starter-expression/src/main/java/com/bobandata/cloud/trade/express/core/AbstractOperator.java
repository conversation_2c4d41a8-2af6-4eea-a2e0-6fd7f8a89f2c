package com.bobandata.cloud.trade.express.core;

import com.bobandata.cloud.trade.express.core.exception.VariableVerifyException;
import com.ql.util.express.Operator;

/**
 * <AUTHOR>
 * @date 2023-11-20日 16:11
 * @description function 和 operator的抽象量类
 */
public abstract class AbstractOperator extends Operator {

    protected AbstractOperator(String name) {
        this.name = name;
    }

    public Operator getOperator() {
        return this;
    }

    public String symbolKey() {
        return this.getName();
    }

    @Override
    public Object executeInner(final Object[] variables) throws Exception {
        try {
            boolean verifyVariables = verifyVariables(variables);
            if (!verifyVariables) {
                throw new VariableVerifyException("参数校验失败");
            }
        } catch (Exception e) {
            throw new VariableVerifyException(e);
        }
        return execute(variables);
    }

    protected abstract Object execute(Object[] variables) throws Exception;

    protected boolean verifyVariables(Object[] variables) throws Exception {
        return true;
    }
}
