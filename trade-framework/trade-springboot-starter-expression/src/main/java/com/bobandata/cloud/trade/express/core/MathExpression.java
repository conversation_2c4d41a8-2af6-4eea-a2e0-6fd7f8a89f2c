package com.bobandata.cloud.trade.express.core;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023-11-20日 16:26
 * @description 数学公式定义部分 默认返回值类型为 ${@link BigDecimal}
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@EqualsAndHashCode
@Builder
public class MathExpression implements Serializable {

    /**
     * 公式名称
     */
    private String name;

    /**
     * 公式内容
     */
    private String express;

    /**
     * 返回值名称
     */
    private String returnName;



    public static class ExpressVar {

        private String varName;

        private String varType;
    }
}
