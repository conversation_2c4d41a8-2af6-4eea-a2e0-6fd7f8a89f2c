package com.bobandata.cloud.trade.express.config;

import com.bobandata.cloud.trade.express.core.DefineExpressManager;
import com.bobandata.cloud.trade.express.core.service.MathExpressFrameworkService;
import com.bobandata.cloud.trade.express.core.service.MathExpressFrameworkServiceImpl;
import com.ql.util.express.ExpressRunner;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2023-11-21日 14:01
 * @description
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "trade.math-express", value = "enable", matchIfMissing = true)
@EnableConfigurationProperties(MathExpressProperties.class)
public class MathExpressAutoConfiguration {

    @Bean
    public DefineExpressManager expressManager() {
        return new DefineExpressManager();
    }

    @Bean
    public MathExpressFrameworkService mathExpressService(MathExpressProperties expressProperties,
                                                          DefineExpressManager expressManager) {
        ExpressRunner expressRunner = new ExpressRunner(expressProperties.isPrecise(), expressProperties.isTrace());
        return new MathExpressFrameworkServiceImpl(expressProperties, expressManager, expressRunner);
    }
}
