package com.bobandata.cloud.trade.express.core.service;

import com.bobandata.cloud.trade.express.config.MathExpressProperties;
import com.bobandata.cloud.trade.express.core.DefineExpressManager;
import com.bobandata.cloud.trade.express.core.MathExpression;
import com.bobandata.cloud.trade.express.core.MathVariable;
import com.bobandata.cloud.trade.express.core.Symbol;
import com.bobandata.cloud.trade.express.core.function.FunctionSymbol;
import com.bobandata.cloud.trade.express.core.operator.OperatorSymbol;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.ql.util.express.Operator;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023-11-21日 13:56
 * @description
 */
@Slf4j
public class MathExpressFrameworkServiceImpl implements MathExpressFrameworkService {

    private final MathExpressProperties expressProperties;

    private final DefineExpressManager expressManager;

    private final ExpressRunner expressRunner;

    public MathExpressFrameworkServiceImpl(final MathExpressProperties expressProperties,
                                           final DefineExpressManager expressManager,
                                           final ExpressRunner expressRunner) {
        this.expressProperties = expressProperties;
        this.expressManager = expressManager;
        this.expressRunner = expressRunner;
    }

    @PostConstruct
    private void initExpressRunner() throws Exception {
        List<Symbol> defineSymbols = this.expressManager.getDefineSymbols();
        for (final Symbol defineSymbol : defineSymbols) {
            Operator operator = defineSymbol.getOperator();
            String symbolKey = defineSymbol.symbolKey();
            if (defineSymbol instanceof FunctionSymbol) {
                this.expressRunner.addFunction(symbolKey, operator);
            } else if (defineSymbol instanceof OperatorSymbol) {
                OperatorSymbol operatorSymbol = (OperatorSymbol) defineSymbol;
                this.expressRunner.addOperator(symbolKey, operatorSymbol.operatorLevel().getLevel(), operator);
            }
        }
    }

    public Object runExpression(MathExpression expression,
                                List<MathVariable> variables,
                                boolean isPrecise,
                                boolean isTrace) throws Exception {
        ExpressRunner expressRunner = new ExpressRunner(isPrecise, isTrace);
        DefaultContext<String, Object> defaultContext = new DefaultContext<>();
        for (final MathVariable variable : variables) {
            defaultContext.put(variable.getName(), variable.getValue());
        }
        String express = expression.getExpress();
        return expressRunner.execute(express, defaultContext, null, false, false);
    }

}
