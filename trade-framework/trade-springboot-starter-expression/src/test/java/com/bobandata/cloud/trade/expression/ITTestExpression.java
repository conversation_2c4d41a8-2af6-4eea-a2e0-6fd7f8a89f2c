package com.bobandata.cloud.trade.expression;

import com.bobandata.cloud.trade.express.core.DefineExpressManager;
import com.bobandata.cloud.trade.express.core.Symbol;
import com.bobandata.cloud.trade.express.core.function.FunctionSymbol;
import com.bobandata.cloud.trade.express.core.operator.OperatorSymbol;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.ql.util.express.Operator;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023-11-08日 16:11
 * @description
 */
public class ITTestExpression {

    private ExpressRunner expressRunner;

    @Before
    public void before() throws Exception {
        expressRunner = new ExpressRunner();
        DefineExpressManager expressManager = new DefineExpressManager();
        expressManager.loadDefineExpresses();
        List<Symbol> defineSymbols = expressManager.getDefineSymbols();
        for (final Symbol defineSymbol : defineSymbols) {
            Operator operator = defineSymbol.getOperator();
            String symbolKey = defineSymbol.symbolKey();
            if (defineSymbol instanceof FunctionSymbol) {
                this.expressRunner.addFunction(symbolKey, operator);
            } else if (defineSymbol instanceof OperatorSymbol) {
                OperatorSymbol operatorSymbol = (OperatorSymbol) defineSymbol;
                this.expressRunner.addOperator(symbolKey, operatorSymbol.operatorLevel().getLevel(), operator);
            }
        }
    }

    @Test
    public void testSimple() throws Exception {
        DefaultContext<String, Object> context = new DefaultContext<String, Object>();
        String express = "a add b sub c div d mul 10";
        context.put("a", new BigDecimal("1"));
        context.put("b", new BigDecimal("1"));
        context.put("c", new BigDecimal("1"));
        context.put("d", new BigDecimal("2"));
        Object execute = expressRunner.execute(express, context, null, false, false);
        System.out.println("execute = " + execute);
    }

    @Test
    public void testSigma() throws Exception {
        DefaultContext<String, Object> context = new DefaultContext<String, Object>();
        String express = "sigma(q mul (p1 sub p2))";
        context.put("q", Arrays.asList(new BigDecimal("5"), new BigDecimal("2")));
        context.put("p1", Arrays.asList(new BigDecimal("3"), new BigDecimal("1")));
        context.put("p2", Arrays.asList(new BigDecimal("1"), new BigDecimal("2")));
        Object execute = expressRunner.execute(express, context, null, false, false);
        System.out.println("execute = " + execute);
    }

}
