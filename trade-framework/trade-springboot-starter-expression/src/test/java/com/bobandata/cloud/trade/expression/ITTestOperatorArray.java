package com.bobandata.cloud.trade.expression;

import cn.hutool.core.util.RandomUtil;
import com.bobandata.cloud.common.utils.array.RandomUtils;
import com.bobandata.cloud.trade.express.core.util.OperatorOfArray;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023-11-27日 09:33
 * @description
 */
public class ITTestOperatorArray {

    private static final Integer ARR_LEN = 8;

    private int[] intsArr;

    private long[] longsArr;

    private double[] doublesArr;

    private float[] floatsArr;

    private BigDecimal[] bigDecimalsArr;

    @Before
    public void initTestArr() {
        intsArr = RandomUtil.randomInts(ARR_LEN);
        bigDecimalsArr = RandomUtils.randomBigDecimals(ARR_LEN);
        System.out.println("intsArr = " + Arrays.toString(intsArr));
        System.out.println("bigDecimalsArr = " + Arrays.toString(bigDecimalsArr));
    }

    @Test
    public void testArrayAdd() throws Exception {
        Object arrayAdd = OperatorOfArray.arrayAdd(intsArr, bigDecimalsArr, false);
        System.out.println("arrayAdd = " + arrayAdd);
        Object arrayAdd1 = OperatorOfArray.arrayAdd(intsArr, RandomUtil.randomDouble(0, 10), false);
        System.out.println("arrayAdd1 = " + arrayAdd1);
        Object arrayAdd2 = OperatorOfArray.arrayAdd(RandomUtil.randomLong(0, 10), intsArr, false);
        System.out.println("arrayAdd2 = " + arrayAdd2);
        Object arrayAdd3 = OperatorOfArray.arrayAdd(RandomUtil.randomLong(0, 10), RandomUtil.randomDouble(0, 10), true);
        System.out.println("arrayAdd3 = " + arrayAdd3);
    }

    @Test
    public void testArrayDiv() throws Exception {
        Object arrayAdd = OperatorOfArray.arrayDiv(intsArr, bigDecimalsArr);
        System.out.println("arrayAdd = " + arrayAdd);
        Object arrayAdd1 = OperatorOfArray.arrayDiv(intsArr, 2.3);
        System.out.println("arrayAdd1 = " + arrayAdd1);
        Object arrayAdd2 = OperatorOfArray.arrayDiv(1.0, intsArr);
        System.out.println("arrayAdd2 = " + arrayAdd2);
        Object arrayAdd3 = OperatorOfArray.arrayDiv(RandomUtil.randomLong(0, 10), RandomUtil.randomDouble(0, 10));
        System.out.println("arrayAdd3 = " + arrayAdd3);
    }

    @Test
    public void testTransArray() throws Exception {
        Long[] arr = new Long[] {
                1L,
                2L
        };
        List<? extends Number> numbers = OperatorOfArray.transArrayObject(arr);
        System.out.println("numbers = " + numbers);
    }
}
