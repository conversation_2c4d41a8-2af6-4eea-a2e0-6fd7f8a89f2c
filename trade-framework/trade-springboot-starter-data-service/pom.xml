<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bobandata.cloud</groupId>
        <artifactId>trade-framework</artifactId>
        <version>${re.version}</version>
    </parent>

    <artifactId>trade-springboot-starter-data-service</artifactId>
    <name>trade : manager :: framework ::: dataService</name>

    <dependencies>
        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-framework-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bobandata.cloud</groupId>
            <artifactId>trade-springboot-starter-web</artifactId>
        </dependency>
    </dependencies>
</project>