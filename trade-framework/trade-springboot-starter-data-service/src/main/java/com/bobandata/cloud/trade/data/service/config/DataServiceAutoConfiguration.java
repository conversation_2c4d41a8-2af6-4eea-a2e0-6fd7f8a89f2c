package com.bobandata.cloud.trade.data.service.config;

import com.bobandata.cloud.trade.data.service.core.DataServiceSQLInitialize;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2024-01-15日 16:41
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(DataServiceProperties.class)
@ConditionalOnProperty(prefix = "trade.data-service", value = "enable", matchIfMissing = true)
public class DataServiceAutoConfiguration {

    private final DataServiceProperties dataServiceProperties;

    public DataServiceAutoConfiguration(DataServiceProperties dataServiceProperties) {
        this.dataServiceProperties = dataServiceProperties;
    }

    @Bean
    public DataServiceSQLInitialize initialize(DataServiceProperties dataServiceProperties) {
        return new DataServiceSQLInitialize(dataServiceProperties);
    }
}
