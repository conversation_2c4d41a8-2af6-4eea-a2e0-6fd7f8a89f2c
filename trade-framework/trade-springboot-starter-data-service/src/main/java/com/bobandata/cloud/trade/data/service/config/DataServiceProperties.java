package com.bobandata.cloud.trade.data.service.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2024-01-15日 16:41
 * @description 对应数据服务 最简单版本 t_dg_service_dev
 */
@ConfigurationProperties("trade.data-service")
@Data
@Validated
public class DataServiceProperties {

    private Boolean enable = false;

    private List<ApiInfo> apiInfos;

    @Data
    public static class ApiInfo {

        /**
         * 对应数据服务配置文件的ID
         */
        private Integer dataBaseId;

        /**
         * 数据服务名称
         */
        private String name;

        /**
         * 数据服务接口路径
         */
        private String serviceName;

        /**
         * 数据服务接口版本
         */
        private String serviceVersion;

        /**
         * 请求类型
         */
        private String requestType;

        /**
         * 数据查询 SQL
         */
        private String serviceSQL;

    }
}
