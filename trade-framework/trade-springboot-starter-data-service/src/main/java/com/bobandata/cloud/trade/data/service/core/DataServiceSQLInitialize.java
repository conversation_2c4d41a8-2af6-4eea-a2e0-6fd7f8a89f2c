package com.bobandata.cloud.trade.data.service.core;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.trade.data.service.config.DataServiceProperties;
import com.bobandata.cloud.web.util.ApplicationContextHolder;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.List;
import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

/**
 * <AUTHOR>
 * @date 2024-01-15日 16:55
 * @description
 */
public class DataServiceSQLInitialize implements ApplicationRunner {

    private static final String INSERT_SQL = "insert into t_dg_service_dev (NAME, SERVICE_NAME, SERVICE_VERSION, SERVICE_CLASSES, REQUEST_TYPE, RESPONSE_TYPE,\n" +
            "                              PROTOCOL, DB_ID, SERVICE_DESC, DEFINITION, SERVICE_STATUS)\n" +
            "values (?,?,?,?,?,?,?,?,?,?,?)";

    private final DataServiceProperties dataServiceProperties;

    public DataServiceSQLInitialize(DataServiceProperties dataServiceProperties) {
        this.dataServiceProperties = dataServiceProperties;
    }

    @Override
    public void run(final ApplicationArguments args) throws Exception {
        List<DataServiceProperties.ApiInfo> apiInfos = this.dataServiceProperties.getApiInfos();
        if (CollUtil.isEmpty(apiInfos)) {
            return;
        }
        DataSource dataSource = ApplicationContextHolder.getBean(DataSource.class);
        if (Objects.isNull(dataSource)) {
            throw new NullPointerException("初始化数据服务报错：数据链接池获取失败!");
        }
        Connection connection = dataSource.getConnection();
        try (PreparedStatement preparedStatement = connection.prepareStatement(INSERT_SQL)) {
            for (final DataServiceProperties.ApiInfo apiInfo : apiInfos) {

            }
        }
    }
}
