package com.bobandata.cloud.trade.mvc.core.protect;

import com.bobandata.cloud.common.utils.BobanStrUtil;

/**
 * 数据脱敏默认处理器
 */
public class DefaultDataMaskHandler implements DataMaskHandler {

    @Override
    public String mask(String content) {
        if (BobanStrUtil.isBlank(content)) {
            return BobanStrUtil.EMPTY;
        }
        int length = content.length();
        switch (length) {
            case 11:
                // 11位手机号，保留前3位和后4位
                return BobanStrUtil.replace(content, 3, length - 4, '*');
            case 18:
                // 18位身份证号，保留前6位和后4位
                return BobanStrUtil.replace(content, 6, length - 4, '*');
            default:
                // 其他长度，保留前0位和后4位，长度小于5位不脱敏
                return BobanStrUtil.replace(content, 0, length - 4, '*');
        }
    }

}
