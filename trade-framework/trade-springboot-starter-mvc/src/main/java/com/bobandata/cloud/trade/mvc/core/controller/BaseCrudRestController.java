package com.bobandata.cloud.trade.mvc.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.trade.mvc.core.support.BaseCrudMapper;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import com.bobandata.cloud.trade.mvc.core.util.ApplicationPlusContextHolder;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import com.bobandata.cloud.trade.mvc.core.vo.Pagination;
import com.bobandata.cloud.trade.mvc.core.vo.PagingResult;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseCrudRestController<Do extends BaseDo, M extends BaseCrudMapper<Do>> extends BaseController {

    protected Class<Do> entityClass;

    protected ServiceResult<Do> getEntity(Serializable id) {
        return ServiceResult.success(getService().getEntity(id));
    }

    /**
     * 单表查询带分页
     */
    protected PagingResult<Do> getEntityWithPage(Do entity, Pagination pagination) {
        QueryWrapper<Do> wrapper = new QueryWrapper<>(entity);
        Page<Do> doPage = pagination.toPage(entityClass);
        Page<Do> records = getService().page(doPage, wrapper);
        return PagingResult.success(records, pagination);
    }

    protected BaseService<M, Do> getService() {
        return ApplicationPlusContextHolder.getBaseServiceByEntity(getEntityClass());
    }

    protected Class<Do> getEntityClass() {
        if (this.entityClass == null) {
            this.entityClass = BobanBeanUtils.getGenericityClass(this, 0);
            if (this.entityClass == null) {
                log.warn("无法从 {} 类定义中获取泛型类entityClass", this.getClass().getName());
            }
        }
        return this.entityClass;
    }
}