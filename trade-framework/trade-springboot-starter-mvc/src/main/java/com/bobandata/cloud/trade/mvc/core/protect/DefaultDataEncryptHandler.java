package com.bobandata.cloud.trade.mvc.core.protect;

import com.bobandata.cloud.web.util.EncryptorUtil;

/**
 * 加密，解密 处理器的默认实现类
 */
public class DefaultDataEncryptHandler implements DataEncryptHandler {

    protected String getSeed() {
        return EncryptorUtil.getDefaultKey();
    }

    @Override
    public String encrypt(String fieldVal) {
        return EncryptorUtil.encrypt(fieldVal, getSeed());
    }

    @Override
    public String decrypt(String encryptedStr) {
        return EncryptorUtil.decrypt(encryptedStr, getSeed());
    }

}
