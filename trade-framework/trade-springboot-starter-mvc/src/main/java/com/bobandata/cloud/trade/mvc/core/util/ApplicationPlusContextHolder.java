package com.bobandata.cloud.trade.mvc.core.util;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.toolkit.JdbcUtils;
import com.bobandata.cloud.common.exception.InvalidUsageException;
import com.bobandata.cloud.trade.mvc.core.relation.BindingCacheManager;
import com.bobandata.cloud.trade.mvc.core.relation.EntityInfoCache;
import com.bobandata.cloud.trade.mvc.core.relation.PropInfo;
import com.bobandata.cloud.trade.mvc.core.support.BaseService;
import com.bobandata.cloud.web.util.ApplicationContextHolder;
import java.sql.Connection;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;

/**
 * Spring上下文帮助类
 */
//@Component
//@Lazy(false)
public class ApplicationPlusContextHolder extends ApplicationContextHolder {

    private static final Logger log = LoggerFactory.getLogger(ApplicationPlusContextHolder.class);

    /**
     * 数据库类型
     */
    private static String DATABASE_TYPE = null;

    /**
     * 根据Entity获取对应的IService实现
     *
     * @param entity
     * @return
     */
    public static IService getIServiceByEntity(Class entity) {
        EntityInfoCache entityInfoCache = BindingCacheManager.getEntityInfoByClass(entity);
        IService iService = entityInfoCache != null ? entityInfoCache.getService() : null;
        if (iService == null) {
            log.debug("未能识别到Entity: " + entity.getName() + " 的IService实现！");
        }
        return iService;
    }

    /**
     * 根据Entity获取对应的BaseService实现
     *
     * @param entity
     * @return
     */
    public static BaseService getBaseServiceByEntity(Class entity) {
        EntityInfoCache entityInfoCache = BindingCacheManager.getEntityInfoByClass(entity);
        IService iService = entityInfoCache != null ? entityInfoCache.getService() : null;
        if (iService == null) {
            log.debug("未能识别到Entity: " + entity.getName() + " 的Service实现！");
        }
        if (iService instanceof BaseService) {
            return (BaseService) iService;
        }
        log.warn("Entity的service实现类: {} 非BaseService实现！", entityInfoCache.getService());
        return null;
    }

    /**
     * 根据Entity获取对应的BaseMapper实现
     *
     * @param entityClass
     * @return
     */
    public static BaseMapper getBaseMapperByEntity(Class entityClass) {
        BaseMapper mapper = BindingCacheManager.getMapperByClass(entityClass);
        if (mapper == null) {
            throw new InvalidUsageException("未找到 " + entityClass.getName() + " 的Mapper定义！");
        }
        return mapper;
    }

    /**
     * 获取Entity主键列名
     *
     * @return
     */
    public static String getIdColumnName(Class entity) {
        PropInfo propInfoCache = BindingCacheManager.getPropInfoByClass(entity);
        return propInfoCache != null ? propInfoCache.getIdColumn() : null;
    }

    /**
     * 获取Entity主键字段名
     *
     * @return
     */
    public static String getIdFieldName(Class entity) {
        PropInfo propInfoCache = BindingCacheManager.getPropInfoByClass(entity);
        return propInfoCache != null ? propInfoCache.getIdFieldName() : null;
    }

    /**
     * 获取Entity主键
     *
     * @return
     */
    @Deprecated
    public static String getPrimaryKey(Class entity) {
        return getIdFieldName(entity);
    }

    /***
     * 获取JdbcUrl
     * @return
     */
    public static String getJdbcUrl() {
        ApplicationContext applicationContext = getApplicationContext();
        if (applicationContext == null) {
            return null;
        }
        String jdbcUrl = null;
        try {
            DataSource dataSource = applicationContext.getBean(DataSource.class);
            Connection connection = dataSource.getConnection();
            jdbcUrl = connection.getMetaData().getURL();
            connection.close();
            return jdbcUrl;
        } catch (Exception e) {
            log.warn("获取JDBC URL异常: {}", e.getMessage());
        }
        // 候补识别方式，暂时保留
        Environment environment = applicationContext.getEnvironment();
        jdbcUrl = environment.getProperty("spring.datasource.url");
        if (jdbcUrl == null) {
            jdbcUrl = environment.getProperty("spring.datasource.druid.url");
        }
        if (jdbcUrl == null) {
            String master = environment.getProperty("spring.datasource.dynamic.primary");
            if (master != null) {
                jdbcUrl = environment.getProperty("spring.datasource.dynamic.datasource." + master + ".url");
            }
        }
        if (jdbcUrl == null) {
            String names = environment.getProperty("spring.shardingsphere.datasource.names");
            if (names != null) {
                jdbcUrl = environment.getProperty("spring.shardingsphere.datasource." + names.split(",")[0] + ".url");
            }
        }
        return jdbcUrl;
    }

    /**
     * 获取数据库类型
     *
     * @return
     */
    public static String getDatabaseType() {
        if (DATABASE_TYPE != null) {
            return DATABASE_TYPE;
        }
        String jdbcUrl = getJdbcUrl();
        if (jdbcUrl != null) {
            DbType dbType = JdbcUtils.getDbType(jdbcUrl);
            DATABASE_TYPE = dbType.getDb();
            if (DATABASE_TYPE.startsWith(DbType.SQL_SERVER.getDb())) {
                DATABASE_TYPE = DbType.SQL_SERVER.getDb();
            }
        }
        if (DATABASE_TYPE == null) {
            log.warn("无法识别数据库类型，请检查数据源配置:spring.datasource.url等");
        }
        return DATABASE_TYPE;
    }

}
