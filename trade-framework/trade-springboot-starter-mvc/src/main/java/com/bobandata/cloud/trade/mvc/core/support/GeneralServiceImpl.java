package com.bobandata.cloud.trade.mvc.core.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bobandata.cloud.trade.mvc.core.util.ApplicationPlusContextHolder;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import com.bobandata.cloud.trade.mvc.core.vo.Pagination;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.AbstractDo;
import com.bobandata.cloud.web.config.ApplicationBaseConfig;
import com.bobandata.cloud.web.util.BobanValidatorUtil;
import com.github.yulichang.base.MPJBaseServiceImpl;
import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-01-11日 16:45
 * @description 单表 增删改查 封装 create update delete 方法才会有 before after 处理 未修改原始 save update delete 方法
 */
public class GeneralServiceImpl<M extends BaseCrudMapper<DO>, DO extends AbstractDo<ID>, ID extends Serializable> extends MPJBaseServiceImpl<M, DO> implements GeneralService<M, DO, ID> {

    private static final Logger log = LoggerFactory.getLogger(GeneralServiceImpl.class);

    @Override
    public DO getEntity(final Serializable id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return super.getById(id);
    }

    @Override
    public List<DO> getEntityList(final Wrapper<DO> queryWrapper) {
        return this.getEntityList(queryWrapper, null);
    }

    @Override
    public List<DO> getEntityList(final Wrapper<DO> queryWrapper, final Pagination pagination) {
        if (Objects.isNull(pagination)) {
            return this.list(queryWrapper);
        } else {
            Page<DO> doPage = this.page(pagination.toPage(entityClass), queryWrapper);
            List<DO> records = doPage.getRecords();
            if (doPage.searchCount()) {
                pagination.setTotalCount(doPage.getTotal());
            }
            return records;
        }
    }

    @Override
    public List<DO> getEntityListByIds(final List<ID> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return super.listByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean createEntity(final DO entity) {
        if (entity == null) {
            warning("createEntity", "参数entity为null");
            return false;
        }
        this.beforeCreate(entity);
        boolean success = super.save(entity);
        if (success) {
            this.afterCreate(entity);
        }
        return success;
    }

    /**
     * 用于创建之前的自动填充等场景调用
     */
    protected void beforeCreate(DO entity) {
    }

    /**
     * 创建数据的后拦截
     *
     * @param entity
     */
    protected void afterCreate(DO entity) {
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean createEntities(final Collection<DO> entityList) {
        if (BobanValidatorUtil.isEmpty(entityList)) {
            return false;
        }
        this.beforeBatchCreate(entityList);
        boolean success = super.saveBatch(entityList, ApplicationBaseConfig.getBatchSize());
        this.afterBatchCreate(entityList);
        return success;
    }

    protected void afterBatchCreate(final Collection<DO> entityList) {

    }

    protected void beforeBatchCreate(final Collection<DO> entityList) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateEntity(final DO entity) {
        this.beforeUpdate(entity);
        boolean success = super.updateById(entity);
        if (success) {
            this.afterUpdate(entity);
        }
        return success;
    }

    protected void afterUpdate(final DO entity) {

    }

    protected void beforeUpdate(final DO entity) {

    }

    @Override
    public boolean updateEntities(final Collection<DO> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return false;
        }
        for (DO entity : entityList) {
            this.beforeUpdate(entity);
        }
        boolean success = super.updateBatchById(entityList);
        if (success) {
            for (DO entity : entityList) {
                this.afterUpdate(entity);
            }
        }
        return success;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteEntities(final Wrapper<DO> queryWrapper) {
        // 执行查询获取匹配ids
        // 优化SQL，只查询id字段
        if (queryWrapper instanceof QueryWrapper) {
            String idCol = ApplicationPlusContextHolder.getIdColumnName(entityClass);
            ((QueryWrapper<?>) queryWrapper).select(idCol);
        }
        List<DO> entityList = getEntityList(queryWrapper);
        if (CollUtil.isEmpty(entityList)) {
            return false;
        }
        String pkFieldName = ApplicationPlusContextHolder.getIdFieldName(entityClass);
        List entityIds = BobanBeanUtils.collectToList(entityList, pkFieldName);
        this.beforeDelete(pkFieldName, entityIds);
        boolean success = super.removeByIds(entityIds);
        if (success) {
            this.afterDelete(pkFieldName, entityIds);
        }
        return success;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteEntity(final Serializable id) {
        String pk = ApplicationPlusContextHolder.getIdFieldName(entityClass);
        this.beforeDelete(pk, id);
        boolean success = super.removeById(id);
        if (success) {
            this.afterDelete(pk, id);
        }
        return success;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteEntities(final Collection<? extends Serializable> entityIds) {
        if (CollUtil.isEmpty(entityIds)) {
            return false;
        }
        String pk = ApplicationPlusContextHolder.getIdFieldName(entityClass);
        this.beforeDelete(pk, entityIds);
        boolean success = super.removeByIds(entityIds);
        if (success) {
            this.afterDelete(pk, entityIds);
        }
        return success;
    }

    /**
     * 删除数据的前拦截，值可能为单值或集合
     *
     * @param fieldKey
     * @param fieldVal
     */
    protected void beforeDelete(String fieldKey, Object fieldVal) {
    }

    /**
     * 删除数据的后拦截，值可能为单值或集合
     *
     * @param fieldKey
     * @param fieldVal
     */
    protected void afterDelete(String fieldKey, Object fieldVal) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrUpdateEntity(final DO entity) {
        Object id = this.getPrimaryKeyValue(entity);
        if (id == null) {
            return createEntity(entity);
        }
        return updateEntity(entity);
    }

    @Override
    public boolean createOrUpdateEntities(final Collection<DO> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            warning("createOrUpdateEntities", "参数entityList为空!");
            return false;
        }
        // 批量插入
        return super.saveOrUpdateBatch(entityList, ApplicationBaseConfig.getBatchSize());
    }

    /***
     * 打印警告信息
     * @param method
     * @param message
     */
    protected void warning(String method, String message) {
        log.warn(this.getClass().getSimpleName() + ".{} 调用错误: {}, 请检查！", method, message);
    }

    /**
     * 获取主键值
     *
     * @param entity
     * @return
     */
    protected Object getPrimaryKeyValue(Object entity) {
        String pk = ApplicationPlusContextHolder.getIdFieldName(entity.getClass());
        return BobanBeanUtils.getProperty(entity, pk);
    }
}
