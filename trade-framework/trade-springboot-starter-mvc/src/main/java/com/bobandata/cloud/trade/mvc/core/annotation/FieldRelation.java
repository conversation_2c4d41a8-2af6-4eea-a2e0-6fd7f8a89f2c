package com.bobandata.cloud.trade.mvc.core.annotation;

import com.bobandata.cloud.trade.mvc.core.constant.RelationType;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 使用在主表字段上
 */
@Target({
        ElementType.FIELD
})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface FieldRelation {

    /**
     * @return 关联实体类
     */
    Class<?> entity();

    /**
     * @return 关联实体类的字段
     */
    String field();

    /**
     * @return 关联类型
     */
    RelationType relationType() default RelationType.ONE_ONE;

}
