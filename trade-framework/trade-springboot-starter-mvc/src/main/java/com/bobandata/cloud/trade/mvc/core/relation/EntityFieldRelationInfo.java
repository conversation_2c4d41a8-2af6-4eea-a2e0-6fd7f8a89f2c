package com.bobandata.cloud.trade.mvc.core.relation;

import com.bobandata.cloud.trade.mvc.core.constant.RelationType;
import java.lang.reflect.Field;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024-02-29日 14:48
 * @description
 */
@Getter
@Setter
@Builder
@ToString
public class EntityFieldRelationInfo {

    private Field field;

    private Class<?> relationClazz;

    private String relationClazzFieldName;

    private RelationType relationType;
}
