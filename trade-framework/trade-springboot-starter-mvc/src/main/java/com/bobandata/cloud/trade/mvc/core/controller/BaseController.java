package com.bobandata.cloud.trade.mvc.core.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.bobandata.cloud.common.enums.Cons;
import com.bobandata.cloud.common.utils.StrUtils;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;

public class BaseController {

    /***
     * 获取请求参数Map
     * @return
     */
    protected Map<String, Object> getParamsMap(HttpServletRequest request) throws Exception {
        return getParamsMap(request, null);
    }

    /***
     * 获取请求参数Map
     * @return
     */
    private Map<String, Object> getParamsMap(HttpServletRequest request, List<String> paramList) throws Exception {
        Map<String, Object> result = new HashMap<>(8);
        Enumeration paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            // 如果非要找的参数，则跳过
            if (CollUtil.isNotEmpty(paramList) && !paramList.contains(paramName)) {
                continue;
            }
            String[] values = request.getParameterValues(paramName);
            if (ArrayUtil.isNotEmpty(values)) {
                if (values.length == 1) {
                    if (ArrayUtil.isNotEmpty(values[0])) {
                        String paramValue = java.net.URLDecoder.decode(values[0], Cons.CHARSET_UTF8);
                        result.put(paramName, paramValue);
                    }
                } else {
                    String[] valueArray = new String[values.length];
                    for (int i = 0; i < values.length; i++) {
                        valueArray[i] = java.net.URLDecoder.decode(values[i], Cons.CHARSET_UTF8);
                    }
                    // 多个值需传递到后台SQL的in语句
                    result.put(paramName, valueArray);
                }
            }
        }

        return result;
    }

    /***
     * 获取请求URI (去除contextPath)
     * @return
     */
    protected String getRequestMappingURI(HttpServletRequest request) {
        String contextPath = request.getContextPath();
        if (StrUtils.isNotEmpty(contextPath)) {
            return StrUtils.replace(request.getRequestURI(), contextPath, "");
        }
        return request.getRequestURI();
    }

    /**
     * 提取请求参数名集合
     *
     * @return
     */
    protected Set<String> extractQueryParams(HttpServletRequest request) {
        Map<String, Object> paramValueMap = convertParams2Map(request);
        if (CollUtil.isNotEmpty(paramValueMap)) {
            return paramValueMap.keySet();
        }
        return Collections.EMPTY_SET;
    }

    /***
     * 将请求参数值转换为Map
     * @return
     */
    protected Map<String, Object> convertParams2Map(HttpServletRequest request) {
        Map<String, Object> result = new HashMap<>(8);
        if (request == null) {
            return result;
        }
        Enumeration paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            String[] values = request.getParameterValues(paramName);
            if (ArrayUtil.isNotEmpty(values)) {
                if (values.length == 1) {
                    if (ArrayUtil.isNotEmpty(values[0])) {
                        result.put(paramName, values[0]);
                    }
                } else {
                    // 多个值需传递到后台SQL的in语句
                    result.put(paramName, values);
                }
            }
        }
        return result;
    }
}