package com.bobandata.cloud.trade.mvc.core.convert;

import com.bobandata.cloud.common.pojo.label.LabelValue;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.reflect.FieldUtils;

/**
 * <AUTHOR>
 * @date 2024-03-13日 10:02
 * @description class 数据转换为 前端图表类数据
 */
public class LabelConvertor {

    public static <T> ArrayList<LabelValue> convertLabels(List<T> data,
                                                          Field keyField,
                                                          Field valueField,
                                                          Function<Object, String> keyMapper) {
        return data.stream()
                   .map(t -> convertLabel(t, keyField, valueField, keyMapper))
                   .collect(Collectors.toCollection(ArrayList::new));
    }

    public static LabelValue convertLabel(Object data,
                                          Field keyField,
                                          Field valueField,
                                          Function<Object, String> keyMapper) {
        Object keyFieldValue;
        Object value;
        try {
            keyFieldValue = FieldUtils.readField(keyField, data, false);
            value = FieldUtils.readField(valueField, data, false);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        String label = keyMapper.apply(keyFieldValue);
        return new LabelValue().setLabel(label).setValue(value);
    }
}
