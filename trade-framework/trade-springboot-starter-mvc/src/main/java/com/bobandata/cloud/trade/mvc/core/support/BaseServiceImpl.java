package com.bobandata.cloud.trade.mvc.core.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bobandata.cloud.common.utils.SnowflakeUtil;
import com.bobandata.cloud.trade.mvc.core.exception.ServiceNotFoundException;
import com.bobandata.cloud.trade.mvc.core.relation.EntityFieldRelationInfo;
import com.bobandata.cloud.trade.mvc.core.relation.EntityRelationCacheManager;
import com.bobandata.cloud.trade.mvc.core.util.ApplicationPlusContextHolder;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.DeleteJoinWrapper;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024-01-11日 17:02
 * @description 基于 join mapper 生成最新的CRUD
 */
public class BaseServiceImpl<M extends BaseCrudMapper<DO>, DO extends BaseDo> extends GeneralServiceImpl<M, DO, Long> implements BaseService<M, DO> {

    private static final Logger log = LoggerFactory.getLogger(BaseServiceImpl.class);

    /**
     * @param entity
     * @param relatedEntities 多个class
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createEntityWithRelatedEntities(DO entity, List<BaseDo> relatedEntities) {
        boolean success = this.createEntity(entity);
        if (!success) {
            warning("新建Entity失败: {}", entity.toString());
            return false;
        }
        if (CollUtil.isEmpty(relatedEntities)) {
            return true;
        }
        Map<? extends Class<? extends BaseDo>, List<BaseDo>> baseDoMaps = relatedEntities.stream()
                                                                                         .collect(Collectors.groupingBy(
                                                                                                 BaseDo::getClass));
        for (final Map.Entry<? extends Class<? extends BaseDo>, List<BaseDo>> entry : baseDoMaps.entrySet()) {
            Class<? extends BaseDo> mainClazz = entity.getClass();
            Class<? extends BaseDo> relationClazz = entry.getKey();
            EntityFieldRelationInfo relationInfo = EntityRelationCacheManager.extractRelation(mainClazz, relationClazz);
            Objects.requireNonNull(relationInfo, entity.getClass() + "关联关系不能为空");
            // 获取关联属性的值
            Field field = relationInfo.getField();
            Object fieldValue = BobanBeanUtils.getProperty(entity, field.getName());
            if (fieldValue == null) {
                // 不存在的时候重新生成关联ID
                fieldValue = SnowflakeUtil.getId();
            }
            String relationClazzFieldName = relationInfo.getRelationClazzFieldName();
            List<BaseDo> relatedDos = entry.getValue();
            for (final BaseDo relatedEntity : relatedDos) {
                // 填充关联属性的值
                BobanBeanUtils.setProperty(relatedEntity, relationClazzFieldName, fieldValue);
            }
            BaseService relatedEntityService = ApplicationPlusContextHolder.getBaseServiceByEntity(relationClazz);
            if (relatedEntityService == null) {
                throw new ServiceNotFoundException(relationClazz);
            }
            boolean create = relatedEntityService.createEntities(relatedDos);
            if (!create) {
                throw new RuntimeException();
            }
        }
        return true;
    }

    /**
     * @param entity
     * @param relatedEntities 同一个class
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public <RE extends BaseDo> boolean createEntityAndRelatedEntities(DO entity, List<RE> relatedEntities) {
        boolean success = this.createEntity(entity);
        if (!success) {
            warning("新建Entity失败: {}", entity.toString());
            return false;
        }
        if (CollUtil.isEmpty(relatedEntities)) {
            return true;
        }
        RE re = relatedEntities.get(0);
        EntityFieldRelationInfo relationInfo = EntityRelationCacheManager.extractRelation(
                entity.getClass(), re.getClass());
        Objects.requireNonNull(relationInfo, entity.getClass() + "关联关系不能为空");
        // 获取关联属性的值
        Field field = relationInfo.getField();
        Object fieldValue = BobanBeanUtils.getProperty(entity, field.getName());
        if (fieldValue == null) {
            // 不存在的时候重新生成关联ID
            fieldValue = SnowflakeUtil.getId();
        }
        String relationClazzFieldName = relationInfo.getRelationClazzFieldName();
        for (final RE relatedEntity : relatedEntities) {
            // 填充关联属性的值
            BobanBeanUtils.setProperty(relatedEntity, relationClazzFieldName, fieldValue);
        }
        BaseService relatedEntityService = ApplicationPlusContextHolder.getBaseServiceByEntity(re.getClass());
        if (relatedEntityService == null) {
            throw new ServiceNotFoundException(re.getClass());
        }
        return relatedEntityService.createEntities(relatedEntities);
    }

    public void deleteEntityWithRelated(DO entity, List<BaseDo> relatedEntities, boolean deleteAll) {
        if (CollUtil.isEmpty(relatedEntities)) {
            Long id = entity.getId();
            if (id != null) {
                this.removeById(id);
            } else {
                this.remove(new QueryWrapper<>(entity));
            }
        }
        DeleteJoinWrapper<? extends BaseDo> deleteJoinWrapper = JoinWrappers.delete(entity.getClass());
        if (deleteAll) {
            deleteJoinWrapper.deleteAll();
        }
        for (final BaseDo relatedEntity : relatedEntities) {

        }
    }

}
