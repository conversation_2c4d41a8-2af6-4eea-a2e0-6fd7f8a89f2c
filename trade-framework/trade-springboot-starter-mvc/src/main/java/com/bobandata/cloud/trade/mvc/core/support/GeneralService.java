package com.bobandata.cloud.trade.mvc.core.support;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.bobandata.cloud.trade.mvc.core.vo.Pagination;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.AbstractDo;
import com.github.yulichang.base.MPJBaseService;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 通用服务接口Service
 */
public interface GeneralService<M extends BaseCrudMapper<DO>, DO extends AbstractDo<ID>, ID extends Serializable> extends MPJBaseService<DO> {

    DO getEntity(Serializable id);

    /**
     * 获取指定条件的Entity集合
     *
     * @param queryWrapper
     * @return
     * @throws Exception
     */
    List<DO> getEntityList(Wrapper<DO> queryWrapper);

    /**
     * 获取指定条件的Entity集合
     *
     * @param queryWrapper
     * @param pagination
     * @return
     * @throws Exception
     */
    List<DO> getEntityList(Wrapper<DO> queryWrapper, Pagination pagination);

    /**
     * 获取指定条件的Entity集合
     *
     * @param ids
     * @return
     */
    List<DO> getEntityListByIds(List<ID> ids);

    /**
     * 创建Entity实体
     *
     * @param entity
     * @return true:成功, false:失败
     */
    boolean createEntity(DO entity);

    /***
     * 批量创建Entity
     * @param entityList 实体对象列表
     * @return true:成功, false: 失败
     */
    boolean createEntities(Collection<DO> entityList);

    /**
     * 更新Entity实体
     *
     * @param entity
     * @return
     */
    boolean updateEntity(DO entity);

    /**
     * 批量更新entity
     *
     * @param entityList
     * @return
     */
    boolean updateEntities(Collection<DO> entityList);

    /**
     * 按条件删除实体
     *
     * @param queryWrapper
     * @return
     * @throws Exception
     */
    boolean deleteEntities(Wrapper<DO> queryWrapper);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean deleteEntity(Serializable id);

    /**
     * 批量删除指定id的实体
     *
     * @param entityIds
     * @return
     * @throws Exception
     */
    boolean deleteEntities(Collection<? extends Serializable> entityIds);

    /***
     * 创建或更新entity（entity.id存在则新建，否则更新）
     * @param entity
     * @return
     */
    boolean createOrUpdateEntity(DO entity);

    /**
     * 批量创建或更新entity（entity.id存在则新建，否则更新）
     *
     * @param entityList
     * @return
     */
    boolean createOrUpdateEntities(Collection<DO> entityList);

}