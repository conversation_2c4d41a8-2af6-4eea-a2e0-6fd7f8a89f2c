package com.bobandata.cloud.trade.mvc.core.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bobandata.cloud.common.enums.ServiceStatus;
import com.bobandata.cloud.common.pojo.ServiceResult;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * JSON返回结果 分页
 */
@Getter
@Setter
public class PagingResult<T> extends ServiceResult<List<T>> {

    private static final long serialVersionUID = 1002L;

    private Long total;

    private Integer pageNo;

    private Integer pageSize;

    public PagingResult() {
    }

    public PagingResult(List<T> data, Long total) {
        this.data = data;
        this.total = total;
    }

    public PagingResult(Long total) {
        this.data = new ArrayList<>();
        this.total = total;
    }

    public PagingResult(IPage<T> iPage) {
        this.total = iPage.getTotal();
        this.data = iPage.getRecords();
    }

    public static <T> PagingResult<T> success(IPage<T> iPage, Pagination pagination) {
        PagingResult<T> pagingResult = new PagingResult<>(iPage);
        pagingResult.setPageNo(pagination.getPageNo());
        pagingResult.setPageSize(pagination.getPageSize());
        pagingResult.setData(iPage.getRecords());
        pagingResult.setCode(ServiceStatus.SUCCESS.code());
        pagingResult.setMsg(ServiceStatus.SUCCESS.label());
        return pagingResult;
    }

    public static <T> PagingResult<T> successWrap(PagingResult<T> data) {
        data.success = true;
        data.code = ServiceStatus.SUCCESS.code();
        data.msg = ServiceStatus.SUCCESS.label();
        return data;
    }

    public static <T> PagingResult<T> empty() {
        return new PagingResult<>(0L);
    }

    public static <T> PagingResult<T> empty(Long total) {
        return new PagingResult<>(total);
    }
}