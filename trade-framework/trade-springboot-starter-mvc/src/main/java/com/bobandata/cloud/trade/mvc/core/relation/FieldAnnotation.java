package com.bobandata.cloud.trade.mvc.core.relation;

import java.lang.annotation.Annotation;
import lombok.Getter;

/**
 * 字段名与注解的包装对象关系 <br>
 */
@Getter
public class FieldAnnotation {
    /**
     * 字段名
     */
    private final String fieldName;
    /**
     * 字段类型
     */
    private final Class<?> fieldClass;
    /**
     * 注解
     */
    private final Annotation annotation;

    public FieldAnnotation(String fieldName, Class<?> fieldClass, Annotation annotation) {
        this.fieldName = fieldName;
        this.fieldClass = fieldClass;
        this.annotation = annotation;
    }

}