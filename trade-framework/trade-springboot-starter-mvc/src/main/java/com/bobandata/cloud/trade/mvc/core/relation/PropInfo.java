package com.bobandata.cloud.trade.mvc.core.relation;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.bobandata.cloud.common.utils.BobanStrUtil;
import com.bobandata.cloud.trade.mvc.core.annotation.FieldRelation;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.bobandata.cloud.web.config.ApplicationBaseConfig;
import com.bobandata.cloud.web.util.BobanValidatorUtil;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;

/**
 * Bean相关信息缓存
 */
@Getter
public class PropInfo implements Serializable {

    private static final long serialVersionUID = 5921667308129991326L;

    /**
     * 主键列
     */
    private String idColumn;
    /**
     * 主键字段类型
     */
    private Class<?> idFieldType;
    /**
     * 逻辑删除列
     */
    private String deletedColumn;
    /**
     * 列集合
     */
    private final List<String> columns = new ArrayList<>();
    /**
     * 字段-列的映射
     */
    private final Map<String, String> fieldToColumnMap = new HashMap<>();
    /**
     * 列-字段的映射
     */
    private final Map<String, String> columnToFieldMap = new HashMap<>();
    /**
     * 列-字段类型的映射
     */
    private final Map<String, Class> columnToFieldTypeMap = new HashMap<>();
    /**
     * 自动更新字段列表
     */
    private final List<String> fillUpdateFieldList = new ArrayList<>();

    /**
     * 关联字段描述信息
     */
    private final List<EntityFieldRelationInfo> entityFieldRelationInfos = new ArrayList<>();

    /**
     * 初始化
     *
     * @param beanClass
     */
    public PropInfo(Class<?> beanClass) {
        this(beanClass, true);
    }

    /**
     * 初始化
     *
     * @param beanClass
     * @param isEntityClass 是否为entity实体类（数据库表对应实体）
     */
    public PropInfo(Class<?> beanClass, boolean isEntityClass) {
        List<Field> fields = BobanBeanUtils.extractAllFields(beanClass, true);
        if (BobanValidatorUtil.notEmpty(fields)) {
            for (Field fld : fields) {
                String fldName = fld.getName();
                String columnName = null;
                FieldRelation fieldRelation = fld.getAnnotation(FieldRelation.class);
                if (fieldRelation != null) {
                    String field = fieldRelation.field();
                    EntityFieldRelationInfo relationInfo = EntityFieldRelationInfo.builder()
                                                                                  .field(fld)
                                                                                  .relationClazz(fieldRelation.entity())
                                                                                  .relationType(
                                                                                          fieldRelation.relationType())
                                                                                  .relationClazzFieldName(field)
                                                                                  .build();
                    this.entityFieldRelationInfos.add(relationInfo);
                }
                TableField tableField = fld.getAnnotation(TableField.class);
                if (tableField != null && tableField.exist()) {
                    if (BobanValidatorUtil.notEmpty(tableField.value())) {
                        columnName = tableField.value();
                    } else {
                        columnName = BobanStrUtil.toSnakeCase(fldName);
                    }
                    FieldFill fill = tableField.fill();
                    if (FieldFill.UPDATE.equals(fill) || FieldFill.INSERT_UPDATE.equals(fill)) {
                        fillUpdateFieldList.add(fldName);
                    }
                }
                // 主键
                TableId tableId = fld.getAnnotation(TableId.class);
                if (tableId != null && this.idColumn == null) {
                    if (BobanValidatorUtil.notEmpty(tableId.value())) {
                        columnName = tableId.value();
                    } else if (columnName == null) {
                        columnName = BobanStrUtil.toSnakeCase(fldName);
                    }
                    this.idColumn = columnName;
                    if (beanClass.isAssignableFrom(BaseDo.class)) {
                        this.idFieldType = Long.class;
                    } else {
                        this.idFieldType = fld.getType();
                    }
                } else {
                    TableLogic tableLogic = fld.getAnnotation(TableLogic.class);
                    if (tableLogic != null) {
                        if (columnName == null) {
                            columnName = BobanStrUtil.toSnakeCase(fldName);
                        }
                        this.deletedColumn = columnName;
                        if (BobanValidatorUtil.notEmpty(tableLogic.value())) {
                            ApplicationBaseConfig.setActiveFlagValue(tableLogic.value());
                        }
                    }
                }
                // 实体类基于默认规则提取列名
                if (columnName == null && isEntityClass) {
                    columnName = BobanStrUtil.toSnakeCase(fldName);
                }
                this.fieldToColumnMap.put(fldName, columnName);
                if (BobanValidatorUtil.notEmpty(columnName)) {
                    this.columnToFieldMap.put(columnName, fldName);
                    if (columnName.equals(this.idColumn) && beanClass.isAssignableFrom(
                            BaseDo.class)) {
                        this.columnToFieldTypeMap.put(columnName, Long.class);
                    } else {
                        this.columnToFieldTypeMap.put(columnName, fld.getType());
                    }
                    this.columns.add(columnName);
                }
            }
        }

    }

    /**
     * 根据列名获取字段
     *
     * @return
     */
    public String getFieldByColumn(String columnName) {
        if (BobanValidatorUtil.isEmpty(this.columnToFieldMap)) {
            return null;
        }
        return this.columnToFieldMap.get(columnName);
    }

    /**
     * 根据列名获取字段
     *
     * @return
     */
    public String getColumnByField(String fieldName) {
        if (BobanValidatorUtil.isEmpty(this.fieldToColumnMap)) {
            return null;
        }
        return this.fieldToColumnMap.get(fieldName);
    }

    /**
     * 是否包含字段
     *
     * @return
     */
    public boolean containsField(String fieldName) {
        if (BobanValidatorUtil.isEmpty(this.fieldToColumnMap)) {
            return false;
        }
        return this.fieldToColumnMap.containsKey(fieldName);
    }

    /**
     * 获取主键属性名
     *
     * @return
     */
    public String getIdFieldName() {
        if (BobanValidatorUtil.isEmpty(this.columnToFieldMap)) {
            return null;
        }
        return this.columnToFieldMap.get(idColumn);
    }

    /**
     * 根据列名获取字段类型
     *
     * @return
     */
    public Class<?> getFieldTypeByColumn(String columnName) {
        if (BobanValidatorUtil.isEmpty(this.columnToFieldTypeMap)) {
            return null;
        }
        return this.columnToFieldTypeMap.get(columnName);
    }

    /**
     * 根据clazz获取字段依赖关系
     *
     * @return
     */
    public Optional<EntityFieldRelationInfo> getRelationByClazz(Class<?> relationClazz) {
        for (final EntityFieldRelationInfo relationInfo : this.entityFieldRelationInfos) {
            Class<?> currentRelationClazz = relationInfo.getRelationClazz();
            if (currentRelationClazz.equals(relationClazz)) {
                return Optional.of(relationInfo);
            }
        }
        return Optional.empty();
    }
}
