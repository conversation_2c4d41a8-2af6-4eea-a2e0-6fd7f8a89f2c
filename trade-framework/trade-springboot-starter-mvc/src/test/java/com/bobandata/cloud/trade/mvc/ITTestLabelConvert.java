package com.bobandata.cloud.trade.mvc;

import cn.hutool.json.JSONUtil;
import com.bobandata.cloud.common.pojo.KeyValueVo;
import com.bobandata.cloud.common.pojo.label.LabelValue;
import com.bobandata.cloud.trade.mvc.core.util.BobanBeanUtils;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import lombok.ToString;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-02-29日 15:59
 * @description
 */
public class ITTestLabelConvert {

    @Test
    public void testBeanUtils() {
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("id", 1);
        TestClass testClass = BobanBeanUtils.convertMap2Bean(hashMap, TestClass.class);
        System.out.println("testClass = " + testClass);
    }

    @ToString
    public static class TestClass {
        private Integer id;
        private String data;
        private Date date;
        private Timestamp timestamp;
    }

    @Test
    public void testLabelJson() {
        KeyValueVo labelValueListVo = new KeyValueVo();
        for (int i = 0; i < 10; i++) {
            LabelValue labelValue = new LabelValue().setLabel("test-" + i).setValue(i);
            labelValueListVo.putLabelValue(labelValue);
        }
        String jsonStr = JSONUtil.toJsonStr(labelValueListVo);
        System.out.println("jsonStr = " + jsonStr);
    }
}
