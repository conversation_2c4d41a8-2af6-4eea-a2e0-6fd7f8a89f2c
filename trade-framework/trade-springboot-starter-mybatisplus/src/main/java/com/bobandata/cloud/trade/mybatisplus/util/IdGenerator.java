package com.bobandata.cloud.trade.mybatisplus.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.bobandata.cloud.common.utils.BobanStrUtil;

/**
 * Snowflake ID生成器控件
 */
public class IdGenerator {

    /***
     * 生成下一个id
     * @return
     */
    public static synchronized long nextId() {
        //暂调用IdWorker保证序列一致
        return IdWorker.getId();
    }

    /**
     * 生成String类型的雪花id
     *
     * @return
     */
    public static synchronized String nextIdStr() {
        return IdWorker.getIdStr();
    }

    /**
     * 生成String类型的UUID
     *
     * @return
     */
    public static String newUUID() {
        return BobanStrUtil.newUuid();
    }

}