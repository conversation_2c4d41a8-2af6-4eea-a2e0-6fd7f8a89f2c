package com.bobandata.cloud.trade.mybatisplus.core.dataobject;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bobandata.cloud.common.core.deserialize.TimestampDeserialize;
import com.bobandata.cloud.common.utils.json.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.sql.Timestamp;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Entity基础父类
 */
@Getter
@Setter
@Accessors
@ToString
public class BaseDo extends AbstractDo<Long> {

    private static final long serialVersionUID = 10203L;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp createTime;

    /**
     * 最后修改时间
     */
    @TableField(value = "last_refresh_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = TimestampDeserialize.class)
    private Timestamp lastRefreshTime;
    /**
     * 最后修改人
     */
    @TableField("last_modifier_id")
    private Long lastModifierId;

    /***
     * Entity对象转为map
     * @return
     */
    public Map<String, Object> toMap() {
        String jsonStr = JsonUtils.toJsonString(this);
        return JsonUtils.toMap(jsonStr);
    }

    @Override
    public Long getPrimaryKeyVal() {
        return super.getId();
    }

}