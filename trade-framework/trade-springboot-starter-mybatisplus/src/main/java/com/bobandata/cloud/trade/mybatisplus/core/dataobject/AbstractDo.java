package com.bobandata.cloud.trade.mybatisplus.core.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;

/**
 * Entity抽象父类
 */
public abstract class AbstractDo<ID extends Serializable> implements Serializable {
    private static final long serialVersionUID = 10202L;

    /**
     * 默认主键字段id，类型为String型雪花id
     */
    @TableId(type = IdType.ASSIGN_ID)
    protected ID id;

    public AbstractDo<ID> setId(ID id) {
        this.id = id;
        return this;
    }

    public ID getId() {
        return this.id;
    }

    /**
     * 获取主键值
     *
     * @return
     */
    @JsonIgnore
    public Object getPrimaryKeyVal() {
        return this.id;
    }

    /**
     * Entity对象转为String
     *
     * @return
     */
    @Override
    public String toString() {
        return this.getClass().getName() + ":" + this.getId();
    }
}
