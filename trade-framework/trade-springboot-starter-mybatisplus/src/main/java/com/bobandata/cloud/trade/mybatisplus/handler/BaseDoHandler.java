package com.bobandata.cloud.trade.mybatisplus.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.bobandata.cloud.trade.mybatisplus.core.dataobject.BaseDo;
import com.bobandata.cloud.web.util.WebFrameworkUtils;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;
import org.apache.ibatis.reflection.MetaObject;

/**
 * <AUTHOR>
 * @date 2022-11-11日 15:02
 * @description
 */
public class BaseDoHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDo) {
            BaseDo baseDO = (BaseDo) metaObject.getOriginalObject();

            LocalDateTime current = LocalDateTime.now();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(Timestamp.valueOf(current));
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getLastRefreshTime())) {
                baseDO.setLastRefreshTime(Timestamp.valueOf(current));
            }

            Long userId = WebFrameworkUtils.getLoginUserId();
            // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getCreatorId())) {
                baseDO.setCreatorId(userId);
            }
            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            if (Objects.nonNull(userId) && Objects.isNull(baseDO.getId())) {
                baseDO.setLastModifierId(userId);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("lastRefreshTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("lastRefreshTime", Timestamp.valueOf(LocalDateTime.now()), metaObject);
        }

        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        Object modifier = getFieldValByName("lastModifierId", metaObject);
        Long userId = WebFrameworkUtils.getLoginUserId();
        if (Objects.nonNull(userId) && Objects.isNull(modifier)) {
            setFieldValByName("lastModifierId", userId.toString(), metaObject);
        }
    }
}
