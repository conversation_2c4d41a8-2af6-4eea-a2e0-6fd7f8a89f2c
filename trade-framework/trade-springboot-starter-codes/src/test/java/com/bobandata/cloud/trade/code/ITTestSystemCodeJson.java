package com.bobandata.cloud.trade.code;

import com.bobandata.cloud.trade.code.core.json.SystemCodeSerialize;
import com.bobandata.cloud.trade.code.core.json.trans.SystemCodeMapperEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2023-11-16日 14:39
 * @description
 */
public class ITTestSystemCodeJson {

    @Test
    public void testSerialize() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        TestJson testJson = new TestJson();
        testJson.setCode("null");
        String s = objectMapper.writeValueAsString(testJson);
        System.out.println("s = " + s);
    }

    @Getter
    @Setter
    private static class TestJson {

        @SystemCodeSerialize(dictKey = "test", codeMapper = SystemCodeMapperEnum.ONE_BY_ONE_KEY)
        private String code;
    }
}
