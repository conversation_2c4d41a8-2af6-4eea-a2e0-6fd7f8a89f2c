package com.bobandata.cloud.trade.code.config;

import com.bobandata.cloud.trade.code.core.loader.SystemCodeLoader;
import com.bobandata.cloud.trade.code.core.loader.SystemCodeLoaderImpl;
import com.bobandata.cloud.trade.system.api.code.SystemCodeApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2023-11-10日 14:41
 * @description
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "trade.system-code", value = "enable", matchIfMissing = true)
@EnableConfigurationProperties(SystemCodeProperties.class)
@EnableScheduling
public class SystemCodeAutoConfiguration {

    @Bean
    public SystemCodeLoader systemCodeLoader(SystemCodeApi systemCodeApi) {
        return new SystemCodeLoaderImpl(systemCodeApi);
    }
}
