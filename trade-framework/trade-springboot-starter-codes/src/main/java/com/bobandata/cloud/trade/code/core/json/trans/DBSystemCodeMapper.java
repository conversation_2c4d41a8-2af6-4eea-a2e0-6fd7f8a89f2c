package com.bobandata.cloud.trade.code.core.json.trans;

import com.bobandata.cloud.trade.code.core.util.SystemCodeUtil;

/**
 * <AUTHOR>
 * @date 2023-11-15日 14:50
 * @description 数据库字段编码表 映射关系
 */
public class DBSystemCodeMapper implements SystemCodeMapper {

    public static final String CODE_KEY_SPLIT = "#";

    @Override
    public String getMapperValue(final String codeKey, final String value) {
        String codeKeyV = codeKey + CODE_KEY_SPLIT + value;
        return SystemCodeUtil.getLabelValue(codeKeyV);
    }
}
