package com.bobandata.cloud.trade.code.core.json;

import com.bobandata.cloud.trade.code.core.json.trans.SystemCodeMapper;
import com.bobandata.cloud.trade.code.core.json.trans.SystemCodeMapperEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import java.io.IOException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023-11-10日 16:48
 * @description
 */
@Slf4j
public class SystemCodeTransHandler extends JsonSerializer<String> implements ContextualSerializer {

    private SystemCodeSerialize codeAnnotation;

    @Override
    public void serialize(final String value,
                          final JsonGenerator jsonGenerator,
                          final SerializerProvider serializerProvider) throws IOException {
        if (Objects.isNull(value)) {
            jsonGenerator.writeNull();
            return;
        }
        if (!StringUtils.isEmpty(value)) {
            String dictKey = codeAnnotation.dictKey();
            SystemCodeMapperEnum systemCodeMapperEnum = codeAnnotation.codeMapper();
            SystemCodeMapper systemCodeMapper = systemCodeMapperEnum.getSystemCodeMapper();
            String mapperValue = systemCodeMapper.getMapperValue(dictKey, value);
            if (mapperValue == null) {
                log.warn("{} {} 未找到对应系统编码的值", dictKey, value);
                jsonGenerator.writeString(value);
                return;
            }
            jsonGenerator.writeObject(mapperValue);
        }
    }

    @Override
    public JsonSerializer<?> createContextual(final SerializerProvider prov,
                                              final BeanProperty property) throws JsonMappingException {
        if (Objects.isNull(property)) {
            return prov.findNullValueSerializer(null);
        }
        //校验当时bean是否为String
        if (Objects.equals(property.getType().getRawClass(), String.class)) {
            SystemCodeSerialize systemCodeSerialize = property.getAnnotation(SystemCodeSerialize.class);
            if (Objects.isNull(systemCodeSerialize)) {
                systemCodeSerialize = property.getContextAnnotation(SystemCodeSerialize.class);
            }
            if (Objects.nonNull(systemCodeSerialize)) {
                this.codeAnnotation = systemCodeSerialize;
                return this;
            }
        }
        return prov.findValueSerializer(property.getType(), property);
    }
}
