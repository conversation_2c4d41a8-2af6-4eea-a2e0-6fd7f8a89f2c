package com.bobandata.cloud.trade.code.core.json;

import com.bobandata.cloud.trade.code.core.json.trans.DBSystemCodeMapper;
import com.bobandata.cloud.trade.code.core.json.trans.SystemCodeMapperEnum;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({
        ElementType.FIELD,
        ElementType.METHOD
})
@Documented
//让jackson的注解拦截器（com.fasterxml.jackson.databind.introspect.JacksonAnnotationIntrospector）能发现当前注解
@JacksonAnnotationsInside
//指定当前注解修饰的属性/方法使用具体哪个序列化类来序列化
@JsonSerialize(using = SystemCodeTransHandler.class)
public @interface SystemCodeSerialize {

    /**
     * 是数据库类型的{@link DBSystemCodeMapper} 时 key 为表名+列名 用#分割
     */
    String dictKey();

    SystemCodeMapperEnum codeMapper();
}
