package com.bobandata.cloud.trade.code.config;

import com.bobandata.cloud.trade.system.api.code.SystemCodeApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date 2023-11-20日 11:16
 * @description 注入SystemCodeApi
 */
@AutoConfiguration
@EnableFeignClients(clients = SystemCodeApi.class)
public class SystemCodeRpcAutoConfiguration {
}
