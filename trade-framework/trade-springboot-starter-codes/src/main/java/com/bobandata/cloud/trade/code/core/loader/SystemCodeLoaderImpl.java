package com.bobandata.cloud.trade.code.core.loader;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.common.pojo.ServiceResult;
import com.bobandata.cloud.common.utils.date.DateUtils;
import com.bobandata.cloud.trade.system.api.code.SystemCodeApi;
import com.bobandata.cloud.trade.system.api.code.SystemCodeRespDto;
import java.sql.Timestamp;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * <AUTHOR>
 * @date 2023-11-10日 14:58
 * @description 基于数据库管理的 系统编码加载器
 */
@Slf4j
public class SystemCodeLoaderImpl implements SystemCodeLoader {
    /**
     * 刷新的频率，单位：毫秒
     */
    private static final int REFRESH_SYSTEM_CODE_PERIOD = 60 * 60 * 1000;

    /**
     * 编码数据量大的情况下，缓存编码的最大更新时间，用于后续的增量轮询，判断是否有更新
     */
    private Timestamp maxUpdateTime;

    private final SystemCodeApi systemCodeApi;

    public SystemCodeLoaderImpl(SystemCodeApi systemCodeApi) {
        this.systemCodeApi = systemCodeApi;
    }

    @Scheduled(fixedDelay = REFRESH_SYSTEM_CODE_PERIOD, initialDelay = REFRESH_SYSTEM_CODE_PERIOD)
    public void refreshSystemCode() {
        this.loadSystemCodes();
    }

    /**
     * 加载系统编码
     */
    public void loadSystemCodes() {
        ServiceResult<List<SystemCodeRespDto>> systemCodeList = systemCodeApi.getSystemCodeList(maxUpdateTime);
        List<SystemCodeRespDto> codeRespDtos = systemCodeList.checkedData();
        if (CollUtil.isEmpty(codeRespDtos)) {
            return;
        }
        log.info("[loadSystemCodes][加载到 ({}) 个错误码]", codeRespDtos.size());

        codeRespDtos.forEach(systemCodeRespDto -> {
            this.putSystemCode(systemCodeRespDto.getDictKey(), systemCodeRespDto.getDictLabel());
            this.maxUpdateTime = DateUtils.max(systemCodeRespDto.getLastUpdateTime(), maxUpdateTime);
        });
    }
}
