package com.bobandata.cloud.trade.code.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2023-11-10日 14:48
 * @description
 */
@ConfigurationProperties("trade.system-dict")
@Data
@Validated
public class SystemCodeProperties {

    /**
     * 是否开启
     */
    private Boolean enable = false;
}
