package com.bobandata.cloud.trade.code.core.util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2023-11-10日 14:54
 * @description
 */
public class SystemCodeUtil {

    private static final ConcurrentMap<String, String> SYSTEM_CODES = new ConcurrentHashMap<>();

    public static void putAll(Map<String, String> messages) {
        SystemCodeUtil.SYSTEM_CODES.putAll(messages);
    }

    public static void put(String dictKey, String label) {
        SystemCodeUtil.SYSTEM_CODES.put(dictKey, label);
    }

    public static String getLabelValue(String dictKey) {
        return SystemCodeUtil.SYSTEM_CODES.get(dictKey);
    }
}
