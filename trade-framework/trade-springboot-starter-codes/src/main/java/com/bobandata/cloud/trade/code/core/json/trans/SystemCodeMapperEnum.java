package com.bobandata.cloud.trade.code.core.json.trans;

import lombok.Getter;

@Getter
public enum SystemCodeMapperEnum {

    ONE_BY_ONE_KEY("一对一映射", new OneKeySystemCodeMapper()),
    DB_CODE_KEY("数据库编码映射", new DBSystemCodeMapper());

    private final String name;

    private final SystemCodeMapper systemCodeMapper;

    SystemCodeMapperEnum(final String name, final SystemCodeMapper systemCodeMapper) {
        this.name = name;
        this.systemCodeMapper = systemCodeMapper;
    }
}
