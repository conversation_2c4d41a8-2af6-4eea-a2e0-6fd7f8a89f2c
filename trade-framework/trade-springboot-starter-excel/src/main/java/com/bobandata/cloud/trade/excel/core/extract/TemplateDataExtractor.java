package com.bobandata.cloud.trade.excel.core.extract;

import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.bobandata.cloud.trade.excel.core.metadata.TableRowData;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateExcelInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateHeaderCell;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateTableInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-03-14日 15:33
 * @description 根据Excel模版提取数据
 */
@Slf4j
public class TemplateDataExtractor implements DataExtractor<TableRowData> {

    private final TemplateExcelInfo templateExcelInfo;

    private TemplateTableInfo currentTable;

    public TemplateDataExtractor(TemplateExcelInfo templateExcelInfo) {
        this.templateExcelInfo = templateExcelInfo;
    }

    @Override
    public String name() {
        return null;
    }

    @Override
    public TableRowData extractData(final ReadRowHolder readRowHolder, final Map<Integer, Object> excelRowData) {
        Integer rowIndex = readRowHolder.getRowIndex();
        TemplateTableInfo templateTableInfo = templateExcelInfo.indexHeaderRow(rowIndex);
        if (Objects.nonNull(templateTableInfo)) {
            this.handleHeadMap(rowIndex, excelRowData);
            this.currentTable = templateTableInfo;
            return null;
        } else if (templateExcelInfo.isDataRow(rowIndex, currentTable)) {
            List<Object> dataList = new ArrayList<>();
            for (final Map.Entry<Integer, Object> entry : excelRowData.entrySet()) {
                Integer colIndex = entry.getKey();
                if (templateExcelInfo.needDataByHeader(colIndex, currentTable)) {
                    Object data = entry.getValue();
                    dataList.add(data);
                }
            }
            Row row = new Row(dataList.size());
            for (int i = 0; i < dataList.size(); i++) {
                row.setField(i, dataList.get(i));
            }
            return this.buildTableRowData(row, rowIndex);
        } else {
            log.debug("无效行 行号 {} 数据 {}", rowIndex, excelRowData);
            return null;
        }
    }

    private TableRowData buildTableRowData(Row data, Integer rowIndex) {
        String[] fields = this.currentTable.getHeaderRow()
                                           .stream()
                                           .map(TemplateHeaderCell::getField)
                                           .toArray(String[]::new);
        String tableName = this.currentTable.getTableName();
        return new TableRowData().setTableName(tableName).setData(data).setFields(fields).setRowIndex(rowIndex);
    }

    private void handleHeadMap(Integer rowIndex, final Map<Integer, Object> excelData) {
        for (final Map.Entry<Integer, Object> entry : excelData.entrySet()) {
            if (Objects.isNull(entry)) {
                continue;
            }
            Integer colIndex = entry.getKey();
            TemplateHeaderCell headerCell = templateExcelInfo.indexHeadCell(rowIndex, colIndex);
            if (Objects.isNull(headerCell)) {
                continue;
            }
            Object cellData = entry.getValue();
            headerCell.setValue(String.valueOf(cellData));
        }
    }

}
