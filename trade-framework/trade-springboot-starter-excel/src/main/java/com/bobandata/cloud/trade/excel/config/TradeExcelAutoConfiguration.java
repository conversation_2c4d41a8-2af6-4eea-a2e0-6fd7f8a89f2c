package com.bobandata.cloud.trade.excel.config;

import cn.hutool.core.collection.CollUtil;
import com.bobandata.cloud.trade.excel.core.service.ExcelReadFactoryService;
import com.bobandata.cloud.trade.excel.core.service.ExcelReadFactoryServiceImp;
import java.util.List;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2024-03-01日 15:28
 * @description
 */
@AutoConfiguration
@EnableConfigurationProperties(TradeExcelProperties.class)
public class TradeExcelAutoConfiguration {

    private final TradeExcelProperties excelProperties;

    public TradeExcelAutoConfiguration(TradeExcelProperties excelProperties) {
        this.excelProperties = excelProperties;
    }

    @Bean
    public ExcelReadFactoryService excelReadFactoryService() {
        return new ExcelReadFactoryServiceImp();
    }

    public TradeExcelProperties.DataSourceConfig getDataSourceConfig(Integer dataSourceId) {
        List<TradeExcelProperties.DataSourceConfig> otherDataSource = excelProperties.getOtherDataSource();
        if (CollUtil.isEmpty(otherDataSource)) {
            return null;
        }
        for (final TradeExcelProperties.DataSourceConfig dataSourceConfig : otherDataSource) {
            if (dataSourceConfig.getId().equals(dataSourceId)) {
                return dataSourceConfig;
            }
        }
        return null;
    }
}
