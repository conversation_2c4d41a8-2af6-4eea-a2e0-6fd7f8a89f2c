package com.bobandata.cloud.trade.excel.core.metadata.template;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-15日 15:55
 * @description
 */
@Getter
@Setter
@Accessors(chain = true)
public class TemplateTableInfo {

    private String tableName;

    private Integer headRowIndex;

    private List<TemplateHeaderCell> headerRow;

    private TemplateDataInfo templateDataInfo;

    public TemplateTableInfo() {
        this.headerRow = new ArrayList<>();
    }

    public void addAllHeaderCell(List<TemplateHeaderCell> headerCells) {
        this.headerRow.addAll(headerCells);
    }

    public String[] getColNames() {
        List<String> fieldNames = new ArrayList<>();
        for (final TemplateHeaderCell headerCell : this.headerRow) {
            if (headerCell.isNeed()) {
                fieldNames.add(headerCell.getField());
            }
        }
        return fieldNames.toArray(new String[0]);
    }

    public void addHeaderCell(TemplateHeaderCell headerCell) {
        this.headerRow.add(headerCell);
    }
}
