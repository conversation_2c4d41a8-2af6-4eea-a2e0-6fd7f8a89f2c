package com.bobandata.cloud.trade.excel.core.metadata.template;

import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-11日 15:56
 * @description
 */
@Getter
@Setter
@EqualsAndHashCode
@Accessors(chain = true)
public class TemplateHeaderCell implements Serializable {

    private Integer rowIndex;

    private Integer colIndex;

    private String orgStr;

    /**
     * 自定义的表头表达式
     */
    private String expression;

    /**
     * 不需要此表头的数据 表达式中提取
     */
    private boolean need;

    /**
     * 从表达式中提取出来的表名
     */
    private String tableName;

    /**
     * 存放映射到数据库的字段信息 表达式中提取
     */
    private String field;

    /**
     * 表格中读取到的值
     */
    private String value;
}
