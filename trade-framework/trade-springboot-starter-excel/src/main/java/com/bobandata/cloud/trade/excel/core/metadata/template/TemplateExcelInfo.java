package com.bobandata.cloud.trade.excel.core.metadata.template;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.exception.ExcelAnalysisException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024-03-11日 15:53
 * @description
 */
@Getter
@Setter
public class TemplateExcelInfo {

    /**
     * 模版名称 可支持正则表达式
     */
    private String templateName;

    private Map<String, TemplateTableInfo> tableInfos = new HashMap<>();

    public void putDataInfo(String tableName, TemplateDataInfo dataInfo) {
        TemplateTableInfo templateTableInfo = tableInfos.get(tableName);
        if (templateTableInfo == null) {
            throw new ExcelAnalysisException("解析Excel模版数据信息出错: 表头信息未初始化");
        }
        templateTableInfo.setTemplateDataInfo(dataInfo);
    }

    public void putTableHeadCell(String tableName, Integer rowIndex, List<TemplateHeaderCell> templateHeaderCell) {
        TemplateTableInfo templateTableInfo = tableInfos.get(tableName);
        if (templateTableInfo == null) {
            templateTableInfo = new TemplateTableInfo()
                    .setTableName(tableName)
                    .setHeadRowIndex(rowIndex);
            this.tableInfos.put(tableName, templateTableInfo);
        }
        templateTableInfo.addAllHeaderCell(templateHeaderCell);
    }

    /**
     * 根据表头所在列判断当前数据列是否有效
     *
     * @return
     */
    public boolean needDataByHeader(Integer colIndex, TemplateTableInfo currentTableInfo) {
        List<TemplateHeaderCell> headerRow = currentTableInfo.getHeaderRow();
        for (final TemplateHeaderCell headerCell : headerRow) {
            if (Objects.equals(headerCell.getColIndex(), colIndex)) {
                return headerCell.isNeed();
            }
        }
        return false;
    }

    /**
     * 根据坐标检索表头信息
     */
    public TemplateHeaderCell indexHeadCell(Integer rowIndex, Integer colIndex) {
        int index = CollUtil.indexOf(
                this.tableInfos.values(),
                templateTableInfo -> templateTableInfo.getHeadRowIndex().equals(rowIndex)
        );
        if (index == -1) {
            return null;
        }
        TemplateTableInfo templateTableInfo = CollUtil.get(tableInfos.values(), index);
        List<TemplateHeaderCell> headerRow = templateTableInfo.getHeaderRow();
        for (final TemplateHeaderCell headerCell : headerRow) {
            if (Objects.equals(headerCell.getRowIndex(), rowIndex) && Objects.equals(
                    headerCell.getColIndex(), colIndex)) {
                return headerCell;
            }
        }
        return null;
    }

    public TemplateTableInfo indexHeaderRow(final Integer rowIndex) {
        int index = CollUtil.indexOf(
                this.tableInfos.values(),
                templateTableInfo -> templateTableInfo.getHeadRowIndex().equals(rowIndex)
        );
        if (index == -1) {
            return null;
        }
        return CollUtil.get(tableInfos.values(), index);
    }

    public boolean isDataRow(final Integer rowIndex, TemplateTableInfo currentTableInfo) {
        if (currentTableInfo == null) {
            return false;
        }
        TemplateDataInfo templateDataInfo = currentTableInfo.getTemplateDataInfo();
        int dataStartRowIndex = templateDataInfo.getDataStartRowIndex();
        int dataEndRowIndex = templateDataInfo.getDataEndRowIndex();
        return rowIndex + 1 >= dataStartRowIndex  && rowIndex + 1 <= dataEndRowIndex;
    }
}
