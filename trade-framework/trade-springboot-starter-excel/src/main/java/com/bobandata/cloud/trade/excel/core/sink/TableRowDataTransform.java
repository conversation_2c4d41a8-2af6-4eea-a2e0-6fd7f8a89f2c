package com.bobandata.cloud.trade.excel.core.sink;

import cn.hutool.core.util.ArrayUtil;
import com.bobandata.cloud.trade.excel.core.metadata.TableRowData;
import java.util.function.Function;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-03-15日 14:23
 * @description
 */
public class TableRowDataTransform implements Function<TableRowData, Row> {

    private final Object[] fixedData;

    public TableRowDataTransform(Object[] fixedData) {
        this.fixedData = fixedData;
    }

    @Override
    public Row apply(final TableRowData row) {
        Row data = row.getData();
        if (ArrayUtil.isNotEmpty(fixedData)) {
            Row fixedRow = new Row(fixedData.length);
            for (int i = 0; i < fixedData.length; i++) {
                fixedRow.setField(i, fixedData[i]);
            }
            return Row.join(data, fixedRow);
        }
        return data;
    }
}
