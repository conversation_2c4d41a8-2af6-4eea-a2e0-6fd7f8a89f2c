package com.bobandata.cloud.trade.excel.core.sink;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateTableInfo;
import com.bobandata.cloud.trade.flink.jdbc.TableMetaInfo;
import java.util.List;
import org.apache.flink.connector.jdbc.dialect.JdbcDialect;

/**
 * <AUTHOR>
 * @date 2024-03-15日 14:01
 * @description
 */
public class TableSchemaInfo {

    private final TemplateTableInfo templateTableInfo;

    private final TableMetaInfo tableMetaInfo;

    private final List<KeyValue<String, Object>> fixedData;

    public TableSchemaInfo(TemplateTableInfo templateTableInfo,
                           TableMetaInfo tableMetaInfo,
                           List<KeyValue<String, Object>> fixedData) {
        this.templateTableInfo = templateTableInfo;
        this.tableMetaInfo = tableMetaInfo;
        this.fixedData = fixedData;
    }

    public Object[] getFixedData() {
        if (fixedData == null) {
            return new Object[0];
        }
        return this.fixedData.stream().map(KeyValue::getValue).toArray();
    }

    public int[] getFieldTypes() {
        String[] colNames = getFinalCols();
        return tableMetaInfo.indexFieldsType(colNames);
    }

    private String[] getFinalCols() {
        String[] colNames = templateTableInfo.getColNames();
        if (CollUtil.isNotEmpty(fixedData)) {
            String[] otherArray = this.fixedData.stream().map(KeyValue::getKey).toArray(String[]::new);
            colNames = ArrayUtil.addAll(colNames, otherArray);
        }
        return colNames;
    }

    public String getInsertSql(JdbcDialect jdbcDialect) {
        String[] finalCols = getFinalCols();
        return tableMetaInfo.getExecuteInsertSQL(jdbcDialect, finalCols);
    }
}
