package com.bobandata.cloud.trade.excel.core;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.bobandata.cloud.trade.excel.core.extract.DataExtractor;
import com.bobandata.cloud.trade.excel.core.sink.TableDataSinker;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-11日 14:17
 * @description
 */
public class SchemaExcelReadListener<T> implements ReadListener<Map<Integer, Object>> {

    private final DataExtractor<T> dataExtractor;

    private final TableDataSinker<T> tableDataSinker;

    public SchemaExcelReadListener(DataExtractor<T> dataExtractor, TableDataSinker<T> tableDataSinker) {
        this.dataExtractor = dataExtractor;
        this.tableDataSinker = tableDataSinker;
        try {
            this.tableDataSinker.initSinker();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void invoke(final Map<Integer, Object> rowData, final AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        T extractData = this.dataExtractor.extractData(readRowHolder, rowData);
        if (extractData == null) {
            return;
        }
        try {
            tableDataSinker.addDataToBatch(extractData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void doAfterAllAnalysed(final AnalysisContext context) {
        try {
            tableDataSinker.commitBatchData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
