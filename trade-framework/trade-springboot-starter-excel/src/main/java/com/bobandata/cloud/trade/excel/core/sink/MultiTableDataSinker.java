package com.bobandata.cloud.trade.excel.core.sink;

import cn.hutool.core.collection.CollectionUtil;
import com.bobandata.cloud.trade.excel.core.metadata.TableRowData;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;

/**
 * <AUTHOR>
 * @date 2024-03-15日 15:16
 * @description 目前仅仅适用于多张表 并且每张表 数据量少的情况
 */
public class MultiTableDataSinker implements TableDataSinker<TableRowData> {

    private final JdbcConnectionProvider jdbcConnectionProvider;

    private final Map<String, JdbcBatchStatementExecutor<TableRowData>> statementExecutors;

    private Function<String, JdbcBatchStatementExecutor<TableRowData>> executorGetter;

    public MultiTableDataSinker(JdbcConnectionProvider jdbcConnectionProvider,
                                Map<String, JdbcBatchStatementExecutor<TableRowData>> statementExecutors) {
        this.jdbcConnectionProvider = jdbcConnectionProvider;
        this.statementExecutors = statementExecutors;
    }

    public MultiTableDataSinker(JdbcConnectionProvider jdbcConnectionProvider,
                                Function<String, JdbcBatchStatementExecutor<TableRowData>> executorGetter) {
        this.jdbcConnectionProvider = jdbcConnectionProvider;
        this.statementExecutors = new HashMap<>();
        this.executorGetter = executorGetter;
    }

    public MultiTableDataSinker(JdbcConnectionProvider jdbcConnectionProvider,
                                Map<String, JdbcBatchStatementExecutor<TableRowData>> statementExecutors,
                                Function<String, JdbcBatchStatementExecutor<TableRowData>> executorGetter) {
        this.jdbcConnectionProvider = jdbcConnectionProvider;
        this.statementExecutors = statementExecutors;
        this.executorGetter = executorGetter;
    }

    @Override
    public void initSinker() throws Exception {
        if (CollectionUtil.isEmpty(statementExecutors)) {
            return;
        }
        Connection connection = jdbcConnectionProvider.getOrEstablishConnection();
        for (final Map.Entry<String, JdbcBatchStatementExecutor<TableRowData>> entry : statementExecutors.entrySet()) {
            JdbcBatchStatementExecutor<TableRowData> statementExecutor = entry.getValue();
            statementExecutor.prepareStatements(connection);
        }
    }

    private JdbcBatchStatementExecutor<TableRowData> createExecutor(String tableName) throws Exception {
        if (executorGetter == null) {
            throw new UnsupportedOperationException("JdbcBatchStatementExecutor's supplier is null");
        }
        JdbcBatchStatementExecutor<TableRowData> newExecutor = this.executorGetter.apply(tableName);
        Connection connection = jdbcConnectionProvider.getOrEstablishConnection();
        newExecutor.prepareStatements(connection);
        return newExecutor;
    }

    @Override
    public void addDataToBatch(final TableRowData data) throws Exception {
        String tableName = data.getTableName();
        JdbcBatchStatementExecutor<TableRowData> currentExecutor = statementExecutors.get(tableName);
        if (currentExecutor == null) {
            JdbcBatchStatementExecutor<TableRowData> newExecutor = this.createExecutor(tableName);
            statementExecutors.put(tableName, newExecutor);
            currentExecutor = newExecutor;
        }
        currentExecutor.addToBatch(data);
    }

    @Override
    public void commitBatchData() throws Exception {
        if (CollectionUtil.isEmpty(statementExecutors)) {
            return;
        }
        for (final Map.Entry<String, JdbcBatchStatementExecutor<TableRowData>> entry : statementExecutors.entrySet()) {
            JdbcBatchStatementExecutor<TableRowData> value = entry.getValue();
            value.executeBatch();
        }
    }

    @Override
    public void close() throws Exception {
        if (CollectionUtil.isEmpty(statementExecutors)) {
            return;
        }
        for (final Map.Entry<String, JdbcBatchStatementExecutor<TableRowData>> entry : statementExecutors.entrySet()) {
            JdbcBatchStatementExecutor<TableRowData> executor = entry.getValue();
            executor.closeStatements();
        }
        if (this.jdbcConnectionProvider != null) {
            this.jdbcConnectionProvider.closeConnection();
        }
    }
}
