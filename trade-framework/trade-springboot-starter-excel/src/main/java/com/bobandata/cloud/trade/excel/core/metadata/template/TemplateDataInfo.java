package com.bobandata.cloud.trade.excel.core.metadata.template;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-14日 14:46
 * @description 描述Excel表格数据信息
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class TemplateDataInfo {

    private String tableName;

    /**
     * 存储在数据头一列的信息
     */
    private String dataExpression;

    /**
     * 数据开始行 [dataStartRowIndex,dataEndRowIndex]
     */
    private Integer dataStartRowIndex;

    /**
     * 数据结束行
     */
    private Integer dataEndRowIndex;

}
