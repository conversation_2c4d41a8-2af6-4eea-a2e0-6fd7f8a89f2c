package com.bobandata.cloud.trade.excel.core.metadata.template;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024-03-11日 15:55
 * @description 模版中存在的变量信息
 */
@Getter
@Setter
@EqualsAndHashCode
@Accessors(chain = true)
@ToString
public class TemplateVar {

    /**
     * 变量所在单元格
     */
    private Integer rowIndex;

    private Integer colIndex;

    /**
     * 存在变量的原始cell值
     */
    private String varCellStr;

    /**
     * 从varCellStr 中提取的变量名
     */
    private String varName;
}
