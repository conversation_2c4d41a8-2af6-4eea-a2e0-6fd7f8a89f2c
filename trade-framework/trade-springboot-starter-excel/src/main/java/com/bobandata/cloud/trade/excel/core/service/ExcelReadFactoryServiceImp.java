package com.bobandata.cloud.trade.excel.core.service;

import com.alibaba.excel.EasyExcel;
import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.excel.config.TradeExcelAutoConfiguration;
import com.bobandata.cloud.trade.excel.config.TradeExcelProperties;
import com.bobandata.cloud.trade.excel.core.SchemaExcelReadListener;
import com.bobandata.cloud.trade.excel.core.TemplateExcelReadListener;
import com.bobandata.cloud.trade.excel.core.extract.TemplateDataExtractor;
import com.bobandata.cloud.trade.excel.core.metadata.TableRowData;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateExcelInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateTableInfo;
import com.bobandata.cloud.trade.excel.core.sink.DataSourceJdbcConnectionProvider;
import com.bobandata.cloud.trade.excel.core.sink.LoggerTableDataSinker;
import com.bobandata.cloud.trade.excel.core.sink.MultiTableDataSinker;
import com.bobandata.cloud.trade.excel.core.sink.TableDataSinker;
import com.bobandata.cloud.trade.excel.core.sink.TableRowDataJdbcStatementBuilder;
import com.bobandata.cloud.trade.excel.core.sink.TableRowDataTransform;
import com.bobandata.cloud.trade.excel.core.sink.TableSchemaInfo;
import com.bobandata.cloud.trade.flink.jdbc.JdbcTableMetaProvider;
import com.bobandata.cloud.trade.flink.jdbc.TableMetaInfo;
import com.bobandata.cloud.web.util.ApplicationContextHolder;
import com.google.common.base.Preconditions;
import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.dialect.mysql.MySqlDialect;
import org.apache.flink.connector.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.connection.SimpleJdbcConnectionProvider;
import org.apache.flink.connector.jdbc.internal.executor.JdbcBatchStatementExecutor;

/**
 * <AUTHOR>
 * @date 2024-03-20日 16:25
 * @description
 */
@Slf4j
public class ExcelReadFactoryServiceImp implements ExcelReadFactoryService {

    public void readExcelAndSinkDb(File templateExcelFile,
                                   String templateSheet,
                                   File dataExcelFile,
                                   String dataSheetName,
                                   Integer datasourceId,
                                   Map<String, List<KeyValue<String, Object>>> otherData) {
        JdbcConnectionProvider jdbcConnectionProvider = this.getJdbcConnectionProvider(datasourceId);
        this.readExcelAndSinkDb(templateExcelFile, templateSheet, dataExcelFile, dataSheetName, datasourceId,
                                jdbcConnectionProvider, otherData
        );
    }

    /**
     * @param templateExcelFile      模版Excel
     * @param templateSheet          表格sheet名
     * @param dataExcelFile          数据Excel
     * @param dataSheetName          数据表格sheet名
     * @param datasourceId           数据源ID
     * @param jdbcConnectionProvider 默认jdbc支持
     * @param otherData              表中其他固定数据
     */
    public void readExcelAndSinkDb(File templateExcelFile,
                                   String templateSheet,
                                   File dataExcelFile,
                                   String dataSheetName,
                                   Integer datasourceId,
                                   JdbcConnectionProvider jdbcConnectionProvider,
                                   Map<String, List<KeyValue<String, Object>>> otherData) {
        Preconditions.checkNotNull(templateSheet, "模版sheet名不能为空");
        Preconditions.checkNotNull(templateExcelFile, "模版Excel不能为空");
        Preconditions.checkNotNull(dataExcelFile, "数据Excel不能为空");
        Preconditions.checkNotNull(dataSheetName, "数据表格sheet名不能为空");
        if (Objects.isNull(otherData)) {
            otherData = Collections.emptyMap();
        }
        TemplateExcelInfo templateExcelInfo = this.readTemplateExcel(templateExcelFile, templateSheet);
        Objects.requireNonNull(templateExcelInfo, "解析模版数据信息为空");
        MultiTableDataSinker tableDataSinker = this.getTableDataSinker(datasourceId, jdbcConnectionProvider,
                                                                       templateExcelInfo, otherData
        );
        TemplateDataExtractor templateDataExtractor = new TemplateDataExtractor(templateExcelInfo);
        SchemaExcelReadListener<TableRowData> schemaExcelReadListener = new SchemaExcelReadListener<>(
                templateDataExtractor, tableDataSinker);
        EasyExcel.read(dataExcelFile, schemaExcelReadListener).headRowNumber(0).sheet(dataSheetName).doRead();
    }

    public TemplateExcelInfo readTemplateExcel(File templateExcelFile, String sheetName) {
        TemplateExcelReadListener templateExcelReadListener = new TemplateExcelReadListener();
        EasyExcel.read(templateExcelFile, templateExcelReadListener).headRowNumber(0).sheet(sheetName).doRead();
        return templateExcelReadListener.getTemplateExcelInfo();
    }

    public MultiTableDataSinker getTableDataSinker(Integer datasourceId,
                                                   JdbcConnectionProvider jdbcConnectionProvider,
                                                   TemplateExcelInfo templateExcelInfo,
                                                   Map<String, List<KeyValue<String, Object>>> otherData) {
        Map<String, TemplateTableInfo> tableInfos = templateExcelInfo.getTableInfos();
        Set<String> tableNames = tableInfos.keySet();
        Map<String, TableMetaInfo> tableMetaInfos = this.loadTableMetaInfo(tableNames, datasourceId,
                                                                           jdbcConnectionProvider
        );
        Map<String, TableSchemaInfo> tableSchemas = this.buildTableSchemaInfo(tableInfos, tableMetaInfos, otherData);
        JdbcConnectionProvider sinkerConnectionProvider = datasourceId == null ? jdbcConnectionProvider : this.getJdbcConnectionProvider(
                datasourceId);
        Function<String, JdbcBatchStatementExecutor<TableRowData>> function = this.createFunction(tableSchemas);
        return new MultiTableDataSinker(sinkerConnectionProvider, function);
    }

    /**
     * 组合每个表的静态信息
     */
    private Map<String, TableSchemaInfo> buildTableSchemaInfo(Map<String, TemplateTableInfo> tableInfos,
                                                              Map<String, TableMetaInfo> tableMetaInfos,
                                                              Map<String, List<KeyValue<String, Object>>> otherData) {
        Map<String, TableSchemaInfo> tableSchemaInfos = new HashMap<>();
        for (final Map.Entry<String, TemplateTableInfo> entry : tableInfos.entrySet()) {
            String key = entry.getKey();
            TemplateTableInfo value = entry.getValue();
            TableMetaInfo tableMetaInfo = tableMetaInfos.get(key);
            List<KeyValue<String, Object>> keyValues = otherData.get(key);
            TableSchemaInfo tableSchemaInfo = new TableSchemaInfo(value, tableMetaInfo, keyValues);
            tableSchemaInfos.put(key, tableSchemaInfo);
        }
        return tableSchemaInfos;
    }

    /**
     * 加载表的元数据信息 字段名 字段类型 字段类型名称等
     */
    private Map<String, TableMetaInfo> loadTableMetaInfo(Set<String> tableName,
                                                         Integer datasourceId,
                                                         JdbcConnectionProvider connectionProvider) {
        if (datasourceId != null) {
            connectionProvider = this.getJdbcConnectionProvider(datasourceId);
        }
        Map<String, TableMetaInfo> tableMetaInfos = new HashMap<>(tableName.size());
        // 和executor里面的jdbc分开
        try {
            JdbcTableMetaProvider jdbcTableMetaProvider = new JdbcTableMetaProvider();
            Connection connection = connectionProvider.getOrEstablishConnection();
            for (final String table : tableName) {
                TableMetaInfo tableMetaInfo = jdbcTableMetaProvider.loadTableMetaInfo(table, connection);
                tableMetaInfos.put(table, tableMetaInfo);
            }
        } catch (SQLException | ClassNotFoundException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            connectionProvider.closeConnection();
        }
        return tableMetaInfos;
    }

    public Function<String, JdbcBatchStatementExecutor<TableRowData>> createFunction(Map<String, TableSchemaInfo> tableSchemas) {
        return tableName -> {
            TableSchemaInfo tableSchemaInfo = tableSchemas.get(tableName);
            String insertSql = tableSchemaInfo.getInsertSql(new MySqlDialect());
            int[] fieldTypes = tableSchemaInfo.getFieldTypes();
            TableRowDataJdbcStatementBuilder jdbcStatementBuilder = new TableRowDataJdbcStatementBuilder(fieldTypes);
            Object[] fixedData = tableSchemaInfo.getFixedData();
            TableRowDataTransform rowDataTransform = new TableRowDataTransform(fixedData);
            return JdbcBatchStatementExecutor.simple(insertSql, jdbcStatementBuilder, rowDataTransform);
        };
    }

    public JdbcConnectionProvider getJdbcConnectionProvider(Integer datasourceId) {
        if (datasourceId == null) {
            return new DataSourceJdbcConnectionProvider();
        }
        TradeExcelAutoConfiguration configuration = ApplicationContextHolder.getBean(TradeExcelAutoConfiguration.class);
        Objects.requireNonNull(configuration, "Excel配置信息未注入");
        TradeExcelProperties.DataSourceConfig dataSourceConfig = configuration.getDataSourceConfig(datasourceId);
        if (dataSourceConfig == null) {
            throw new IllegalArgumentException("数据源ID不存在：" + datasourceId);
        }
        JdbcConnectionOptions connectionOptions = new JdbcConnectionOptions.JdbcConnectionOptionsBuilder().withDriverName(
                                                                                                                  dataSourceConfig.getDriverClassName())
                                                                                                          .withUrl(
                                                                                                                  dataSourceConfig.getUrl())
                                                                                                          .withUsername(
                                                                                                                  dataSourceConfig.getUsername())
                                                                                                          .withPassword(
                                                                                                                  dataSourceConfig.getPassword())
                                                                                                          .build();
        return new SimpleJdbcConnectionProvider(connectionOptions);
    }

    public TableDataSinker<TableRowData> getLoggerTableDataSinker() {
        return new LoggerTableDataSinker<>();
    }

    public void doReadDataExcel(File templateExcelFile,
                                String templateSheetName,
                                File dataExcelFile,
                                String dataSheetName,
                                TableDataSinker<TableRowData> dataTableDataSinker) {
        TemplateExcelInfo templateExcelInfo = this.readTemplateExcel(templateExcelFile, templateSheetName);
        Objects.requireNonNull(templateExcelInfo, "解析模版数据信息为空");
        TemplateDataExtractor templateDataExtractor = new TemplateDataExtractor(templateExcelInfo);
        SchemaExcelReadListener<TableRowData> schemaExcelReadListener = new SchemaExcelReadListener<>(
                templateDataExtractor, dataTableDataSinker);
        EasyExcel.read(dataExcelFile, schemaExcelReadListener).headRowNumber(0).sheet(dataSheetName).doRead();
    }

}
