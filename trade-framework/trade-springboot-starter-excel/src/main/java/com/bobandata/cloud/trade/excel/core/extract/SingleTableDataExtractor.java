package com.bobandata.cloud.trade.excel.core.extract;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import java.util.Collection;
import java.util.Map;
import java.util.Properties;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-03-14日 14:49
 * @description 一对一映射到数据库表
 */
public class SingleTableDataExtractor implements DataExtractor<Row> {

    private static final String RULE_NAME = "single_table";

    // 以当前行的第一列数据为准 数据区间为[start,end]
    public static final String DATA_START_FLAG = "startData";

    public static final String DATA_END_FLAG = "endData";

    private final Properties configProperties;

    private boolean isStart = false;

    private boolean isEnd = false;

    public SingleTableDataExtractor(Properties configProperties) {
        this.configProperties = configProperties;
    }

    @Override
    public String name() {
        return RULE_NAME;
    }

    @Override
    public Row extractData(final ReadRowHolder readRowHolder, final Map<Integer, Object> excelData) {
        if (CollUtil.isEmpty(excelData)) {
            return null;
        }
        Collection<Object> values = excelData.values();
        Object[] array = values.stream().toArray();
        String currentValue = String.valueOf(array[0]);
        String startValue = String.valueOf(configProperties.get(DATA_START_FLAG));
        if (StrUtil.equalsAnyIgnoreCase(currentValue, startValue)) {
            isStart = true;
        }
        if (isStart && !isEnd) {
            Row row = new Row(values.size());
            int index = 0;
            for (final Object value : values) {
                row.setField(index, value);
                index++;
            }
            // 当前行是有效的 不能忽略 等发完之后再结束
            String endValue = String.valueOf(configProperties.get(DATA_END_FLAG));
            if (StrUtil.equalsAnyIgnoreCase(currentValue, endValue)) {
                isEnd = true;
            }
            return row;
        } else {
            return null;
        }
    }

}
