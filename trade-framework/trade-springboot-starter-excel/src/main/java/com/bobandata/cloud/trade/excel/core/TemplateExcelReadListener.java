package com.bobandata.cloud.trade.excel.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.bobandata.cloud.common.utils.json.JsonUtils;
import com.bobandata.cloud.trade.excel.core.constant.TemplateMaskConst;
import com.bobandata.cloud.trade.excel.core.metadata.template.DataCellEntity;
import com.bobandata.cloud.trade.excel.core.metadata.template.HeadCellEntity;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateDataInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateExcelInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateHeaderCell;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateTableInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

import static com.bobandata.cloud.trade.excel.core.constant.TemplateMaskConst.DATA_CONST_PREFIX;
import static com.bobandata.cloud.trade.excel.core.constant.TemplateMaskConst.HEADER_CONST_PREFIX;

/**
 * <AUTHOR>
 * @date 2024-03-16日 13:32
 * @description
 */
@Slf4j
public class TemplateExcelReadListener implements ReadListener<Map<Integer, String>> {

    private final TemplateExcelInfo templateExcelInfo;

    public TemplateExcelReadListener() {
        this.templateExcelInfo = new TemplateExcelInfo();
    }

    public TemplateExcelInfo getTemplateExcelInfo() {
        return templateExcelInfo;
    }

    @Override
    public void invoke(final Map<Integer, String> data, final AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();
        List<TemplateHeaderCell> headerRow = new ArrayList<>();
        for (final Map.Entry<Integer, String> entry : data.entrySet()) {
            Integer colIndex = entry.getKey();
            String stringValue = entry.getValue();
            if (StrUtil.isEmpty(stringValue)) {
                continue;
            }
            // 移除字符串匹配不到的字符
            stringValue = StrUtil.removeAny(stringValue, "\n", "\r");
            if (StrUtil.startWith(stringValue, HEADER_CONST_PREFIX)) {
                TemplateHeaderCell headerCell = this.parseHeaderCell(stringValue, rowIndex, colIndex);
                headerCell.setOrgStr(stringValue);
                headerRow.add(headerCell);
            } else if (StrUtil.startWith(stringValue, DATA_CONST_PREFIX)) {
                TemplateDataInfo templateDataInfo = this.parseDataCell(stringValue);
                this.templateExcelInfo.putDataInfo(templateDataInfo.getTableName(), templateDataInfo);
            } else {
                log.info("无效单元格 {}", stringValue);
            }
        }
        if (CollUtil.isEmpty(headerRow)) {
            log.info("当前行未读取到表头信息 {}", rowIndex);
            return;
        }
        Set<String> tableNames = headerRow.stream().map(TemplateHeaderCell::getTableName).collect(Collectors.toSet());
        if (tableNames.size() != 1) {
            throw new ExcelAnalysisException("解析Excel模版表头出错: 获取数据库表信息不为一");
        }
        String tableName = CollUtil.get(tableNames, 0);
        templateExcelInfo.putTableHeadCell(tableName, rowIndex, headerRow);
    }

    @Override
    public void doAfterAllAnalysed(final AnalysisContext context) {
        Map<String, TemplateTableInfo> tableInfos = templateExcelInfo.getTableInfos();
        if (CollUtil.isEmpty(tableInfos)) {
            log.warn("解析模版数据为空");
        }

    }

    private TemplateDataInfo parseDataCell(String dataStr) {
        Pattern pattern = TemplateMaskConst.DATA_PATTERN;
        Matcher matcher = pattern.matcher(dataStr);
        boolean find = matcher.find();
        if (find) {
            String expression = matcher.group(1);
            return this.buildDataCell(expression);
        }
        throw new ExcelAnalysisException("解析Excel模版数据cell为空: 正则表达式未匹配上");
    }

    private TemplateDataInfo buildDataCell(String expression) {
        DataCellEntity dataCellEntity = JsonUtils.parseObject(expression, DataCellEntity.class);
        Objects.requireNonNull(dataCellEntity, "解析Excel模版数据信息为空");
        return new TemplateDataInfo().setTableName(dataCellEntity.getTableName())
                                     .setDataExpression(expression)
                                     .setDataStartRowIndex(dataCellEntity.getStart())
                                     .setDataEndRowIndex(dataCellEntity.getEnd());
    }

    private TemplateHeaderCell parseHeaderCell(String headerStr, Integer rowIndex, Integer colIndex) {
        Pattern pattern = TemplateMaskConst.HEADER_PATTERN;
        Matcher matcher = pattern.matcher(headerStr);
        boolean find = matcher.find();
        if (find) {
            String expression = matcher.group(1);
            return this.buildHeaderCell(expression, rowIndex, colIndex);
        }
        throw new ExcelAnalysisException("解析Excel模版表头为空: 正则表达式未匹配上");
    }

    private TemplateHeaderCell buildHeaderCell(String expression, Integer rowIndex, Integer colIndex) {
        HeadCellEntity headCellEntity = JsonUtils.parseObject(expression, HeadCellEntity.class);
        Objects.requireNonNull(headCellEntity, "解析Excel模版表头为空");
        return new TemplateHeaderCell().setField(headCellEntity.getCol())
                                       .setNeed(headCellEntity.isNeed())
                                       .setExpression(expression)
                                       .setRowIndex(rowIndex)
                                       .setTableName(headCellEntity.getTable())
                                       .setColIndex(colIndex);
    }
}
