package com.bobandata.cloud.trade.excel.core.sink;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import org.apache.flink.connector.jdbc.JdbcStatementBuilder;
import org.apache.flink.connector.jdbc.utils.JdbcUtils;
import org.apache.flink.types.Row;

/**
 * <AUTHOR>
 * @date 2024-03-15日 14:24
 * @description
 */
public class TableRowDataJdbcStatementBuilder implements JdbcStatementBuilder<Row> {

    private final int[] fieldTypes;

    public TableRowDataJdbcStatementBuilder(int[] fieldTypes) {
        this.fieldTypes = fieldTypes;
    }

    @Override
    public void accept(final PreparedStatement preparedStatement, final Row rowData) throws SQLException {
        // todo 后续需要考虑字符传类型转换
        JdbcUtils.setRecordToStatement(preparedStatement, null, rowData);
    }
}
