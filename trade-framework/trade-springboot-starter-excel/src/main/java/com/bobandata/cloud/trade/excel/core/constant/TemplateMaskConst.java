package com.bobandata.cloud.trade.excel.core.constant;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-03-11日 16:14
 * @description
 */
public final class TemplateMaskConst {

    /**
     * 变量信息正则
     */
    public static final String VAR_PATTERN_STR = "#\\{(.*?)}";

    public static final Pattern VAR_PATTERN = Pattern.compile(VAR_PATTERN_STR);

    public static final String DATA_CONST_PREFIX = "d-";

    public static final String DATA_CONST = "d-(\\{(.*?)})";

    public static final Pattern DATA_PATTERN = Pattern.compile(DATA_CONST);

    public static final String HEADER_CONST_PREFIX = "h-";

    /**
     * 表头正则
     */
    public static final String HEADER_CONST = "h-(\\{(.*?)})";

    public static final Pattern HEADER_PATTERN = Pattern.compile(HEADER_CONST);
}
