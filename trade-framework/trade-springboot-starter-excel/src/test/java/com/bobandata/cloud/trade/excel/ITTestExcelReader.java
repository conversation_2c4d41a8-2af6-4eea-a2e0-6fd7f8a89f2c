package com.bobandata.cloud.trade.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.bobandata.cloud.common.core.KeyValue;
import com.bobandata.cloud.trade.excel.core.SchemaExcelReadListener;
import com.bobandata.cloud.trade.excel.core.TemplateExcelReadListener;
import com.bobandata.cloud.trade.excel.core.constant.TemplateMaskConst;
import com.bobandata.cloud.trade.excel.core.extract.SingleTableDataExtractor;
import com.bobandata.cloud.trade.excel.core.extract.TemplateDataExtractor;
import com.bobandata.cloud.trade.excel.core.metadata.TableRowData;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateDataInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateExcelInfo;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateHeaderCell;
import com.bobandata.cloud.trade.excel.core.metadata.template.TemplateTableInfo;
import com.bobandata.cloud.trade.excel.core.service.ExcelReadFactoryServiceImp;
import com.bobandata.cloud.trade.excel.core.sink.LoggerTableDataSinker;
import com.mysql.jdbc.Driver;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.internal.connection.SimpleJdbcConnectionProvider;
import org.apache.flink.types.Row;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024-03-11日 14:21
 * @description
 */

public class ITTestExcelReader {

    @Test
    public void testExcelReaderSinker() {
        ExcelReadFactoryServiceImp factoryServiceImp = new ExcelReadFactoryServiceImp();
        File templateExcel = new File("src/test/resources/exceltemplate.xlsx");
        File dataExcel = new File("src/test/resources/湖南新华供电有限公司2023-11之零售用户明细分时excel.xlsx");
        String sheetName = "sheet1";
        JdbcConnectionOptions connectionOptions = new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                .withUrl(
                        "**********************************************************************************************************************")
                .withUsername("root")
                .withPassword("root")
                .withDriverName(Driver.class.getName())
                .build();
        SimpleJdbcConnectionProvider provider = new SimpleJdbcConnectionProvider(connectionOptions);
        KeyValue<String, Object> keyValue1 = new KeyValue<String, Object>()
                .setKey("date_year")
                .setValue(2023);
        KeyValue<String, Object> keyValue2 = new KeyValue<String, Object>()
                .setKey("date_month")
                .setValue(11);
        List<KeyValue<String, Object>> keyValues = Arrays.asList(keyValue1, keyValue2);
        Map<String, List<KeyValue<String, Object>>> otherData = new HashMap<String, List<KeyValue<String, Object>>>();
        otherData.put("ml_settle_retail_detail", keyValues);
        factoryServiceImp.readExcelAndSinkDb(templateExcel, sheetName, dataExcel, sheetName, null, provider, otherData);
    }

    @Test
    public void testExcelReader() {
        ExcelReadFactoryServiceImp factoryServiceImp = new ExcelReadFactoryServiceImp();
        File templateExcel = new File("src/test/resources/exceltemplate.xlsx");
        File dataExcel = new File("src/test/resources/湖南新华供电有限公司2023-11之零售用户明细分时excel.xlsx");
        String sheetName = "sheet1";
        factoryServiceImp.doReadDataExcel(templateExcel, sheetName, dataExcel, sheetName,
                                          new LoggerTableDataSinker<>()
        );
    }

    @Test
    public void testTemplateReader() {
        TemplateExcelReadListener templateExcelReadListener = new TemplateExcelReadListener();
        File file = new File("src/test/resources/exceltemplate.xlsx");
        EasyExcel.read(file, templateExcelReadListener).headRowNumber(0).sheet("sheet1").doRead();
    }

    @Test
    public void testTemplateExtract() {
        TemplateExcelInfo templateExcelInfo = new TemplateExcelInfo();
        templateExcelInfo.setTemplateName("test-template");
        TemplateTableInfo templateTableInfo = new TemplateTableInfo();
        TemplateDataInfo templateDataInfo = new TemplateDataInfo();
        List<TemplateHeaderCell> headerCells = new ArrayList<>();
        templateTableInfo.setTableName("")
                         .setHeadRowIndex(1)
                         .setHeaderRow(headerCells)
                         .setTemplateDataInfo(templateDataInfo);
        templateExcelInfo.setTableInfos(null);
        TemplateDataExtractor tableDataExtractor = new TemplateDataExtractor(templateExcelInfo);
        LoggerTableDataSinker<TableRowData> tableDataSinker = new LoggerTableDataSinker<TableRowData>();
        SchemaExcelReadListener<TableRowData> schemaExcelReadListener = new SchemaExcelReadListener<TableRowData>(
                tableDataExtractor,
                tableDataSinker
        );
        File file = new File("src/test/resources/湖南新华供电有限公司2023-11之零售用户明细分时excel.xlsx");
        EasyExcel.read(file, schemaExcelReadListener).headRowNumber(0).sheet("sheet1").doRead();
    }

    @Test
    public void testHeaderPattern() {
        Pattern compile = Pattern.compile(TemplateMaskConst.HEADER_CONST);
        String str = "h-{\n" +
                "  \"table\": \"ml_settle_retail_detail\",\n" +
                "  \"col\": \"order\",\n" +
                "  \"need\": false\n" +
                "}";
        str = StrUtil.removeAny(str, "\n", "\r");
        Matcher matcher = compile.matcher(str);
        while (matcher.find()) {
            String group = matcher.group();
            System.out.println("group = " + group);
        }
    }

    @Test
    public void testPattern() {
        Pattern pattern = TemplateMaskConst.VAR_PATTERN;
        String str = "aasdfasdf#{a_b_c_01}adsfasdf";
        Matcher matcher = pattern.matcher(str);
        while (matcher.find()) {
            String group = matcher.group(1);
            System.out.println("group = " + group);
        }
    }

    @Test
    public void testReader() {
        Properties properties = new Properties();
        properties.put(SingleTableDataExtractor.DATA_START_FLAG, "1");
        properties.put(SingleTableDataExtractor.DATA_END_FLAG, "35");
        LoggerTableDataSinker<Row> tableDataSinker = new LoggerTableDataSinker<>();
        SingleTableDataExtractor tableDataExtractor = new SingleTableDataExtractor(properties);
        SchemaExcelReadListener<Row> schemaExcelReadListener = new SchemaExcelReadListener<Row>(
                tableDataExtractor,
                tableDataSinker
        );
        File file = new File("src/test/resources/湖南新华供电有限公司2023-11之零售用户明细分时excel.xlsx");
        EasyExcel.read(file, schemaExcelReadListener).headRowNumber(0).sheet("sheet1").doRead();
    }
}
