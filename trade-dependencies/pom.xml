<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.bobandata.cloud</groupId>
    <version>${base.version}</version>
    <artifactId>trade-dependencies</artifactId>

    <name>trade : manager :: dependencies</name>

    <repositories>
        <repository>
            <id>boban-central</id>
            <name>boban-central</name>
            <url>http://${boban.host}/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

    <properties>
        <boban.host>*************:8081</boban.host>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <base.version>1.0-SNAPSHOT</base.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>2.7.11</spring.boot.version>
        <spring-cloud-commons.version>3.1.3</spring-cloud-commons.version>
        <spring-cloud-netflix.version>3.1.3</spring-cloud-netflix.version>
        <spring-cloud-openfeign.version>3.1.3</spring-cloud-openfeign.version>
        <spring-cloud-bus.version>3.1.2</spring-cloud-bus.version>
        <spring-cloud-gateway.version>3.1.3</spring-cloud-gateway.version>
        <!-- Web 相关 -->
        <xxl.job.version>2.3.1</xxl.job.version>
        <swagger.version>2.2.8</swagger.version>
        <springdoc.version>1.6.15</springdoc.version>
        <knife4j.version>4.3.0</knife4j.version>
        <servlet.versoin>2.5</servlet.versoin>
        <spring.cloud.version>2021.0.5</spring.cloud.version>
        <spring.cloud.alibaba.version>2021.0.4.0</spring.cloud.alibaba.version>
        <!-- DB 相关 -->
        <flink.version>1.16.2</flink.version>
        <flywaydb.versoin>9.10.2</flywaydb.versoin>
        <mysql.version>8.0.13</mysql.version>
        <druid.version>1.2.16</druid.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.3.1</mybatis-plus-generator.version>
        <mybatis-flex.version>1.7.0</mybatis-flex.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <mybatis-plus-join-boot-starter.version>1.4.9</mybatis-plus-join-boot-starter.version>
        <redisson.version>3.18.0</redisson.version>
        <dm8.jdbc.version>8.1.2.141</dm8.jdbc.version>
        <lock4j.version>2.2.3</lock4j.version>
        <!-- Test 测试相关 -->
        <podam.version>7.2.11.RELEASE</podam.version>
        <jedis-mock.version>1.0.7</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!--        工作流-->
        <liteflow.version>2.10.3</liteflow.version>
        <qlexpress.version>3.3.2</qlexpress.version>
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <commons-codec.version>1.6</commons-codec.version>
        <commons-io.version>2.7</commons-io.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <captcha-plus.version>1.0.2</captcha-plus.version>
        <jsoup.version>1.15.4</jsoup.version>
        <lombok.version>1.18.26</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.18</hutool.version>
        <easyexcel.verion>3.3.1</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>31.1-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-net.version>3.8.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.7.0</tika-core.version>
        <netty-all.version>4.1.90.Final</netty-all.version>
        <ip2region.version>2.7.0</ip2region.version>
        <okio.version>3.0.0</okio.version>
        <okhttp3.version>4.10.0</okhttp3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <minio.version>8.5.2</minio.version>
        <jimureport.version>1.6.4</jimureport.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <!-- 用于生成自定义的 Spring @ConfigurationProperties 配置类的说明文件 -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flywaydb.versoin}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>${qlexpress.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId> <!-- 多数据源 -->
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-api-java-bridge</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-jdbc</artifactId>
                <version>${flink.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-common</artifactId>
                <version>${flink.version}</version>
            </dependency>


            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-processor</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <!-- 工作流相关 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- idm依赖提供身份认证 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-idm</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- modeler绘制流程图 -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-modeler</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-actuator</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!-- 工作流相关结束 -->

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version> <!-- 支持 Mockito 的 final 类与 static 方法的 mock -->
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
                <artifactId>jedis-mock</artifactId>
                <version>${jedis-mock.version}</version>
            </dependency>

            <dependency>
                <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
                <artifactId>podam</artifactId>
                <version>${podam.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId> <!-- 接口文档：使用最新版本的 Swagger 模型 -->
                <artifactId>swagger-models</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-common</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webmvc-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：解决 knife4j 引入的 Spring Doc 版本太老 -->
                <artifactId>springdoc-openapi-webflux-core</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j -->
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 UI：knife4j【网关专属】 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-core</artifactId> <!-- 实现数据库文档 -->
                <version>${screw.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.freemarker</groupId>
                        <artifactId>freemarker</artifactId> <!-- 移除 Freemarker 依赖，采用 Velocity 作为模板引擎 -->
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId> <!-- 最新版screw-core1.0.5依赖fastjson1.2.73存在漏洞，移除。 -->
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>${okio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jeecgframework.jimureport</groupId>
                <artifactId>jimureport-spring-boot-starter</artifactId>
                <version>${jimureport.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>


            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-framework-common</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-web</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-curve</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-flink</artifactId>
                <version>${base.version}</version>
            </dependency>


            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-mybatisplus</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-data-permission</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-excel</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-security</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-operatelog</artifactId>
                <version>${base.version}</version>
            </dependency>


            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-expression</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-codes</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-variable</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-manager-system-api</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-flowable</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bobandata.cloud</groupId>
                <artifactId>trade-springboot-starter-mvc</artifactId>
                <version>${base.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>